#!/usr/bin/env python3
"""
Validation script for external embeddings implementation.

This script validates that all modifications are syntactically correct
and that the key components are properly implemented.
"""

import ast
import sys
from pathlib import Path


def check_syntax(file_path: str) -> bool:
    """Check if a Python file has valid syntax."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        return True
    except SyntaxError as e:
        print(f"❌ Syntax Error in {file_path}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False


def check_class_exists(file_path: str, class_name: str) -> bool:
    """Check if a class exists in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        tree = ast.parse(source)
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                return True
        return False
    except Exception:
        return False


def check_method_exists(file_path: str, class_name: str, method_name: str) -> bool:
    """Check if a method exists in a class."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        tree = ast.parse(source)
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                for item in node.body:
                    if isinstance(item, ast.FunctionDef) and item.name == method_name:
                        return True
        return False
    except Exception:
        return False


def check_function_signature(file_path: str, class_name: str, method_name: str, expected_args: list) -> bool:
    """Check if a method has the expected arguments."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        tree = ast.parse(source)
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef) and node.name == class_name:
                for item in node.body:
                    if isinstance(item, ast.FunctionDef) and item.name == method_name:
                        arg_names = [arg.arg for arg in item.args.args]
                        return all(arg in arg_names for arg in expected_args)
        return False
    except Exception:
        return False


def check_string_in_file(file_path: str, search_string: str) -> bool:
    """Check if a string exists in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return search_string in content
    except Exception:
        return False


def validate_implementation():
    """Validate the external embeddings implementation."""
    print("🔍 Validating External Embeddings Implementation\n")
    
    all_passed = True
    
    # Files to check
    files_to_check = [
        "base_ml/base_loss.py",
        "models/segmentation/cell_segmentation/cellvit.py", 
        "cell_segmentation/datasets/pannuke.py",
        "cell_segmentation/trainer/trainer_cellvit.py"
    ]
    
    # 1. Check syntax of all modified files
    print("1. Checking syntax of modified files...")
    for file_path in files_to_check:
        if Path(file_path).exists():
            if check_syntax(file_path):
                print(f"   ✅ {file_path}")
            else:
                all_passed = False
        else:
            print(f"   ❌ {file_path} - File not found")
            all_passed = False
    
    print()
    
    # 2. Check EmbeddingAlignmentLoss implementation
    print("2. Checking EmbeddingAlignmentLoss implementation...")
    loss_file = "base_ml/base_loss.py"
    
    if check_class_exists(loss_file, "EmbeddingAlignmentLoss"):
        print("   ✅ EmbeddingAlignmentLoss class exists")
        
        if check_method_exists(loss_file, "EmbeddingAlignmentLoss", "__init__"):
            print("   ✅ __init__ method exists")
        else:
            print("   ❌ __init__ method missing")
            all_passed = False
            
        if check_method_exists(loss_file, "EmbeddingAlignmentLoss", "forward"):
            print("   ✅ forward method exists")
        else:
            print("   ❌ forward method missing")
            all_passed = False
            
        if check_string_in_file(loss_file, '"EmbeddingAlignmentLoss": EmbeddingAlignmentLoss'):
            print("   ✅ Added to LOSS_DICT")
        else:
            print("   ❌ Not added to LOSS_DICT")
            all_passed = False
    else:
        print("   ❌ EmbeddingAlignmentLoss class not found")
        all_passed = False
    
    print()
    
    # 3. Check CellViT modifications
    print("3. Checking CellViT model modifications...")
    cellvit_file = "models/segmentation/cell_segmentation/cellvit.py"
    
    if check_function_signature(cellvit_file, "CellViT", "__init__", 
                               ["external_embedding_dim", "use_external_embeddings"]):
        print("   ✅ __init__ method has external embedding parameters")
    else:
        print("   ❌ __init__ method missing external embedding parameters")
        all_passed = False
    
    if check_function_signature(cellvit_file, "CellViT", "forward", ["external_embeddings"]):
        print("   ✅ forward method accepts external_embeddings parameter")
    else:
        print("   ❌ forward method missing external_embeddings parameter")
        all_passed = False
    
    if check_string_in_file(cellvit_file, "external_embedding_projector"):
        print("   ✅ External embedding projector implemented")
    else:
        print("   ❌ External embedding projector missing")
        all_passed = False
    
    if check_string_in_file(cellvit_file, "embedding_fusion"):
        print("   ✅ Embedding fusion mechanism implemented")
    else:
        print("   ❌ Embedding fusion mechanism missing")
        all_passed = False
    
    print()
    
    # 4. Check PanNukeDataset modifications
    print("4. Checking PanNukeDataset modifications...")
    dataset_file = "cell_segmentation/datasets/pannuke.py"
    
    if check_function_signature(dataset_file, "PanNukeDataset", "__init__", 
                               ["external_embeddings_path", "embedding_dim"]):
        print("   ✅ __init__ method has external embedding parameters")
    else:
        print("   ❌ __init__ method missing external embedding parameters")
        all_passed = False
    
    if check_method_exists(dataset_file, "PanNukeDataset", "load_external_embeddings"):
        print("   ✅ load_external_embeddings method exists")
    else:
        print("   ❌ load_external_embeddings method missing")
        all_passed = False
    
    if check_method_exists(dataset_file, "PanNukeDataset", "get_external_embedding"):
        print("   ✅ get_external_embedding method exists")
    else:
        print("   ❌ get_external_embedding method missing")
        all_passed = False
    
    if check_string_in_file(dataset_file, "Tuple[torch.Tensor, dict, str, str, torch.Tensor]"):
        print("   ✅ __getitem__ returns external embedding")
    else:
        print("   ❌ __getitem__ not modified to return external embedding")
        all_passed = False
    
    print()
    
    # 5. Check trainer modifications
    print("5. Checking trainer modifications...")
    trainer_file = "cell_segmentation/trainer/trainer_cellvit.py"
    
    if check_string_in_file(trainer_file, "batch[4]"):
        print("   ✅ Trainer handles external embeddings from batch[4]")
    else:
        print("   ❌ Trainer not modified to handle external embeddings")
        all_passed = False
    
    if check_string_in_file(trainer_file, "external_embeddings=external_embeddings"):
        print("   ✅ Model forward calls include external embeddings")
    else:
        print("   ❌ Model forward calls not updated")
        all_passed = False
    
    print()
    
    # 6. Check configuration and documentation files
    print("6. Checking configuration and documentation...")
    
    config_files = [
        "configs/examples/cell_segmentation/train_cellvit_with_external_embeddings.yaml",
        "examples/train_cellvit_with_external_embeddings.py",
        "docs/external_embeddings_guide.md",
        "tests/test_external_embeddings.py",
        "EXTERNAL_EMBEDDINGS_IMPLEMENTATION.md"
    ]
    
    for file_path in config_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - File not found")
            all_passed = False
    
    print()
    
    # Final result
    if all_passed:
        print("🎉 All validation checks passed!")
        print("\n✅ External embeddings implementation is complete and ready to use.")
        print("\nNext steps:")
        print("1. Install required dependencies (torch, numpy, etc.)")
        print("2. Prepare your external embeddings file")
        print("3. Use the example configuration and script to start training")
        print("4. Run the test suite: python tests/test_external_embeddings.py")
    else:
        print("❌ Some validation checks failed.")
        print("Please review the errors above and fix the issues.")
    
    return all_passed


if __name__ == "__main__":
    success = validate_implementation()
    sys.exit(0 if success else 1)
