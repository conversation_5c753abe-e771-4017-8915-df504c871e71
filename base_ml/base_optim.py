# -*- coding: utf-8 -*-
# Wrappping all available PyTorch Optimizer
#
# @ <PERSON>, <EMAIL>
# Institute for Artifical Intelligence in Medicine,
# University Medicine Essen

from torch.optim import (
    ASGD,
    LBFGS,
    SGD,
    Adadelta,
    Adagrad,
    Adam,
    Adamax,
    AdamW,
    RAdam,
    RMSprop,
    Rprop,
    SparseAdam,
)

OPTI_DICT = {
    "Adadel<PERSON>": <PERSON><PERSON><PERSON>,
    "Adagrad": <PERSON><PERSON>,
    "<PERSON>": <PERSON>,
    "<PERSON><PERSON>": <PERSON><PERSON>,
    "SparseAdam": Sparse<PERSON>dam,
    "Adamax": <PERSON><PERSON>,
    "ASGD": ASGD,
    "LBFGS": LBFGS,
    "RAdam": <PERSON>dam,
    "RMSprop": RMSprop,
    "Rprop": Rprop,
    "SGD": SGD,
}
