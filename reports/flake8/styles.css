html {
   font-family: sans-serif;
   font-size: 90%;
}

#masthead {
   position: fixed;
   left: 0;
   top: 0;
   right: 0;
   height: 40%;
}

h1, h2 {
   font-family: sans-serif;
   font-weight: normal;
}

h1 {
   color: white;
   font-size: 36px;
   margin-top: 1em;
}

h1 img {
   margin-right: 0.3em;
}

h2 {
   margin-top: 0;
}

h1 a {
   color: white;
}

#versions {
   color: rgba(255, 255, 255, 0.7);
}

#page {
   position: relative;
   max-width: 960px;
   margin: 0 auto;
}

#index {
   background-color: white;
   box-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
   padding: 0;
   margin: 0;
}

#index li {
   list-style: none;
   margin: 0;
   padding: 1px 0;
}

#index li + li {
   border-top: solid silver 1px;
}

.details p {
   margin-left: 3em;
   color: #888;
}

#index a {
   display: block;
   padding: 0.8em 1em;
   cursor: pointer;
}

#index #all-good {
   padding: 1.4em 1em 0.8em;
}

#all-good .count .tick {
   font-size: 2em;
}

#all-good .count {
   float: left;
}

#all-good h2,
#all-good p {
   margin-left: 50px;
}

#index a:hover {
   background-color: #eee;
}

.count {
   display: inline-block;
   border-radius: 50%;
   text-align: center;
   width: 2.5em;
   line-height: 2.5em;
   height: 2.5em;
   color: white;
   margin-right: 1em;
}

.sev-1 {
   background-color: #a00;
}
.sev-2 {
   background-color: #b80;
}
.sev-3 {
   background-color: #28c;
}
.sev-4 {
   background-color: #383;
}

a {
   text-decoration: none;
}

#doc {
   background-color: white;
   margin: 1em 0;
   padding: 1em;
   padding-left: 1.2em;
   position: relative;
   box-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
}

#doc pre {
   margin: 0;
   padding: 0.07em;
}

.violations {
   position: absolute;
   margin: 1.2em 0 0 3em;
   padding: 0.5em 1em;
   font-size: 14px;
   background-color: white;
   box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
   display: none;
}

.violations .count {
   font-size: 70%;
}

.violations li {
   padding: 0.1em 0.3em;
   list-style: none;
}

.line-violations::before {
   display: block;
   content: "";
   position: absolute;
   left: -1em;
   width: 14px;
   height: 14px;
   border-radius: 50%;
   background-color: red;
}

.code:hover .violations {
   display: block;
}

tt {
   white-space: pre-wrap;
   font-family: Consolas, monospace;
   font-size: 10pt;
}

tt i {
   color: silver;
   display: inline-block;
   text-align: right;
   width: 3em;
   box-sizing: border-box;
   height: 100%;
   border-right: solid #eee 1px;
   padding-right: 0.2em;
}

.le {
   background-color: #ffe8e8;
   cursor: pointer;
}

.le:hover {
   background-color: #fcc;
}

.details {
   clear: both;
}

#index .details {
   border-top-style: none;
   margin: 1em;
}

ul.details {
   margin-left: 0;
   padding-left: 0;
}

#index .details li {
   list-style: none;
   border-top-style: none;
   margin: 0.3em 0;
   padding: 0;
}

#srclink {
   float: right;
   font-size: 36px;
   margin: 0;
}

#srclink a {
   color: white;
}

#index .details a {
   padding: 0;
   color: inherit;
}

.le {
   background-color: #ffe8e8;
   cursor: pointer;
}

.le.sev-1 {
   background-color: #f88;
}
.le.sev-2 {
   background-color: #fda;
}
.le.sev-3 {
   background-color: #adf;
}

img {
   height: 1.2em;
   vertical-align: -0.35em;
}

pre { line-height: 125%; }
td.linenos .normal { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
span.linenos { color: inherit; background-color: transparent; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #000000; background-color: #ffffc0; padding-left: 5px; padding-right: 5px; }
.hll { background-color: #ffffcc }
.c { color: #3D7B7B; font-style: italic } /* Comment */
.err { border: 1px solid #FF0000 } /* Error */
.k { color: #008000; font-weight: bold } /* Keyword */
.o { color: #666666 } /* Operator */
.ch { color: #3D7B7B; font-style: italic } /* Comment.Hashbang */
.cm { color: #3D7B7B; font-style: italic } /* Comment.Multiline */
.cp { color: #9C6500 } /* Comment.Preproc */
.cpf { color: #3D7B7B; font-style: italic } /* Comment.PreprocFile */
.c1 { color: #3D7B7B; font-style: italic } /* Comment.Single */
.cs { color: #3D7B7B; font-style: italic } /* Comment.Special */
.gd { color: #A00000 } /* Generic.Deleted */
.ge { font-style: italic } /* Generic.Emph */
.ges { font-weight: bold; font-style: italic } /* Generic.EmphStrong */
.gr { color: #E40000 } /* Generic.Error */
.gh { color: #000080; font-weight: bold } /* Generic.Heading */
.gi { color: #008400 } /* Generic.Inserted */
.go { color: #717171 } /* Generic.Output */
.gp { color: #000080; font-weight: bold } /* Generic.Prompt */
.gs { font-weight: bold } /* Generic.Strong */
.gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.gt { color: #0044DD } /* Generic.Traceback */
.kc { color: #008000; font-weight: bold } /* Keyword.Constant */
.kd { color: #008000; font-weight: bold } /* Keyword.Declaration */
.kn { color: #008000; font-weight: bold } /* Keyword.Namespace */
.kp { color: #008000 } /* Keyword.Pseudo */
.kr { color: #008000; font-weight: bold } /* Keyword.Reserved */
.kt { color: #B00040 } /* Keyword.Type */
.m { color: #666666 } /* Literal.Number */
.s { color: #BA2121 } /* Literal.String */
.na { color: #687822 } /* Name.Attribute */
.nb { color: #008000 } /* Name.Builtin */
.nc { color: #0000FF; font-weight: bold } /* Name.Class */
.no { color: #880000 } /* Name.Constant */
.nd { color: #AA22FF } /* Name.Decorator */
.ni { color: #717171; font-weight: bold } /* Name.Entity */
.ne { color: #CB3F38; font-weight: bold } /* Name.Exception */
.nf { color: #0000FF } /* Name.Function */
.nl { color: #767600 } /* Name.Label */
.nn { color: #0000FF; font-weight: bold } /* Name.Namespace */
.nt { color: #008000; font-weight: bold } /* Name.Tag */
.nv { color: #19177C } /* Name.Variable */
.ow { color: #AA22FF; font-weight: bold } /* Operator.Word */
.w { color: #bbbbbb } /* Text.Whitespace */
.mb { color: #666666 } /* Literal.Number.Bin */
.mf { color: #666666 } /* Literal.Number.Float */
.mh { color: #666666 } /* Literal.Number.Hex */
.mi { color: #666666 } /* Literal.Number.Integer */
.mo { color: #666666 } /* Literal.Number.Oct */
.sa { color: #BA2121 } /* Literal.String.Affix */
.sb { color: #BA2121 } /* Literal.String.Backtick */
.sc { color: #BA2121 } /* Literal.String.Char */
.dl { color: #BA2121 } /* Literal.String.Delimiter */
.sd { color: #BA2121; font-style: italic } /* Literal.String.Doc */
.s2 { color: #BA2121 } /* Literal.String.Double */
.se { color: #AA5D1F; font-weight: bold } /* Literal.String.Escape */
.sh { color: #BA2121 } /* Literal.String.Heredoc */
.si { color: #A45A77; font-weight: bold } /* Literal.String.Interpol */
.sx { color: #008000 } /* Literal.String.Other */
.sr { color: #A45A77 } /* Literal.String.Regex */
.s1 { color: #BA2121 } /* Literal.String.Single */
.ss { color: #19177C } /* Literal.String.Symbol */
.bp { color: #008000 } /* Name.Builtin.Pseudo */
.fm { color: #0000FF } /* Name.Function.Magic */
.vc { color: #19177C } /* Name.Variable.Class */
.vg { color: #19177C } /* Name.Variable.Global */
.vi { color: #19177C } /* Name.Variable.Instance */
.vm { color: #19177C } /* Name.Variable.Magic */
.il { color: #666666 } /* Literal.Number.Integer.Long */
