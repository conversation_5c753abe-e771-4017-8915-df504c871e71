<!--

* Use this issue template for suggesting new docs or updates to existing docs.
  Note: Doc work as part of feature development is covered in the Feature Request template.

* For issues related to features of the docs.gitlab.com site, see
     https://gitlab.com/gitlab-org/gitlab-docs/issues/

* For information about documentation content and process, see
     https://docs.gitlab.com/ee/development/documentation/ -->

### Problem to solve

<!-- Include the following detail as necessary:
* What product or feature(s) affected?
* What docs or doc section affected? Include links or paths.
* Is there a problem with a specific document, or a feature/process that's not addressed sufficiently in docs?
* Any other ideas or requests?
-->

### Further details

<!--
* Any concepts, procedures, reference info we could add to make it easier to successfully use GitLab?
* Include use cases, benefits, and/or goals for this work.
* If adding content: What audience is it intended for? (What roles and scenarios?)
  For ideas, see personas at https://about.gitlab.com/handbook/product/personas/ or the persona labels at
  https://gitlab.com/groups/gitlab-org/-/labels?subscribed=&search=persona%3A
-->

### Proposal

<!-- Further specifics for how can we solve the problem. -->

/label ~documentation
