# External Embeddings Implementation for CellViT

## Overview

This document summarizes the implementation of external embeddings support for the CellViT model, allowing integration of transcription information (e.g., gene expression data) with histological image analysis.

## Key Features Implemented

### 1. Multi-modal Data Integration
- **Visual Features**: Extracted from histological images using Vision Transformer backbone
- **Transcription Features**: External embeddings from molecular data (RNA-seq, gene expression, etc.)
- **Fusion Mechanism**: Attention-based fusion of visual and external embeddings

### 2. Flexible Architecture
- **Backward Compatibility**: Existing models work without modification
- **Optional Integration**: External embeddings can be enabled/disabled via configuration
- **Multiple File Formats**: Support for .npy, .npz, .pt, .pth embedding files

## Implementation Details

### Modified Files

#### 1. Dataset Layer (`cell_segmentation/datasets/pannuke.py`)
**Changes:**
- Added `external_embeddings_path` and `embedding_dim` parameters to `__init__`
- Modified `__getitem__` to return 5-tuple: `(image, masks, tissue_type, image_name, external_embedding)`
- Added `load_external_embeddings()` method for loading embeddings from various file formats
- Added `get_external_embedding()` method for retrieving embeddings by index
- Graceful handling of missing embeddings with zero padding

**Key Methods:**
```python
def load_external_embeddings(self):
    """Load external embeddings from .npy, .npz, .pt, .pth files"""

def get_external_embedding(self, index: int) -> torch.Tensor:
    """Get external embedding for specific image, returns zero if missing"""
```

#### 2. Model Layer (`models/segmentation/cell_segmentation/cellvit.py`)
**Changes:**
- Added `external_embedding_dim` and `use_external_embeddings` parameters
- Added embedding projection and fusion components:
  - `external_embedding_projector`: Projects external embeddings to match encoder dimension
  - `embedding_fusion`: Multi-head attention for fusing visual and external features
  - `fusion_norm`: Layer normalization for fusion output
- Modified `forward()` method to accept `external_embeddings` parameter
- Implemented attention-based fusion mechanism

**Key Components:**
```python
# Embedding fusion components
self.external_embedding_projector = nn.Sequential(
    nn.Linear(self.external_embedding_dim, self.embed_dim),
    nn.ReLU(),
    nn.Dropout(self.drop_rate),
    nn.Linear(self.embed_dim, self.embed_dim)
)

self.embedding_fusion = nn.MultiheadAttention(
    embed_dim=self.embed_dim,
    num_heads=self.num_heads,
    dropout=self.drop_rate,
    batch_first=True
)
```

#### 3. Training Layer (`cell_segmentation/trainer/trainer_cellvit.py`)
**Changes:**
- Modified `train_step()` and `validation_step()` to handle external embeddings
- Updated batch unpacking to extract external embeddings from `batch[4]`
- Modified model forward calls to pass external embeddings
- Maintained backward compatibility for batches without external embeddings

**Key Changes:**
```python
# Handle external embeddings if present
external_embeddings = None
if len(batch) > 4:  # Check if external embeddings are provided
    external_embeddings = batch[4].to(self.device)

# Forward pass with external embeddings
predictions_ = self.model.forward(imgs, external_embeddings=external_embeddings)
```

#### 4. Loss Functions (`base_ml/base_loss.py`)
**Changes:**
- Added `EmbeddingAlignmentLoss` class for aligning visual and external embeddings
- Support for multiple alignment loss types: cosine similarity, MSE, L1
- Added to `LOSS_DICT` for easy configuration

**New Loss Function:**
```python
class EmbeddingAlignmentLoss(nn.Module):
    """Loss function for aligning visual and external embeddings"""
    
    def __init__(self, loss_type: str = "cosine", temperature: float = 0.1):
        # Supports "cosine", "mse", "l1" loss types
    
    def forward(self, visual_embeddings: torch.Tensor, external_embeddings: torch.Tensor):
        # Computes alignment loss between embeddings
```

### New Files Created

#### 1. Configuration Example (`configs/examples/cell_segmentation/train_cellvit_with_external_embeddings.yaml`)
- Complete configuration example showing how to enable external embeddings
- Includes data, model, training, and loss configuration sections
- Shows how to configure embedding alignment loss

#### 2. Example Script (`examples/train_cellvit_with_external_embeddings.py`)
- End-to-end example of training CellViT with external embeddings
- Demonstrates dataset creation, model initialization, and training setup
- Shows proper configuration loading and parameter setup

#### 3. Documentation (`docs/external_embeddings_guide.md`)
- Comprehensive guide for using external embeddings
- Covers data preparation, configuration, and best practices
- Includes troubleshooting section and example workflows

#### 4. Test Suite (`tests/test_external_embeddings.py`)
- Unit tests for all new components
- Tests dataset loading, model forward pass, loss computation, and integration
- Validates backward compatibility and error handling

## Usage Workflow

### 1. Prepare External Embeddings
```python
# Create embeddings dictionary
embeddings = {
    "image_001.png": np.random.randn(512),  # 512-dim embedding
    "image_002.png": np.random.randn(512),
    # ... more embeddings
}

# Save as .npz file
np.savez("external_embeddings.npz", **embeddings)
```

### 2. Configure Dataset
```python
dataset = PanNukeDataset(
    dataset_path="./data/PanNuke",
    folds=[0, 1, 2],
    external_embeddings_path="./data/external_embeddings.npz",
    embedding_dim=512
)
```

### 3. Configure Model
```python
model = CellViTSAM(
    model_path="./models/sam_vit_b_01ec64.pth",
    num_nuclei_classes=6,
    num_tissue_classes=19,
    vit_structure="SAM-B",
    external_embedding_dim=512,
    use_external_embeddings=True
)
```

### 4. Train with External Embeddings
```python
# Training automatically handles external embeddings
trainer.fit(train_dataloader, val_dataloader, epochs=130)
```

## Technical Architecture

### Fusion Mechanism
1. **Projection**: External embeddings are projected to match encoder dimension
2. **Attention**: Multi-head attention fuses class token with external embedding
3. **Normalization**: Layer norm with residual connection
4. **Classification**: Fused representation used for tissue classification

### Data Flow
```
Image → ViT Encoder → Class Token ─┐
                                   ├─→ Attention Fusion → Tissue Classification
External Embedding → Projection ──┘

Visual Features → Segmentation Decoders → Nuclei/HV/Type Maps
```

## Benefits

1. **Enhanced Representation Learning**: Combines visual and molecular information
2. **Improved Classification**: Better tissue type prediction with multi-modal data
3. **Flexible Integration**: Can be enabled/disabled without code changes
4. **Scalable Design**: Supports various embedding dimensions and file formats
5. **Research Enablement**: Facilitates multi-modal histopathology research

## Future Enhancements

1. **Alternative Fusion Methods**: Concatenation, element-wise addition, gated fusion
2. **Hierarchical Embeddings**: Multi-scale transcription data integration
3. **Dynamic Loading**: Memory-efficient loading for large embedding datasets
4. **Cross-modal Attention**: Bidirectional attention between visual and molecular features
5. **Uncertainty Quantification**: Confidence estimation for multi-modal predictions

## Validation

The implementation has been tested for:
- ✅ Backward compatibility with existing models
- ✅ Proper handling of missing embeddings
- ✅ Multiple file format support
- ✅ Integration with existing training pipeline
- ✅ Loss function computation
- ✅ Memory efficiency and performance

This implementation provides a solid foundation for multi-modal learning in computational pathology, enabling researchers to leverage both visual and molecular information for improved cell segmentation and tissue analysis.
