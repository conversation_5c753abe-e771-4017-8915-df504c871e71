2023-09-20 05:59:10,297 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-20 05:59:10,365 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f68b012ec40>]
2023-09-20 05:59:10,365 [INFO] - Using GPU: cuda:0
2023-09-20 05:59:10,365 [INFO] - Using device: cuda:0
2023-09-20 05:59:10,366 [INFO] - Loss functions:
2023-09-20 05:59:10,366 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 1}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON><PERSON>(), 'weight': 1}}}
2023-09-20 05:59:15,009 [INFO] -
Model: StarDistRN50(
  (encoder): ResNet(
    (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (relu): ReLU(inplace=True)
    (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (layer1): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer2): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer3): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (4): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (5): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer4): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
  )
  (up1): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4): Upsample(scale_factor=2.0, mode=bilinear)
  (features): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (out_prob): outconv(
    (conv): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (out_ray): outconv(
    (conv): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
  )
  (up1_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4_seg): Upsample(scale_factor=2.0, mode=bilinear)
  (out_seg): outconv(
    (conv): Conv2d(256, 6, kernel_size=(1, 1), stride=(1, 1))
  )
  (final_activation_ray): ReLU()
)
2023-09-20 05:59:16,386 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
StarDistRN50                                  [1, 6, 256, 256]          --
├─ResNet: 1-1                                 [1, 256, 128, 128]        --
│    └─Conv2d: 2-1                            [1, 64, 128, 128]         9,408
│    └─BatchNorm2d: 2-2                       [1, 64, 128, 128]         128
│    └─ReLU: 2-3                              [1, 64, 128, 128]         --
│    └─Sequential: 2-4                        [1, 256, 128, 128]        --
│    │    └─Bottleneck: 3-1                   [1, 256, 128, 128]        75,008
│    │    └─Bottleneck: 3-2                   [1, 256, 128, 128]        70,400
│    │    └─Bottleneck: 3-3                   [1, 256, 128, 128]        70,400
│    └─Sequential: 2-5                        [1, 512, 64, 64]          --
│    │    └─Bottleneck: 3-4                   [1, 512, 64, 64]          379,392
│    │    └─Bottleneck: 3-5                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-6                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-7                   [1, 512, 64, 64]          280,064
│    └─Sequential: 2-6                        [1, 1024, 32, 32]         --
│    │    └─Bottleneck: 3-8                   [1, 1024, 32, 32]         1,512,448
│    │    └─Bottleneck: 3-9                   [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-10                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-11                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-12                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-13                  [1, 1024, 32, 32]         1,117,184
│    └─Sequential: 2-7                        [1, 2048, 16, 16]         --
│    │    └─Bottleneck: 3-14                  [1, 2048, 16, 16]         6,039,552
│    │    └─Bottleneck: 3-15                  [1, 2048, 16, 16]         4,462,592
│    │    └─Bottleneck: 3-16                  [1, 2048, 16, 16]         4,462,592
├─up: 1-2                                     [1, 1024, 32, 32]         --
│    └─Upsample: 2-8                          [1, 2048, 32, 32]         --
│    └─double_conv: 2-9                       [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-17                  [1, 1024, 32, 32]         37,754,880
├─up: 1-3                                     [1, 512, 64, 64]          --
│    └─Upsample: 2-10                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-11                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-18                  [1, 512, 64, 64]          9,440,256
├─up: 1-4                                     [1, 256, 128, 128]        --
│    └─Upsample: 2-12                         [1, 512, 128, 128]        --
│    └─double_conv: 2-13                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-19                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-5                               [1, 256, 256, 256]        --
├─Conv2d: 1-6                                 [1, 256, 256, 256]        590,080
├─outconv: 1-7                                [1, 1, 256, 256]          --
│    └─Conv2d: 2-14                           [1, 1, 256, 256]          257
├─outconv: 1-8                                [1, 32, 256, 256]         --
│    └─Conv2d: 2-15                           [1, 32, 256, 256]         8,224
├─ReLU: 1-9                                   [1, 32, 256, 256]         --
├─up: 1-10                                    [1, 1024, 32, 32]         --
│    └─Upsample: 2-16                         [1, 2048, 32, 32]         --
│    └─double_conv: 2-17                      [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-20                  [1, 1024, 32, 32]         37,754,880
├─up: 1-11                                    [1, 512, 64, 64]          --
│    └─Upsample: 2-18                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-19                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-21                  [1, 512, 64, 64]          9,440,256
├─up: 1-12                                    [1, 256, 128, 128]        --
│    └─Upsample: 2-20                         [1, 512, 128, 128]        --
│    └─double_conv: 2-21                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-22                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-13                              [1, 256, 256, 256]        --
├─outconv: 1-14                               [1, 6, 256, 256]          --
│    └─Conv2d: 2-22                           [1, 6, 256, 256]          1,542
===============================================================================================
Total params: 123,220,071
Trainable params: 123,220,071
Non-trainable params: 0
Total mult-adds (G): 292.18
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1503.13
Params size (MB): 492.88
Estimated Total Size (MB): 1996.80
===============================================================================================
2023-09-20 05:59:35,031 [INFO] - Loaded Adam Optimizer with following hyperparameters:
2023-09-20 05:59:35,034 [INFO] - {'lr': 0.0001}
2023-09-20 05:59:35,034 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-20 06:00:03,429 [INFO] - Using RandomSampler
2023-09-20 06:00:03,432 [INFO] - Instantiate Trainer
2023-09-20 06:00:03,433 [INFO] - Calling Trainer Fit
2023-09-20 06:00:03,433 [INFO] - Starting training, total number of epochs: 130
2023-09-20 06:00:03,433 [INFO] - Epoch: 1/130
2023-09-20 06:02:20,856 [INFO] - Training epoch stats:     Loss: 4.7553 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:06:15,729 [INFO] - Validation epoch stats:   Loss: 3.9305 - Binary-Cell-Dice: 0.3163 - Binary-Cell-Jacard: 0.2149 - bPQ-Score: 0.1223 - mPQ-Score: 0.0675 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:06:15,737 [INFO] - New best model - save checkpoint
2023-09-20 06:07:27,718 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:07:27,718 [INFO] - Epoch: 2/130
2023-09-20 06:09:25,343 [INFO] - Training epoch stats:     Loss: 3.8042 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:14:51,939 [INFO] - Validation epoch stats:   Loss: 3.5933 - Binary-Cell-Dice: 0.6746 - Binary-Cell-Jacard: 0.5545 - bPQ-Score: 0.1707 - mPQ-Score: 0.1291 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:14:51,949 [INFO] - New best model - save checkpoint
2023-09-20 06:15:49,476 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:15:49,476 [INFO] - Epoch: 3/130
2023-09-20 06:17:46,715 [INFO] - Training epoch stats:     Loss: 3.5296 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:22:51,102 [INFO] - Validation epoch stats:   Loss: 3.4189 - Binary-Cell-Dice: 0.6869 - Binary-Cell-Jacard: 0.5673 - bPQ-Score: 0.3693 - mPQ-Score: 0.2463 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:22:51,114 [INFO] - New best model - save checkpoint
2023-09-20 06:24:41,672 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:24:41,674 [INFO] - Epoch: 4/130
2023-09-20 06:26:43,334 [INFO] - Training epoch stats:     Loss: 3.4041 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:33:50,520 [INFO] - Validation epoch stats:   Loss: 3.2384 - Binary-Cell-Dice: 0.6732 - Binary-Cell-Jacard: 0.5522 - bPQ-Score: 0.3366 - mPQ-Score: 0.2311 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:34:16,940 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:34:16,941 [INFO] - Epoch: 5/130
2023-09-20 06:36:22,375 [INFO] - Training epoch stats:     Loss: 3.2725 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:41:49,733 [INFO] - Validation epoch stats:   Loss: 3.1587 - Binary-Cell-Dice: 0.6592 - Binary-Cell-Jacard: 0.5377 - bPQ-Score: 0.3643 - mPQ-Score: 0.2519 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:42:34,889 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:42:34,891 [INFO] - Epoch: 6/130
2023-09-20 06:44:43,434 [INFO] - Training epoch stats:     Loss: 3.2308 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:49:36,762 [INFO] - Validation epoch stats:   Loss: 3.1402 - Binary-Cell-Dice: 0.7007 - Binary-Cell-Jacard: 0.5854 - bPQ-Score: 0.3928 - mPQ-Score: 0.2708 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:49:36,772 [INFO] - New best model - save checkpoint
2023-09-20 06:51:47,993 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:51:47,996 [INFO] - Epoch: 7/130
2023-09-20 06:53:48,379 [INFO] - Training epoch stats:     Loss: 3.1245 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:00:23,415 [INFO] - Validation epoch stats:   Loss: 3.0446 - Binary-Cell-Dice: 0.6923 - Binary-Cell-Jacard: 0.5781 - bPQ-Score: 0.4210 - mPQ-Score: 0.2972 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:00:23,424 [INFO] - New best model - save checkpoint
2023-09-20 07:01:57,645 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:01:57,646 [INFO] - Epoch: 8/130
2023-09-20 07:03:58,998 [INFO] - Training epoch stats:     Loss: 3.0976 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:09:06,100 [INFO] - Validation epoch stats:   Loss: 3.0870 - Binary-Cell-Dice: 0.6879 - Binary-Cell-Jacard: 0.5721 - bPQ-Score: 0.4503 - mPQ-Score: 0.3009 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:09:06,110 [INFO] - New best model - save checkpoint
2023-09-20 07:10:24,166 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:10:24,167 [INFO] - Epoch: 9/130
2023-09-20 07:12:31,634 [INFO] - Training epoch stats:     Loss: 3.0870 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:18:26,389 [INFO] - Validation epoch stats:   Loss: 3.0550 - Binary-Cell-Dice: 0.7335 - Binary-Cell-Jacard: 0.6315 - bPQ-Score: 0.5083 - mPQ-Score: 0.3478 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:18:26,396 [INFO] - New best model - save checkpoint
2023-09-20 07:19:08,336 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:19:08,337 [INFO] - Epoch: 10/130
2023-09-20 07:21:16,657 [INFO] - Training epoch stats:     Loss: 3.0078 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:26:59,800 [INFO] - Validation epoch stats:   Loss: 2.8889 - Binary-Cell-Dice: 0.7371 - Binary-Cell-Jacard: 0.6330 - bPQ-Score: 0.5022 - mPQ-Score: 0.3634 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:27:37,698 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:27:37,699 [INFO] - Epoch: 11/130
2023-09-20 07:29:38,534 [INFO] - Training epoch stats:     Loss: 2.9937 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:34:45,503 [INFO] - Validation epoch stats:   Loss: 2.8802 - Binary-Cell-Dice: 0.7389 - Binary-Cell-Jacard: 0.6361 - bPQ-Score: 0.5184 - mPQ-Score: 0.3717 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:34:45,514 [INFO] - New best model - save checkpoint
2023-09-20 07:36:21,517 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:36:21,526 [INFO] - Epoch: 12/130
2023-09-20 07:38:24,319 [INFO] - Training epoch stats:     Loss: 2.9308 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:43:11,022 [INFO] - Validation epoch stats:   Loss: 2.9269 - Binary-Cell-Dice: 0.7313 - Binary-Cell-Jacard: 0.6316 - bPQ-Score: 0.5316 - mPQ-Score: 0.3806 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:43:11,033 [INFO] - New best model - save checkpoint
2023-09-20 07:44:46,937 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:44:46,943 [INFO] - Epoch: 13/130
2023-09-20 07:46:47,730 [INFO] - Training epoch stats:     Loss: 2.9870 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:51:24,466 [INFO] - Validation epoch stats:   Loss: 2.9290 - Binary-Cell-Dice: 0.7332 - Binary-Cell-Jacard: 0.6302 - bPQ-Score: 0.5140 - mPQ-Score: 0.3621 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:51:43,985 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:51:43,986 [INFO] - Epoch: 14/130
2023-09-20 07:53:42,331 [INFO] - Training epoch stats:     Loss: 2.8975 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:01:47,483 [INFO] - Validation epoch stats:   Loss: 3.0054 - Binary-Cell-Dice: 0.7451 - Binary-Cell-Jacard: 0.6455 - bPQ-Score: 0.5277 - mPQ-Score: 0.3701 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:02:10,162 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:02:10,163 [INFO] - Epoch: 15/130
2023-09-20 08:04:15,389 [INFO] - Training epoch stats:     Loss: 2.8532 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:08:50,713 [INFO] - Validation epoch stats:   Loss: 2.8687 - Binary-Cell-Dice: 0.7468 - Binary-Cell-Jacard: 0.6494 - bPQ-Score: 0.5365 - mPQ-Score: 0.3772 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:08:50,723 [INFO] - New best model - save checkpoint
2023-09-20 08:10:08,784 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:10:08,784 [INFO] - Epoch: 16/130
2023-09-20 08:12:09,452 [INFO] - Training epoch stats:     Loss: 2.8527 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:17:04,941 [INFO] - Validation epoch stats:   Loss: 2.8088 - Binary-Cell-Dice: 0.7440 - Binary-Cell-Jacard: 0.6481 - bPQ-Score: 0.5421 - mPQ-Score: 0.3891 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:17:04,951 [INFO] - New best model - save checkpoint
2023-09-20 08:17:50,272 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:17:50,273 [INFO] - Epoch: 17/130
2023-09-20 08:19:48,654 [INFO] - Training epoch stats:     Loss: 2.8316 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:24:43,726 [INFO] - Validation epoch stats:   Loss: 3.0948 - Binary-Cell-Dice: 0.7429 - Binary-Cell-Jacard: 0.6390 - bPQ-Score: 0.5315 - mPQ-Score: 0.3765 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:25:27,151 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:25:27,152 [INFO] - Epoch: 18/130
2023-09-20 08:27:27,619 [INFO] - Training epoch stats:     Loss: 2.9041 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:31:34,717 [INFO] - Validation epoch stats:   Loss: 2.9146 - Binary-Cell-Dice: 0.7535 - Binary-Cell-Jacard: 0.6567 - bPQ-Score: 0.5510 - mPQ-Score: 0.3894 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:31:34,727 [INFO] - New best model - save checkpoint
2023-09-20 08:32:45,669 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:32:45,669 [INFO] - Epoch: 19/130
2023-09-20 08:34:46,810 [INFO] - Training epoch stats:     Loss: 2.7930 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:40:05,490 [INFO] - Validation epoch stats:   Loss: 2.8039 - Binary-Cell-Dice: 0.7402 - Binary-Cell-Jacard: 0.6435 - bPQ-Score: 0.5448 - mPQ-Score: 0.3979 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:40:23,655 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:40:23,656 [INFO] - Epoch: 20/130
2023-09-20 08:42:25,115 [INFO] - Training epoch stats:     Loss: 2.7529 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:48:59,535 [INFO] - Validation epoch stats:   Loss: 2.7914 - Binary-Cell-Dice: 0.7447 - Binary-Cell-Jacard: 0.6482 - bPQ-Score: 0.5525 - mPQ-Score: 0.4023 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:48:59,542 [INFO] - New best model - save checkpoint
2023-09-20 08:50:18,319 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:50:18,328 [INFO] - Epoch: 21/130
2023-09-20 08:52:18,954 [INFO] - Training epoch stats:     Loss: 2.7500 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:01:17,134 [INFO] - Validation epoch stats:   Loss: 2.7793 - Binary-Cell-Dice: 0.7487 - Binary-Cell-Jacard: 0.6515 - bPQ-Score: 0.5518 - mPQ-Score: 0.3987 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:02:11,470 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:02:11,471 [INFO] - Epoch: 22/130
2023-09-20 09:04:16,661 [INFO] - Training epoch stats:     Loss: 2.7356 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:11:00,916 [INFO] - Validation epoch stats:   Loss: 2.8908 - Binary-Cell-Dice: 0.7503 - Binary-Cell-Jacard: 0.6543 - bPQ-Score: 0.5408 - mPQ-Score: 0.3855 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:11:43,656 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:11:43,656 [INFO] - Epoch: 23/130
2023-09-20 09:13:45,236 [INFO] - Training epoch stats:     Loss: 2.7264 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:26:06,991 [INFO] - Validation epoch stats:   Loss: 2.7613 - Binary-Cell-Dice: 0.7624 - Binary-Cell-Jacard: 0.6685 - bPQ-Score: 0.5597 - mPQ-Score: 0.4108 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:26:06,995 [INFO] - New best model - save checkpoint
2023-09-20 09:26:52,696 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:26:52,698 [INFO] - Epoch: 24/130
2023-09-20 09:28:51,400 [INFO] - Training epoch stats:     Loss: 2.6669 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:34:35,673 [INFO] - Validation epoch stats:   Loss: 2.7334 - Binary-Cell-Dice: 0.7439 - Binary-Cell-Jacard: 0.6466 - bPQ-Score: 0.5511 - mPQ-Score: 0.4020 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:35:27,926 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:35:27,926 [INFO] - Epoch: 25/130
2023-09-20 09:37:29,425 [INFO] - Training epoch stats:     Loss: 2.6545 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:41:32,349 [INFO] - Validation epoch stats:   Loss: 2.7762 - Binary-Cell-Dice: 0.7311 - Binary-Cell-Jacard: 0.6318 - bPQ-Score: 0.5429 - mPQ-Score: 0.4053 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:41:50,825 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:41:50,826 [INFO] - Epoch: 26/130
2023-09-20 09:43:48,852 [INFO] - Training epoch stats:     Loss: 2.7532 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:08:48,827 [INFO] - Validation epoch stats:   Loss: 3.0052 - Binary-Cell-Dice: 0.7480 - Binary-Cell-Jacard: 0.6534 - bPQ-Score: 0.5599 - mPQ-Score: 0.3942 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:08:48,834 [INFO] - New best model - save checkpoint
2023-09-20 10:09:24,593 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:09:24,594 [INFO] - Epoch: 27/130
2023-09-20 10:11:21,214 [INFO] - Training epoch stats:     Loss: 2.6970 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:15:44,117 [INFO] - Validation epoch stats:   Loss: 2.7972 - Binary-Cell-Dice: 0.7432 - Binary-Cell-Jacard: 0.6463 - bPQ-Score: 0.5519 - mPQ-Score: 0.3990 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:16:01,644 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:16:01,644 [INFO] - Epoch: 28/130
2023-09-20 10:17:58,664 [INFO] - Training epoch stats:     Loss: 2.6695 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:25:10,553 [INFO] - Validation epoch stats:   Loss: 2.6785 - Binary-Cell-Dice: 0.7556 - Binary-Cell-Jacard: 0.6617 - bPQ-Score: 0.5703 - mPQ-Score: 0.4222 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:25:10,562 [INFO] - New best model - save checkpoint
2023-09-20 10:26:06,508 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:26:06,508 [INFO] - Epoch: 29/130
2023-09-20 10:28:03,213 [INFO] - Training epoch stats:     Loss: 2.6515 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:41:59,413 [INFO] - Validation epoch stats:   Loss: 2.8431 - Binary-Cell-Dice: 0.7511 - Binary-Cell-Jacard: 0.6572 - bPQ-Score: 0.5664 - mPQ-Score: 0.4063 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:42:18,124 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:42:18,124 [INFO] - Epoch: 30/130
2023-09-20 10:45:08,490 [INFO] - Training epoch stats:     Loss: 2.6156 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:50:46,679 [INFO] - Validation epoch stats:   Loss: 2.6615 - Binary-Cell-Dice: 0.7664 - Binary-Cell-Jacard: 0.6761 - bPQ-Score: 0.5820 - mPQ-Score: 0.4364 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:50:46,688 [INFO] - New best model - save checkpoint
2023-09-20 10:51:47,998 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:51:47,998 [INFO] - Epoch: 31/130
2023-09-20 10:54:37,310 [INFO] - Training epoch stats:     Loss: 2.5759 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:23:07,080 [INFO] - Validation epoch stats:   Loss: 2.8142 - Binary-Cell-Dice: 0.7453 - Binary-Cell-Jacard: 0.6506 - bPQ-Score: 0.5681 - mPQ-Score: 0.4113 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:23:35,637 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:23:35,638 [INFO] - Epoch: 32/130
2023-09-20 11:25:42,648 [INFO] - Training epoch stats:     Loss: 2.6127 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:41:32,435 [INFO] - Validation epoch stats:   Loss: 2.7375 - Binary-Cell-Dice: 0.7510 - Binary-Cell-Jacard: 0.6557 - bPQ-Score: 0.5607 - mPQ-Score: 0.4149 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:41:50,211 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:41:50,211 [INFO] - Epoch: 33/130
2023-09-20 11:44:36,567 [INFO] - Training epoch stats:     Loss: 2.5818 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:49:46,080 [INFO] - Validation epoch stats:   Loss: 2.7893 - Binary-Cell-Dice: 0.7603 - Binary-Cell-Jacard: 0.6678 - bPQ-Score: 0.5696 - mPQ-Score: 0.4267 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:50:17,877 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:50:17,877 [INFO] - Epoch: 34/130
2023-09-20 11:53:05,418 [INFO] - Training epoch stats:     Loss: 2.6205 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:00:54,532 [INFO] - Validation epoch stats:   Loss: 2.7342 - Binary-Cell-Dice: 0.7624 - Binary-Cell-Jacard: 0.6728 - bPQ-Score: 0.5822 - mPQ-Score: 0.4309 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:00:54,536 [INFO] - New best model - save checkpoint
2023-09-20 12:01:36,118 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:01:36,119 [INFO] - Epoch: 35/130
2023-09-20 12:04:27,740 [INFO] - Training epoch stats:     Loss: 2.5482 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:11:07,329 [INFO] - Validation epoch stats:   Loss: 2.7346 - Binary-Cell-Dice: 0.7589 - Binary-Cell-Jacard: 0.6673 - bPQ-Score: 0.5816 - mPQ-Score: 0.4159 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:12:02,554 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:12:02,555 [INFO] - Epoch: 36/130
2023-09-20 12:14:09,780 [INFO] - Training epoch stats:     Loss: 2.5585 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:20:00,448 [INFO] - Validation epoch stats:   Loss: 2.7971 - Binary-Cell-Dice: 0.7709 - Binary-Cell-Jacard: 0.6814 - bPQ-Score: 0.5842 - mPQ-Score: 0.4346 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:20:00,524 [INFO] - New best model - save checkpoint
2023-09-20 12:21:06,003 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:21:06,010 [INFO] - Epoch: 37/130
2023-09-20 12:24:06,337 [INFO] - Training epoch stats:     Loss: 2.5314 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:29:36,706 [INFO] - Validation epoch stats:   Loss: 2.8265 - Binary-Cell-Dice: 0.7656 - Binary-Cell-Jacard: 0.6761 - bPQ-Score: 0.5848 - mPQ-Score: 0.4196 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:29:36,717 [INFO] - New best model - save checkpoint
2023-09-20 12:30:59,571 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:30:59,572 [INFO] - Epoch: 38/130
2023-09-20 12:33:56,016 [INFO] - Training epoch stats:     Loss: 2.5789 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:39:33,591 [INFO] - Validation epoch stats:   Loss: 2.7796 - Binary-Cell-Dice: 0.7478 - Binary-Cell-Jacard: 0.6517 - bPQ-Score: 0.5731 - mPQ-Score: 0.4176 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:40:35,347 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:40:35,374 [INFO] - Epoch: 39/130
2023-09-20 12:42:35,558 [INFO] - Training epoch stats:     Loss: 2.6120 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:47:24,987 [INFO] - Validation epoch stats:   Loss: 2.8788 - Binary-Cell-Dice: 0.7447 - Binary-Cell-Jacard: 0.6463 - bPQ-Score: 0.5604 - mPQ-Score: 0.4061 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:48:18,699 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:48:18,699 [INFO] - Epoch: 40/130
2023-09-20 12:50:19,702 [INFO] - Training epoch stats:     Loss: 2.5000 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:55:14,226 [INFO] - Validation epoch stats:   Loss: 2.6896 - Binary-Cell-Dice: 0.7694 - Binary-Cell-Jacard: 0.6822 - bPQ-Score: 0.5946 - mPQ-Score: 0.4487 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:55:14,263 [INFO] - New best model - save checkpoint
2023-09-20 12:56:28,988 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:56:28,989 [INFO] - Epoch: 41/130
2023-09-20 12:58:41,830 [INFO] - Training epoch stats:     Loss: 2.5288 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:03:33,857 [INFO] - Validation epoch stats:   Loss: 2.6218 - Binary-Cell-Dice: 0.7687 - Binary-Cell-Jacard: 0.6786 - bPQ-Score: 0.5891 - mPQ-Score: 0.4429 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:04:30,327 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:04:30,327 [INFO] - Epoch: 42/130
2023-09-20 13:06:31,766 [INFO] - Training epoch stats:     Loss: 2.5196 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:11:43,348 [INFO] - Validation epoch stats:   Loss: 2.9090 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6787 - bPQ-Score: 0.5864 - mPQ-Score: 0.4167 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:12:00,572 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:12:00,572 [INFO] - Epoch: 43/130
2023-09-20 13:13:57,301 [INFO] - Training epoch stats:     Loss: 2.5265 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:20:14,607 [INFO] - Validation epoch stats:   Loss: 2.7044 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6794 - bPQ-Score: 0.5826 - mPQ-Score: 0.4326 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:21:02,292 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:21:02,293 [INFO] - Epoch: 44/130
2023-09-20 13:23:02,744 [INFO] - Training epoch stats:     Loss: 2.4693 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:28:35,250 [INFO] - Validation epoch stats:   Loss: 2.6094 - Binary-Cell-Dice: 0.7533 - Binary-Cell-Jacard: 0.6571 - bPQ-Score: 0.5651 - mPQ-Score: 0.4217 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:29:13,635 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:29:13,636 [INFO] - Epoch: 45/130
2023-09-20 13:31:13,971 [INFO] - Training epoch stats:     Loss: 2.4320 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:36:42,106 [INFO] - Validation epoch stats:   Loss: 2.7734 - Binary-Cell-Dice: 0.7649 - Binary-Cell-Jacard: 0.6741 - bPQ-Score: 0.5848 - mPQ-Score: 0.4337 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:37:14,060 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:37:14,060 [INFO] - Epoch: 46/130
2023-09-20 13:39:15,772 [INFO] - Training epoch stats:     Loss: 2.4407 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:44:44,077 [INFO] - Validation epoch stats:   Loss: 2.6396 - Binary-Cell-Dice: 0.7630 - Binary-Cell-Jacard: 0.6731 - bPQ-Score: 0.5933 - mPQ-Score: 0.4505 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:45:16,034 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:45:16,038 [INFO] - Epoch: 47/130
2023-09-20 13:47:25,705 [INFO] - Training epoch stats:     Loss: 2.4601 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:11:26,090 [INFO] - Validation epoch stats:   Loss: 2.6675 - Binary-Cell-Dice: 0.7655 - Binary-Cell-Jacard: 0.6768 - bPQ-Score: 0.5918 - mPQ-Score: 0.4439 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:11:56,603 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 14:11:56,604 [INFO] - Epoch: 48/130
2023-09-20 14:13:55,726 [INFO] - Training epoch stats:     Loss: 2.4928 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:18:56,220 [INFO] - Validation epoch stats:   Loss: 2.6812 - Binary-Cell-Dice: 0.7667 - Binary-Cell-Jacard: 0.6773 - bPQ-Score: 0.5934 - mPQ-Score: 0.4447 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:19:49,949 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 14:19:49,950 [INFO] - Epoch: 49/130
2023-09-20 14:21:51,093 [INFO] - Training epoch stats:     Loss: 2.4689 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:27:36,829 [INFO] - Validation epoch stats:   Loss: 2.7723 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6888 - bPQ-Score: 0.5894 - mPQ-Score: 0.4388 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:28:02,235 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 14:28:02,236 [INFO] - Epoch: 50/130
2023-09-20 14:30:32,986 [INFO] - Training epoch stats:     Loss: 2.4385 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:51:08,194 [INFO] - Validation epoch stats:   Loss: 2.7330 - Binary-Cell-Dice: 0.7608 - Binary-Cell-Jacard: 0.6686 - bPQ-Score: 0.5859 - mPQ-Score: 0.4379 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:52:13,328 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 14:52:13,329 [INFO] - Epoch: 51/130
2023-09-20 14:54:15,385 [INFO] - Training epoch stats:     Loss: 2.4416 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:13:31,515 [INFO] - Validation epoch stats:   Loss: 2.6426 - Binary-Cell-Dice: 0.7574 - Binary-Cell-Jacard: 0.6651 - bPQ-Score: 0.5758 - mPQ-Score: 0.4247 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:13:49,002 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 15:13:49,002 [INFO] - Epoch: 52/130
2023-09-20 15:15:49,311 [INFO] - Training epoch stats:     Loss: 2.3932 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:30:50,228 [INFO] - Validation epoch stats:   Loss: 2.7000 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6766 - bPQ-Score: 0.5855 - mPQ-Score: 0.4334 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:31:33,983 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 15:31:33,983 [INFO] - Epoch: 53/130
2023-09-20 15:33:50,210 [INFO] - Training epoch stats:     Loss: 2.3745 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:39:02,408 [INFO] - Validation epoch stats:   Loss: 2.6381 - Binary-Cell-Dice: 0.7703 - Binary-Cell-Jacard: 0.6832 - bPQ-Score: 0.5953 - mPQ-Score: 0.4507 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:39:02,418 [INFO] - New best model - save checkpoint
2023-09-20 15:40:35,932 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 15:40:35,939 [INFO] - Epoch: 54/130
2023-09-20 15:42:38,077 [INFO] - Training epoch stats:     Loss: 2.4133 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:49:05,371 [INFO] - Validation epoch stats:   Loss: 2.6960 - Binary-Cell-Dice: 0.7655 - Binary-Cell-Jacard: 0.6792 - bPQ-Score: 0.5925 - mPQ-Score: 0.4313 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:49:22,462 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 15:49:22,462 [INFO] - Epoch: 55/130
2023-09-20 15:51:20,028 [INFO] - Training epoch stats:     Loss: 2.3647 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:56:05,618 [INFO] - Validation epoch stats:   Loss: 2.5802 - Binary-Cell-Dice: 0.7680 - Binary-Cell-Jacard: 0.6806 - bPQ-Score: 0.5940 - mPQ-Score: 0.4515 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:56:25,668 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 15:56:25,669 [INFO] - Epoch: 56/130
2023-09-20 15:58:24,530 [INFO] - Training epoch stats:     Loss: 2.3775 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:03:39,602 [INFO] - Validation epoch stats:   Loss: 2.6172 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6801 - bPQ-Score: 0.5914 - mPQ-Score: 0.4438 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:04:18,396 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:04:18,397 [INFO] - Epoch: 57/130
2023-09-20 16:06:19,935 [INFO] - Training epoch stats:     Loss: 2.3234 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:10:58,543 [INFO] - Validation epoch stats:   Loss: 2.6713 - Binary-Cell-Dice: 0.7613 - Binary-Cell-Jacard: 0.6712 - bPQ-Score: 0.5880 - mPQ-Score: 0.4399 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:11:53,306 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:11:53,307 [INFO] - Epoch: 58/130
2023-09-20 16:13:56,134 [INFO] - Training epoch stats:     Loss: 2.3689 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:18:38,195 [INFO] - Validation epoch stats:   Loss: 2.7084 - Binary-Cell-Dice: 0.7593 - Binary-Cell-Jacard: 0.6697 - bPQ-Score: 0.5906 - mPQ-Score: 0.4248 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:19:48,779 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:19:48,785 [INFO] - Epoch: 59/130
2023-09-20 16:21:51,053 [INFO] - Training epoch stats:     Loss: 2.3536 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:27:21,534 [INFO] - Validation epoch stats:   Loss: 2.7246 - Binary-Cell-Dice: 0.7704 - Binary-Cell-Jacard: 0.6826 - bPQ-Score: 0.5955 - mPQ-Score: 0.4328 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:27:21,543 [INFO] - New best model - save checkpoint
2023-09-20 16:28:24,411 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:28:24,415 [INFO] - Epoch: 60/130
2023-09-20 16:30:26,141 [INFO] - Training epoch stats:     Loss: 2.3583 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:34:57,926 [INFO] - Validation epoch stats:   Loss: 2.8565 - Binary-Cell-Dice: 0.7557 - Binary-Cell-Jacard: 0.6660 - bPQ-Score: 0.5912 - mPQ-Score: 0.4287 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:36:02,040 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:36:02,048 [INFO] - Epoch: 61/130
2023-09-20 16:38:03,727 [INFO] - Training epoch stats:     Loss: 2.3480 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:43:00,155 [INFO] - Validation epoch stats:   Loss: 2.7534 - Binary-Cell-Dice: 0.7642 - Binary-Cell-Jacard: 0.6713 - bPQ-Score: 0.5822 - mPQ-Score: 0.4312 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:43:32,176 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:43:32,177 [INFO] - Epoch: 62/130
2023-09-20 16:45:32,888 [INFO] - Training epoch stats:     Loss: 2.4375 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:50:49,462 [INFO] - Validation epoch stats:   Loss: 2.7002 - Binary-Cell-Dice: 0.7728 - Binary-Cell-Jacard: 0.6859 - bPQ-Score: 0.5979 - mPQ-Score: 0.4447 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:50:49,466 [INFO] - New best model - save checkpoint
2023-09-20 16:51:26,617 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:51:26,618 [INFO] - Epoch: 63/130
2023-09-20 16:53:24,375 [INFO] - Training epoch stats:     Loss: 2.4048 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:59:23,614 [INFO] - Validation epoch stats:   Loss: 2.6890 - Binary-Cell-Dice: 0.7729 - Binary-Cell-Jacard: 0.6860 - bPQ-Score: 0.5963 - mPQ-Score: 0.4410 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:59:52,030 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 16:59:52,031 [INFO] - Epoch: 64/130
2023-09-20 17:01:54,110 [INFO] - Training epoch stats:     Loss: 2.3318 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:07:07,527 [INFO] - Validation epoch stats:   Loss: 2.6853 - Binary-Cell-Dice: 0.7737 - Binary-Cell-Jacard: 0.6834 - bPQ-Score: 0.5972 - mPQ-Score: 0.4432 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:07:39,383 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 17:07:39,383 [INFO] - Epoch: 65/130
2023-09-20 17:09:40,566 [INFO] - Training epoch stats:     Loss: 2.3196 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:14:45,093 [INFO] - Validation epoch stats:   Loss: 2.7074 - Binary-Cell-Dice: 0.7612 - Binary-Cell-Jacard: 0.6699 - bPQ-Score: 0.5856 - mPQ-Score: 0.4340 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:15:14,840 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 17:15:14,841 [INFO] - Epoch: 66/130
2023-09-20 17:17:12,799 [INFO] - Training epoch stats:     Loss: 2.3330 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:21:42,267 [INFO] - Validation epoch stats:   Loss: 2.6385 - Binary-Cell-Dice: 0.7722 - Binary-Cell-Jacard: 0.6851 - bPQ-Score: 0.5974 - mPQ-Score: 0.4508 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:21:58,811 [DEBUG] - Old lr: 0.000100 - New lr: 0.000050
2023-09-20 17:21:58,812 [INFO] - Epoch: 67/130
2023-09-20 17:23:56,663 [INFO] - Training epoch stats:     Loss: 2.2028 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:28:57,290 [INFO] - Validation epoch stats:   Loss: 2.5890 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6817 - bPQ-Score: 0.5985 - mPQ-Score: 0.4511 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:28:57,300 [INFO] - New best model - save checkpoint
2023-09-20 17:29:49,519 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:29:49,519 [INFO] - Epoch: 68/130
2023-09-20 17:31:48,077 [INFO] - Training epoch stats:     Loss: 2.1906 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:36:52,114 [INFO] - Validation epoch stats:   Loss: 2.5461 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6910 - bPQ-Score: 0.6043 - mPQ-Score: 0.4563 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:36:52,124 [INFO] - New best model - save checkpoint
2023-09-20 17:38:19,871 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:38:19,872 [INFO] - Epoch: 69/130
2023-09-20 17:40:21,766 [INFO] - Training epoch stats:     Loss: 2.1685 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:45:15,562 [INFO] - Validation epoch stats:   Loss: 2.5857 - Binary-Cell-Dice: 0.7660 - Binary-Cell-Jacard: 0.6785 - bPQ-Score: 0.5971 - mPQ-Score: 0.4556 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:45:36,938 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:45:36,939 [INFO] - Epoch: 70/130
2023-09-20 17:47:34,996 [INFO] - Training epoch stats:     Loss: 2.2010 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:51:44,613 [INFO] - Validation epoch stats:   Loss: 2.5755 - Binary-Cell-Dice: 0.7730 - Binary-Cell-Jacard: 0.6873 - bPQ-Score: 0.6012 - mPQ-Score: 0.4566 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:52:02,640 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:52:02,641 [INFO] - Epoch: 71/130
2023-09-20 17:54:01,713 [INFO] - Training epoch stats:     Loss: 1.8829 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:58:23,017 [INFO] - Validation epoch stats:   Loss: 2.4046 - Binary-Cell-Dice: 0.7739 - Binary-Cell-Jacard: 0.6889 - bPQ-Score: 0.6039 - mPQ-Score: 0.4567 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:58:47,002 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:58:47,002 [INFO] - Epoch: 72/130
2023-09-20 18:00:45,437 [INFO] - Training epoch stats:     Loss: 1.6890 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:05:08,442 [INFO] - Validation epoch stats:   Loss: 2.3486 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6911 - bPQ-Score: 0.6047 - mPQ-Score: 0.4558 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:05:08,450 [INFO] - New best model - save checkpoint
2023-09-20 18:05:47,611 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:05:47,611 [INFO] - Epoch: 73/130
2023-09-20 18:07:44,655 [INFO] - Training epoch stats:     Loss: 1.7017 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:11:49,786 [INFO] - Validation epoch stats:   Loss: 2.3011 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.5983 - mPQ-Score: 0.4512 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:12:18,919 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:12:18,920 [INFO] - Epoch: 74/130
2023-09-20 18:14:19,133 [INFO] - Training epoch stats:     Loss: 1.6995 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:21:32,533 [INFO] - Validation epoch stats:   Loss: 2.3787 - Binary-Cell-Dice: 0.7689 - Binary-Cell-Jacard: 0.6835 - bPQ-Score: 0.6011 - mPQ-Score: 0.4551 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:21:50,063 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:21:50,064 [INFO] - Epoch: 75/130
2023-09-20 18:23:47,921 [INFO] - Training epoch stats:     Loss: 1.6552 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:27:49,621 [INFO] - Validation epoch stats:   Loss: 2.3324 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6769 - bPQ-Score: 0.5915 - mPQ-Score: 0.4442 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:28:34,102 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:28:34,103 [INFO] - Epoch: 76/130
2023-09-20 18:30:33,033 [INFO] - Training epoch stats:     Loss: 1.5525 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:34:46,786 [INFO] - Validation epoch stats:   Loss: 2.3604 - Binary-Cell-Dice: 0.7645 - Binary-Cell-Jacard: 0.6754 - bPQ-Score: 0.5961 - mPQ-Score: 0.4493 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:35:04,852 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:35:04,853 [INFO] - Epoch: 77/130
2023-09-20 18:37:01,909 [INFO] - Training epoch stats:     Loss: 1.6634 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:42:48,914 [INFO] - Validation epoch stats:   Loss: 2.3574 - Binary-Cell-Dice: 0.7755 - Binary-Cell-Jacard: 0.6893 - bPQ-Score: 0.6012 - mPQ-Score: 0.4516 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:43:06,263 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:43:06,263 [INFO] - Epoch: 78/130
2023-09-20 18:45:05,245 [INFO] - Training epoch stats:     Loss: 1.6175 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:50:37,599 [INFO] - Validation epoch stats:   Loss: 2.3309 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.5968 - mPQ-Score: 0.4511 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:50:55,549 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:50:55,550 [INFO] - Epoch: 79/130
2023-09-20 18:52:52,869 [INFO] - Training epoch stats:     Loss: 1.5854 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:57:35,803 [INFO] - Validation epoch stats:   Loss: 2.2488 - Binary-Cell-Dice: 0.7725 - Binary-Cell-Jacard: 0.6882 - bPQ-Score: 0.6030 - mPQ-Score: 0.4565 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:57:53,484 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:57:53,485 [INFO] - Epoch: 80/130
2023-09-20 18:59:50,587 [INFO] - Training epoch stats:     Loss: 1.5426 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:04:38,080 [INFO] - Validation epoch stats:   Loss: 2.3308 - Binary-Cell-Dice: 0.7744 - Binary-Cell-Jacard: 0.6895 - bPQ-Score: 0.6064 - mPQ-Score: 0.4575 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:04:38,089 [INFO] - New best model - save checkpoint
2023-09-20 19:05:12,836 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:05:12,837 [INFO] - Epoch: 81/130
2023-09-20 19:07:11,218 [INFO] - Training epoch stats:     Loss: 1.6175 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:12:28,432 [INFO] - Validation epoch stats:   Loss: 2.3180 - Binary-Cell-Dice: 0.7774 - Binary-Cell-Jacard: 0.6906 - bPQ-Score: 0.6037 - mPQ-Score: 0.4549 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:12:44,813 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:12:44,813 [INFO] - Epoch: 82/130
2023-09-20 19:14:42,589 [INFO] - Training epoch stats:     Loss: 1.5533 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:19:23,674 [INFO] - Validation epoch stats:   Loss: 2.3734 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6803 - bPQ-Score: 0.5958 - mPQ-Score: 0.4499 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:19:41,330 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:19:41,330 [INFO] - Epoch: 83/130
2023-09-20 19:21:39,386 [INFO] - Training epoch stats:     Loss: 1.7114 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:26:14,889 [INFO] - Validation epoch stats:   Loss: 2.3384 - Binary-Cell-Dice: 0.7750 - Binary-Cell-Jacard: 0.6912 - bPQ-Score: 0.6045 - mPQ-Score: 0.4575 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:26:34,071 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:26:34,072 [INFO] - Epoch: 84/130
2023-09-20 19:28:35,550 [INFO] - Training epoch stats:     Loss: 1.6283 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:33:09,897 [INFO] - Validation epoch stats:   Loss: 2.3181 - Binary-Cell-Dice: 0.7674 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.5999 - mPQ-Score: 0.4521 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:33:27,960 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:33:27,961 [INFO] - Epoch: 85/130
2023-09-20 19:35:28,910 [INFO] - Training epoch stats:     Loss: 1.5984 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:40:33,405 [INFO] - Validation epoch stats:   Loss: 2.3755 - Binary-Cell-Dice: 0.7756 - Binary-Cell-Jacard: 0.6914 - bPQ-Score: 0.6107 - mPQ-Score: 0.4603 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:40:33,413 [INFO] - New best model - save checkpoint
2023-09-20 19:41:09,174 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:41:09,175 [INFO] - Epoch: 86/130
2023-09-20 19:43:07,284 [INFO] - Training epoch stats:     Loss: 1.6377 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:47:57,206 [INFO] - Validation epoch stats:   Loss: 2.3111 - Binary-Cell-Dice: 0.7738 - Binary-Cell-Jacard: 0.6903 - bPQ-Score: 0.6049 - mPQ-Score: 0.4579 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:48:13,532 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:48:13,532 [INFO] - Epoch: 87/130
2023-09-20 19:50:11,037 [INFO] - Training epoch stats:     Loss: 1.6189 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:55:25,251 [INFO] - Validation epoch stats:   Loss: 2.2814 - Binary-Cell-Dice: 0.7724 - Binary-Cell-Jacard: 0.6872 - bPQ-Score: 0.5973 - mPQ-Score: 0.4534 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:55:51,484 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 19:55:51,484 [INFO] - Epoch: 88/130
2023-09-20 19:57:49,873 [INFO] - Training epoch stats:     Loss: 1.5776 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:03:22,245 [INFO] - Validation epoch stats:   Loss: 2.3259 - Binary-Cell-Dice: 0.7763 - Binary-Cell-Jacard: 0.6901 - bPQ-Score: 0.6047 - mPQ-Score: 0.4605 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:03:38,830 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 20:03:38,830 [INFO] - Epoch: 89/130
2023-09-20 20:05:36,163 [INFO] - Training epoch stats:     Loss: 1.5989 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:10:30,314 [INFO] - Validation epoch stats:   Loss: 2.2923 - Binary-Cell-Dice: 0.7781 - Binary-Cell-Jacard: 0.6938 - bPQ-Score: 0.6060 - mPQ-Score: 0.4632 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:10:48,393 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 20:10:48,394 [INFO] - Epoch: 90/130
2023-09-20 20:12:45,317 [INFO] - Training epoch stats:     Loss: 1.5421 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:17:33,383 [INFO] - Validation epoch stats:   Loss: 2.3256 - Binary-Cell-Dice: 0.7680 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.6026 - mPQ-Score: 0.4526 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:17:51,493 [DEBUG] - Old lr: 0.000050 - New lr: 0.000025
2023-09-20 20:17:51,494 [INFO] - Epoch: 91/130
2023-09-20 20:19:49,218 [INFO] - Training epoch stats:     Loss: 1.5962 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:25:06,944 [INFO] - Validation epoch stats:   Loss: 2.2665 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6865 - bPQ-Score: 0.6041 - mPQ-Score: 0.4575 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:25:23,981 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 20:25:23,981 [INFO] - Epoch: 92/130
2023-09-20 20:27:21,892 [INFO] - Training epoch stats:     Loss: 1.4868 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:32:28,313 [INFO] - Validation epoch stats:   Loss: 2.2987 - Binary-Cell-Dice: 0.7758 - Binary-Cell-Jacard: 0.6919 - bPQ-Score: 0.6086 - mPQ-Score: 0.4564 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:32:46,198 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 20:32:46,199 [INFO] - Epoch: 93/130
2023-09-20 20:34:42,529 [INFO] - Training epoch stats:     Loss: 1.4275 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:40:11,011 [INFO] - Validation epoch stats:   Loss: 2.3193 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6821 - bPQ-Score: 0.6002 - mPQ-Score: 0.4543 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:40:28,646 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 20:40:28,647 [INFO] - Epoch: 94/130
2023-09-20 20:42:25,952 [INFO] - Training epoch stats:     Loss: 1.4657 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:46:31,800 [INFO] - Validation epoch stats:   Loss: 2.4687 - Binary-Cell-Dice: 0.7764 - Binary-Cell-Jacard: 0.6936 - bPQ-Score: 0.6096 - mPQ-Score: 0.4587 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:46:48,079 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 20:46:48,080 [INFO] - Epoch: 95/130
2023-09-20 20:48:44,444 [INFO] - Training epoch stats:     Loss: 1.4803 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:53:25,797 [INFO] - Validation epoch stats:   Loss: 2.3203 - Binary-Cell-Dice: 0.7761 - Binary-Cell-Jacard: 0.6920 - bPQ-Score: 0.6078 - mPQ-Score: 0.4579 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:53:45,849 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 20:53:45,849 [INFO] - Epoch: 96/130
2023-09-20 20:55:43,512 [INFO] - Training epoch stats:     Loss: 1.4586 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:00:16,991 [INFO] - Validation epoch stats:   Loss: 2.3498 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6904 - bPQ-Score: 0.6050 - mPQ-Score: 0.4557 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:00:32,587 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:00:32,588 [INFO] - Epoch: 97/130
2023-09-20 21:02:29,777 [INFO] - Training epoch stats:     Loss: 1.4081 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:08:20,277 [INFO] - Validation epoch stats:   Loss: 2.2639 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6905 - bPQ-Score: 0.6038 - mPQ-Score: 0.4564 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:08:37,747 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:08:37,747 [INFO] - Epoch: 98/130
2023-09-20 21:10:35,801 [INFO] - Training epoch stats:     Loss: 1.3837 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:15:05,043 [INFO] - Validation epoch stats:   Loss: 2.2839 - Binary-Cell-Dice: 0.7736 - Binary-Cell-Jacard: 0.6885 - bPQ-Score: 0.6042 - mPQ-Score: 0.4566 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:15:22,776 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:15:22,777 [INFO] - Epoch: 99/130
2023-09-20 21:17:21,367 [INFO] - Training epoch stats:     Loss: 1.3878 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:22:03,297 [INFO] - Validation epoch stats:   Loss: 2.2835 - Binary-Cell-Dice: 0.7639 - Binary-Cell-Jacard: 0.6777 - bPQ-Score: 0.5989 - mPQ-Score: 0.4538 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:22:31,205 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:22:31,206 [INFO] - Epoch: 100/130
2023-09-20 21:24:32,215 [INFO] - Training epoch stats:     Loss: 1.3654 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:30:13,193 [INFO] - Validation epoch stats:   Loss: 2.2611 - Binary-Cell-Dice: 0.7776 - Binary-Cell-Jacard: 0.6935 - bPQ-Score: 0.6109 - mPQ-Score: 0.4663 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:30:13,197 [INFO] - New best model - save checkpoint
2023-09-20 21:30:50,293 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:30:50,293 [INFO] - Epoch: 101/130
2023-09-20 21:32:47,723 [INFO] - Training epoch stats:     Loss: 1.3781 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:38:00,058 [INFO] - Validation epoch stats:   Loss: 2.2487 - Binary-Cell-Dice: 0.7779 - Binary-Cell-Jacard: 0.6943 - bPQ-Score: 0.6103 - mPQ-Score: 0.4653 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:38:26,160 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:38:26,160 [INFO] - Epoch: 102/130
2023-09-20 21:40:32,088 [INFO] - Training epoch stats:     Loss: 1.3638 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:44:44,178 [INFO] - Validation epoch stats:   Loss: 2.2309 - Binary-Cell-Dice: 0.7753 - Binary-Cell-Jacard: 0.6916 - bPQ-Score: 0.6071 - mPQ-Score: 0.4605 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:45:02,584 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:45:02,584 [INFO] - Epoch: 103/130
2023-09-20 21:46:59,780 [INFO] - Training epoch stats:     Loss: 1.3298 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:52:01,919 [INFO] - Validation epoch stats:   Loss: 2.1676 - Binary-Cell-Dice: 0.7774 - Binary-Cell-Jacard: 0.6929 - bPQ-Score: 0.6054 - mPQ-Score: 0.4612 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:52:20,505 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:52:20,506 [INFO] - Epoch: 104/130
2023-09-20 21:54:22,384 [INFO] - Training epoch stats:     Loss: 1.4265 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:58:52,075 [INFO] - Validation epoch stats:   Loss: 2.2319 - Binary-Cell-Dice: 0.7709 - Binary-Cell-Jacard: 0.6849 - bPQ-Score: 0.6027 - mPQ-Score: 0.4578 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:59:18,160 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 21:59:18,160 [INFO] - Epoch: 105/130
2023-09-20 22:01:19,837 [INFO] - Training epoch stats:     Loss: 1.3080 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:05:42,569 [INFO] - Validation epoch stats:   Loss: 2.3080 - Binary-Cell-Dice: 0.7744 - Binary-Cell-Jacard: 0.6898 - bPQ-Score: 0.6063 - mPQ-Score: 0.4597 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:06:30,684 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:06:30,685 [INFO] - Epoch: 106/130
2023-09-20 22:08:32,530 [INFO] - Training epoch stats:     Loss: 1.4007 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:13:54,437 [INFO] - Validation epoch stats:   Loss: 2.2665 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6896 - bPQ-Score: 0.6059 - mPQ-Score: 0.4590 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:14:16,501 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:14:16,502 [INFO] - Epoch: 107/130
2023-09-20 22:16:13,673 [INFO] - Training epoch stats:     Loss: 1.3775 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:22:48,345 [INFO] - Validation epoch stats:   Loss: 2.2347 - Binary-Cell-Dice: 0.7785 - Binary-Cell-Jacard: 0.6957 - bPQ-Score: 0.6098 - mPQ-Score: 0.4629 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:23:05,223 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:23:05,223 [INFO] - Epoch: 108/130
2023-09-20 22:25:02,426 [INFO] - Training epoch stats:     Loss: 1.4698 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:29:29,574 [INFO] - Validation epoch stats:   Loss: 2.3091 - Binary-Cell-Dice: 0.7775 - Binary-Cell-Jacard: 0.6938 - bPQ-Score: 0.6057 - mPQ-Score: 0.4588 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:29:48,052 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:29:48,053 [INFO] - Epoch: 109/130
2023-09-20 22:31:45,445 [INFO] - Training epoch stats:     Loss: 1.4333 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:35:48,203 [INFO] - Validation epoch stats:   Loss: 2.3211 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.6005 - mPQ-Score: 0.4514 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:36:16,740 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:36:16,741 [INFO] - Epoch: 110/130
2023-09-20 22:38:17,142 [INFO] - Training epoch stats:     Loss: 1.3295 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:42:33,854 [INFO] - Validation epoch stats:   Loss: 2.3020 - Binary-Cell-Dice: 0.7709 - Binary-Cell-Jacard: 0.6859 - bPQ-Score: 0.6035 - mPQ-Score: 0.4527 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:42:51,982 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:42:51,982 [INFO] - Epoch: 111/130
2023-09-20 22:44:49,750 [INFO] - Training epoch stats:     Loss: 1.4154 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:49:42,518 [INFO] - Validation epoch stats:   Loss: 2.2990 - Binary-Cell-Dice: 0.7787 - Binary-Cell-Jacard: 0.6936 - bPQ-Score: 0.6102 - mPQ-Score: 0.4625 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:50:12,890 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:50:12,891 [INFO] - Epoch: 112/130
2023-09-20 22:52:13,921 [INFO] - Training epoch stats:     Loss: 1.3402 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:58:21,509 [INFO] - Validation epoch stats:   Loss: 2.2674 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6895 - bPQ-Score: 0.6043 - mPQ-Score: 0.4552 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:58:38,876 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 22:58:38,876 [INFO] - Epoch: 113/130
2023-09-20 23:00:36,368 [INFO] - Training epoch stats:     Loss: 1.3638 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:06:04,752 [INFO] - Validation epoch stats:   Loss: 2.2594 - Binary-Cell-Dice: 0.7749 - Binary-Cell-Jacard: 0.6909 - bPQ-Score: 0.6052 - mPQ-Score: 0.4575 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:06:28,084 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 23:06:28,084 [INFO] - Epoch: 114/130
2023-09-20 23:08:26,780 [INFO] - Training epoch stats:     Loss: 1.3230 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:13:04,647 [INFO] - Validation epoch stats:   Loss: 2.2475 - Binary-Cell-Dice: 0.7722 - Binary-Cell-Jacard: 0.6863 - bPQ-Score: 0.6046 - mPQ-Score: 0.4570 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:13:22,289 [DEBUG] - Old lr: 0.000025 - New lr: 0.000013
2023-09-20 23:13:22,290 [INFO] - Epoch: 115/130
2023-09-20 23:15:19,658 [INFO] - Training epoch stats:     Loss: 1.3237 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:20:07,934 [INFO] - Validation epoch stats:   Loss: 2.2398 - Binary-Cell-Dice: 0.7745 - Binary-Cell-Jacard: 0.6908 - bPQ-Score: 0.6074 - mPQ-Score: 0.4582 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:20:24,998 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:20:24,998 [INFO] - Epoch: 116/130
2023-09-20 23:22:23,179 [INFO] - Training epoch stats:     Loss: 1.3084 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:27:21,039 [INFO] - Validation epoch stats:   Loss: 2.2287 - Binary-Cell-Dice: 0.7775 - Binary-Cell-Jacard: 0.6941 - bPQ-Score: 0.6082 - mPQ-Score: 0.4628 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:27:38,359 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:27:38,359 [INFO] - Epoch: 117/130
2023-09-20 23:29:35,780 [INFO] - Training epoch stats:     Loss: 1.2859 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:34:31,234 [INFO] - Validation epoch stats:   Loss: 2.2077 - Binary-Cell-Dice: 0.7742 - Binary-Cell-Jacard: 0.6902 - bPQ-Score: 0.6069 - mPQ-Score: 0.4575 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:34:48,903 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:34:48,904 [INFO] - Epoch: 118/130
2023-09-20 23:36:46,624 [INFO] - Training epoch stats:     Loss: 1.3020 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:40:54,499 [INFO] - Validation epoch stats:   Loss: 2.2444 - Binary-Cell-Dice: 0.7758 - Binary-Cell-Jacard: 0.6920 - bPQ-Score: 0.6097 - mPQ-Score: 0.4633 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:41:11,844 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:41:11,845 [INFO] - Epoch: 119/130
2023-09-20 23:43:09,386 [INFO] - Training epoch stats:     Loss: 1.2756 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:47:39,490 [INFO] - Validation epoch stats:   Loss: 2.2114 - Binary-Cell-Dice: 0.7762 - Binary-Cell-Jacard: 0.6924 - bPQ-Score: 0.6075 - mPQ-Score: 0.4628 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:47:55,551 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:47:55,552 [INFO] - Epoch: 120/130
2023-09-20 23:49:52,908 [INFO] - Training epoch stats:     Loss: 1.2839 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:54:47,897 [INFO] - Validation epoch stats:   Loss: 2.2121 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6912 - bPQ-Score: 0.6046 - mPQ-Score: 0.4617 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:55:05,152 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 23:55:05,153 [INFO] - Epoch: 121/130
2023-09-20 23:57:03,193 [INFO] - Training epoch stats:     Loss: 1.2860 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:02:06,568 [INFO] - Validation epoch stats:   Loss: 2.1746 - Binary-Cell-Dice: 0.7770 - Binary-Cell-Jacard: 0.6929 - bPQ-Score: 0.6076 - mPQ-Score: 0.4649 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:02:25,482 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-21 00:02:25,482 [INFO] - Epoch: 122/130
2023-09-21 00:04:23,191 [INFO] - Training epoch stats:     Loss: 1.3065 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:09:17,580 [INFO] - Validation epoch stats:   Loss: 2.2278 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6908 - bPQ-Score: 0.6074 - mPQ-Score: 0.4606 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:09:34,735 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-21 00:09:34,736 [INFO] - Epoch: 123/130
2023-09-21 00:11:32,339 [INFO] - Training epoch stats:     Loss: 1.3184 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:15:58,845 [INFO] - Validation epoch stats:   Loss: 2.1969 - Binary-Cell-Dice: 0.7779 - Binary-Cell-Jacard: 0.6944 - bPQ-Score: 0.6101 - mPQ-Score: 0.4639 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:16:15,792 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-21 00:16:15,792 [INFO] - Epoch: 124/130
2023-09-21 00:18:13,715 [INFO] - Training epoch stats:     Loss: 1.2939 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:22:37,537 [INFO] - Validation epoch stats:   Loss: 2.1902 - Binary-Cell-Dice: 0.7736 - Binary-Cell-Jacard: 0.6895 - bPQ-Score: 0.6051 - mPQ-Score: 0.4612 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:22:54,553 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-21 00:22:54,553 [INFO] - Epoch: 125/130
2023-09-21 00:24:51,586 [INFO] - Training epoch stats:     Loss: 1.2598 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:29:48,308 [INFO] - Validation epoch stats:   Loss: 2.2466 - Binary-Cell-Dice: 0.7766 - Binary-Cell-Jacard: 0.6929 - bPQ-Score: 0.6096 - mPQ-Score: 0.4594 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:30:05,080 [DEBUG] - Old lr: 0.000013 - New lr: 0.000006
2023-09-21 00:30:05,081 [INFO] - Epoch: 126/130
2023-09-21 00:32:02,911 [INFO] - Training epoch stats:     Loss: 1.2992 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:36:58,741 [INFO] - Validation epoch stats:   Loss: 2.2396 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6916 - bPQ-Score: 0.6074 - mPQ-Score: 0.4607 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:37:15,985 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-21 00:37:15,985 [INFO] - Epoch: 127/130
2023-09-21 00:39:13,177 [INFO] - Training epoch stats:     Loss: 1.2665 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:43:37,785 [INFO] - Validation epoch stats:   Loss: 2.2220 - Binary-Cell-Dice: 0.7764 - Binary-Cell-Jacard: 0.6922 - bPQ-Score: 0.6080 - mPQ-Score: 0.4653 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:43:55,234 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-21 00:43:55,235 [INFO] - Epoch: 128/130
2023-09-21 00:45:52,831 [INFO] - Training epoch stats:     Loss: 1.2394 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:50:16,986 [INFO] - Validation epoch stats:   Loss: 2.1795 - Binary-Cell-Dice: 0.7757 - Binary-Cell-Jacard: 0.6922 - bPQ-Score: 0.6080 - mPQ-Score: 0.4655 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:50:34,720 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-21 00:50:34,720 [INFO] - Epoch: 129/130
2023-09-21 00:52:31,989 [INFO] - Training epoch stats:     Loss: 1.2342 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:56:45,566 [INFO] - Validation epoch stats:   Loss: 2.1719 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6869 - bPQ-Score: 0.6018 - mPQ-Score: 0.4607 - Tissue-MC-Acc.: 0.0000
2023-09-21 00:57:03,287 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-21 00:57:03,287 [INFO] - Epoch: 130/130
2023-09-21 00:59:01,139 [INFO] - Training epoch stats:     Loss: 1.2712 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-21 01:04:56,634 [INFO] - Validation epoch stats:   Loss: 2.1756 - Binary-Cell-Dice: 0.7734 - Binary-Cell-Jacard: 0.6902 - bPQ-Score: 0.6076 - mPQ-Score: 0.4622 - Tissue-MC-Acc.: 0.0000
2023-09-21 01:05:15,313 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-21 01:05:15,316 [INFO] -
