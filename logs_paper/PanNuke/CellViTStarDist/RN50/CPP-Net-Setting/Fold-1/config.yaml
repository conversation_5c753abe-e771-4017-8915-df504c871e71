logging:
  mode: offline
  project: Cell-Segmentation
  notes: RN50
  log_comment: RN50-Fold-1
  tags:
  - Fold-1
  - RN50
  wandb_dir: ./CellViT/results/PanNuke

  level: Debug
  group: RN50-CPP-Net-Setting


random_seed: 19
gpu: 0
data:
  dataset: PanNuke
  dataset_path: ./PanNuke/digitalhistologyhub
  train_folds:
  - 0
  val_folds:
  - 1
  test_folds:
  - 2
  num_nuclei_classes: 6
  num_tissue_classes: 19
model:
  backbone: RN50
training:
  drop_rate: 0
  attn_drop_rate: 0.1
  drop_path_rate: 0.1
  batch_size: 16
  epochs: 130
  optimizer: Adam
  early_stopping_patience: 130
  scheduler:
    scheduler_type: reducelronplateau
  optimizer_hyperparameter:
    lr: 0.0001
  unfreeze_epoch: 0
  sampling_strategy: random
  eval_every: 1
  mixed_precision: true
transformations:
  randomrotate90:
    p: 0.5
  horizontalflip:
    p: 0.5
  verticalflip:
    p: 0.5
  normalize:
    mean:
    - 0.5
    - 0.5
    - 0.5
    std:
    - 0.5
    - 0.5
    - 0.5
eval_checkpoint: latest_checkpoint.pth
run_sweep: false
agent: null
dataset_config:
  tissue_types:
    Adrenal_gland: 0
    Bile-duct: 1
    Bladder: 2
    Breast: 3
    Cervix: 4
    Colon: 5
    Esophagus: 6
    HeadNeck: 7
    Kidney: 8
    Liver: 9
    Lung: 10
    Ovarian: 11
    Pancreatic: 12
    Prostate: 13
    Skin: 14
    Stomach: 15
    Testis: 16
    Thyroid: 17
    Uterus: 18
  nuclei_types:
    Background: 0
    Neoplastic: 1
    Inflammatory: 2
    Connective: 3
    Dead: 4
    Epithelial: 5
