2023-09-20 06:08:11,922 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-20 06:08:11,985 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f04b8e8e700>]
2023-09-20 06:08:11,986 [INFO] - Using GPU: cuda:0
2023-09-20 06:08:11,986 [INFO] - Using device: cuda:0
2023-09-20 06:08:11,987 [INFO] - Loss functions:
2023-09-20 06:08:11,987 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 1}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON><PERSON>(), 'weight': 1}}}
2023-09-20 06:08:18,493 [INFO] -
Model: StarDistRN50(
  (encoder): ResNet(
    (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (relu): ReLU(inplace=True)
    (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (layer1): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer2): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer3): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (4): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (5): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer4): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
  )
  (up1): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4): Upsample(scale_factor=2.0, mode=bilinear)
  (features): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (out_prob): outconv(
    (conv): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (out_ray): outconv(
    (conv): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
  )
  (up1_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4_seg): Upsample(scale_factor=2.0, mode=bilinear)
  (out_seg): outconv(
    (conv): Conv2d(256, 6, kernel_size=(1, 1), stride=(1, 1))
  )
  (final_activation_ray): ReLU()
)
2023-09-20 06:08:19,724 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
StarDistRN50                                  [1, 6, 256, 256]          --
├─ResNet: 1-1                                 [1, 256, 128, 128]        --
│    └─Conv2d: 2-1                            [1, 64, 128, 128]         9,408
│    └─BatchNorm2d: 2-2                       [1, 64, 128, 128]         128
│    └─ReLU: 2-3                              [1, 64, 128, 128]         --
│    └─Sequential: 2-4                        [1, 256, 128, 128]        --
│    │    └─Bottleneck: 3-1                   [1, 256, 128, 128]        75,008
│    │    └─Bottleneck: 3-2                   [1, 256, 128, 128]        70,400
│    │    └─Bottleneck: 3-3                   [1, 256, 128, 128]        70,400
│    └─Sequential: 2-5                        [1, 512, 64, 64]          --
│    │    └─Bottleneck: 3-4                   [1, 512, 64, 64]          379,392
│    │    └─Bottleneck: 3-5                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-6                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-7                   [1, 512, 64, 64]          280,064
│    └─Sequential: 2-6                        [1, 1024, 32, 32]         --
│    │    └─Bottleneck: 3-8                   [1, 1024, 32, 32]         1,512,448
│    │    └─Bottleneck: 3-9                   [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-10                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-11                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-12                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-13                  [1, 1024, 32, 32]         1,117,184
│    └─Sequential: 2-7                        [1, 2048, 16, 16]         --
│    │    └─Bottleneck: 3-14                  [1, 2048, 16, 16]         6,039,552
│    │    └─Bottleneck: 3-15                  [1, 2048, 16, 16]         4,462,592
│    │    └─Bottleneck: 3-16                  [1, 2048, 16, 16]         4,462,592
├─up: 1-2                                     [1, 1024, 32, 32]         --
│    └─Upsample: 2-8                          [1, 2048, 32, 32]         --
│    └─double_conv: 2-9                       [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-17                  [1, 1024, 32, 32]         37,754,880
├─up: 1-3                                     [1, 512, 64, 64]          --
│    └─Upsample: 2-10                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-11                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-18                  [1, 512, 64, 64]          9,440,256
├─up: 1-4                                     [1, 256, 128, 128]        --
│    └─Upsample: 2-12                         [1, 512, 128, 128]        --
│    └─double_conv: 2-13                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-19                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-5                               [1, 256, 256, 256]        --
├─Conv2d: 1-6                                 [1, 256, 256, 256]        590,080
├─outconv: 1-7                                [1, 1, 256, 256]          --
│    └─Conv2d: 2-14                           [1, 1, 256, 256]          257
├─outconv: 1-8                                [1, 32, 256, 256]         --
│    └─Conv2d: 2-15                           [1, 32, 256, 256]         8,224
├─ReLU: 1-9                                   [1, 32, 256, 256]         --
├─up: 1-10                                    [1, 1024, 32, 32]         --
│    └─Upsample: 2-16                         [1, 2048, 32, 32]         --
│    └─double_conv: 2-17                      [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-20                  [1, 1024, 32, 32]         37,754,880
├─up: 1-11                                    [1, 512, 64, 64]          --
│    └─Upsample: 2-18                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-19                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-21                  [1, 512, 64, 64]          9,440,256
├─up: 1-12                                    [1, 256, 128, 128]        --
│    └─Upsample: 2-20                         [1, 512, 128, 128]        --
│    └─double_conv: 2-21                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-22                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-13                              [1, 256, 256, 256]        --
├─outconv: 1-14                               [1, 6, 256, 256]          --
│    └─Conv2d: 2-22                           [1, 6, 256, 256]          1,542
===============================================================================================
Total params: 123,220,071
Trainable params: 123,220,071
Non-trainable params: 0
Total mult-adds (G): 292.18
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1503.13
Params size (MB): 492.88
Estimated Total Size (MB): 1996.80
===============================================================================================
2023-09-20 06:08:37,543 [INFO] - Loaded Adam Optimizer with following hyperparameters:
2023-09-20 06:08:37,548 [INFO] - {'lr': 0.0001}
2023-09-20 06:08:37,549 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-20 06:08:51,727 [INFO] - Using RandomSampler
2023-09-20 06:08:51,733 [INFO] - Instantiate Trainer
2023-09-20 06:08:51,733 [INFO] - Calling Trainer Fit
2023-09-20 06:08:51,733 [INFO] - Starting training, total number of epochs: 130
2023-09-20 06:08:51,733 [INFO] - Epoch: 1/130
2023-09-20 06:11:27,498 [INFO] - Training epoch stats:     Loss: 4.7309 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:22:36,076 [INFO] - Validation epoch stats:   Loss: 3.9146 - Binary-Cell-Dice: 0.5259 - Binary-Cell-Jacard: 0.3935 - bPQ-Score: 0.0227 - mPQ-Score: 0.0155 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:22:36,084 [INFO] - New best model - save checkpoint
2023-09-20 06:24:19,976 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:24:19,984 [INFO] - Epoch: 2/130
2023-09-20 06:26:25,447 [INFO] - Training epoch stats:     Loss: 3.7204 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:31:53,515 [INFO] - Validation epoch stats:   Loss: 3.5078 - Binary-Cell-Dice: 0.5682 - Binary-Cell-Jacard: 0.4318 - bPQ-Score: 0.1638 - mPQ-Score: 0.1069 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:31:53,523 [INFO] - New best model - save checkpoint
2023-09-20 06:33:16,376 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:33:16,384 [INFO] - Epoch: 3/130
2023-09-20 06:35:22,467 [INFO] - Training epoch stats:     Loss: 3.5723 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:39:41,833 [INFO] - Validation epoch stats:   Loss: 3.3661 - Binary-Cell-Dice: 0.6567 - Binary-Cell-Jacard: 0.5334 - bPQ-Score: 0.3647 - mPQ-Score: 0.2400 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:39:41,923 [INFO] - New best model - save checkpoint
2023-09-20 06:40:52,449 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:40:52,450 [INFO] - Epoch: 4/130
2023-09-20 06:42:52,542 [INFO] - Training epoch stats:     Loss: 3.3957 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:46:57,554 [INFO] - Validation epoch stats:   Loss: 3.2313 - Binary-Cell-Dice: 0.6692 - Binary-Cell-Jacard: 0.5458 - bPQ-Score: 0.3523 - mPQ-Score: 0.2436 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:47:20,418 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:47:20,419 [INFO] - Epoch: 5/130
2023-09-20 06:49:19,962 [INFO] - Training epoch stats:     Loss: 3.2579 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:53:03,133 [INFO] - Validation epoch stats:   Loss: 3.3655 - Binary-Cell-Dice: 0.6229 - Binary-Cell-Jacard: 0.4938 - bPQ-Score: 0.3590 - mPQ-Score: 0.2161 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:53:37,806 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:53:37,806 [INFO] - Epoch: 6/130
2023-09-20 06:55:37,460 [INFO] - Training epoch stats:     Loss: 3.2249 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:01:11,241 [INFO] - Validation epoch stats:   Loss: 3.1135 - Binary-Cell-Dice: 0.7007 - Binary-Cell-Jacard: 0.5818 - bPQ-Score: 0.3959 - mPQ-Score: 0.2717 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:01:11,251 [INFO] - New best model - save checkpoint
2023-09-20 07:02:13,028 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:02:13,029 [INFO] - Epoch: 7/130
2023-09-20 07:04:17,246 [INFO] - Training epoch stats:     Loss: 3.1356 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:08:31,627 [INFO] - Validation epoch stats:   Loss: 3.1582 - Binary-Cell-Dice: 0.6621 - Binary-Cell-Jacard: 0.5406 - bPQ-Score: 0.4187 - mPQ-Score: 0.2794 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:08:31,636 [INFO] - New best model - save checkpoint
2023-09-20 07:09:52,038 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:09:52,039 [INFO] - Epoch: 8/130
2023-09-20 07:11:58,187 [INFO] - Training epoch stats:     Loss: 3.1008 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:16:55,599 [INFO] - Validation epoch stats:   Loss: 3.1071 - Binary-Cell-Dice: 0.7222 - Binary-Cell-Jacard: 0.6112 - bPQ-Score: 0.4457 - mPQ-Score: 0.3116 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:16:55,604 [INFO] - New best model - save checkpoint
2023-09-20 07:18:00,554 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:18:00,557 [INFO] - Epoch: 9/130
2023-09-20 07:20:03,472 [INFO] - Training epoch stats:     Loss: 3.0495 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:25:04,697 [INFO] - Validation epoch stats:   Loss: 3.0316 - Binary-Cell-Dice: 0.6799 - Binary-Cell-Jacard: 0.5557 - bPQ-Score: 0.4160 - mPQ-Score: 0.2976 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:25:40,731 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:25:40,732 [INFO] - Epoch: 10/130
2023-09-20 07:27:45,030 [INFO] - Training epoch stats:     Loss: 2.9608 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:33:45,541 [INFO] - Validation epoch stats:   Loss: 2.9983 - Binary-Cell-Dice: 0.7029 - Binary-Cell-Jacard: 0.5867 - bPQ-Score: 0.4398 - mPQ-Score: 0.3075 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:34:36,771 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:34:36,771 [INFO] - Epoch: 11/130
2023-09-20 07:36:41,764 [INFO] - Training epoch stats:     Loss: 2.9978 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:40:44,861 [INFO] - Validation epoch stats:   Loss: 3.0250 - Binary-Cell-Dice: 0.7219 - Binary-Cell-Jacard: 0.6168 - bPQ-Score: 0.5163 - mPQ-Score: 0.3586 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:40:44,871 [INFO] - New best model - save checkpoint
2023-09-20 07:42:02,317 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:42:02,417 [INFO] - Epoch: 12/130
2023-09-20 07:44:51,368 [INFO] - Training epoch stats:     Loss: 2.9353 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:49:34,163 [INFO] - Validation epoch stats:   Loss: 3.1260 - Binary-Cell-Dice: 0.7375 - Binary-Cell-Jacard: 0.6368 - bPQ-Score: 0.5130 - mPQ-Score: 0.3530 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:49:58,990 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:49:58,991 [INFO] - Epoch: 13/130
2023-09-20 07:52:23,299 [INFO] - Training epoch stats:     Loss: 2.9720 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:57:14,686 [INFO] - Validation epoch stats:   Loss: 3.0308 - Binary-Cell-Dice: 0.7293 - Binary-Cell-Jacard: 0.6258 - bPQ-Score: 0.5154 - mPQ-Score: 0.3573 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:57:56,508 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:57:56,508 [INFO] - Epoch: 14/130
2023-09-20 08:00:01,709 [INFO] - Training epoch stats:     Loss: 2.8315 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:03:53,624 [INFO] - Validation epoch stats:   Loss: 2.9293 - Binary-Cell-Dice: 0.6575 - Binary-Cell-Jacard: 0.5471 - bPQ-Score: 0.4832 - mPQ-Score: 0.3411 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:04:42,064 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:04:42,065 [INFO] - Epoch: 15/130
2023-09-20 08:07:00,089 [INFO] - Training epoch stats:     Loss: 2.8628 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:11:23,565 [INFO] - Validation epoch stats:   Loss: 2.9148 - Binary-Cell-Dice: 0.7427 - Binary-Cell-Jacard: 0.6402 - bPQ-Score: 0.5284 - mPQ-Score: 0.3762 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:11:23,575 [INFO] - New best model - save checkpoint
2023-09-20 08:12:47,836 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:12:47,837 [INFO] - Epoch: 16/130
2023-09-20 08:15:10,044 [INFO] - Training epoch stats:     Loss: 2.8205 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:19:12,294 [INFO] - Validation epoch stats:   Loss: 2.8542 - Binary-Cell-Dice: 0.7363 - Binary-Cell-Jacard: 0.6344 - bPQ-Score: 0.5414 - mPQ-Score: 0.3804 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:19:12,297 [INFO] - New best model - save checkpoint
2023-09-20 08:20:14,370 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:20:14,485 [INFO] - Epoch: 17/130
2023-09-20 08:22:43,637 [INFO] - Training epoch stats:     Loss: 2.8111 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:27:05,568 [INFO] - Validation epoch stats:   Loss: 3.0033 - Binary-Cell-Dice: 0.7383 - Binary-Cell-Jacard: 0.6356 - bPQ-Score: 0.5279 - mPQ-Score: 0.3539 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:27:42,412 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:27:42,412 [INFO] - Epoch: 18/130
2023-09-20 08:29:42,719 [INFO] - Training epoch stats:     Loss: 2.8208 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:34:23,158 [INFO] - Validation epoch stats:   Loss: 2.8289 - Binary-Cell-Dice: 0.7371 - Binary-Cell-Jacard: 0.6359 - bPQ-Score: 0.5329 - mPQ-Score: 0.3772 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:35:00,084 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:35:00,084 [INFO] - Epoch: 19/130
2023-09-20 08:37:03,052 [INFO] - Training epoch stats:     Loss: 2.7562 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:41:10,094 [INFO] - Validation epoch stats:   Loss: 2.8222 - Binary-Cell-Dice: 0.7423 - Binary-Cell-Jacard: 0.6399 - bPQ-Score: 0.5395 - mPQ-Score: 0.3920 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:41:27,923 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:41:27,924 [INFO] - Epoch: 20/130
2023-09-20 08:43:27,466 [INFO] - Training epoch stats:     Loss: 2.7326 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:47:40,516 [INFO] - Validation epoch stats:   Loss: 2.8392 - Binary-Cell-Dice: 0.7379 - Binary-Cell-Jacard: 0.6395 - bPQ-Score: 0.5392 - mPQ-Score: 0.3813 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:47:59,465 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:47:59,465 [INFO] - Epoch: 21/130
2023-09-20 08:49:59,088 [INFO] - Training epoch stats:     Loss: 2.8013 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:55:28,098 [INFO] - Validation epoch stats:   Loss: 2.7398 - Binary-Cell-Dice: 0.7051 - Binary-Cell-Jacard: 0.5981 - bPQ-Score: 0.5198 - mPQ-Score: 0.3840 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:55:59,310 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:55:59,310 [INFO] - Epoch: 22/130
2023-09-20 08:58:03,452 [INFO] - Training epoch stats:     Loss: 2.7344 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:03:13,964 [INFO] - Validation epoch stats:   Loss: 2.7340 - Binary-Cell-Dice: 0.7271 - Binary-Cell-Jacard: 0.6238 - bPQ-Score: 0.5273 - mPQ-Score: 0.3837 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:04:00,412 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:04:00,413 [INFO] - Epoch: 23/130
2023-09-20 09:06:00,033 [INFO] - Training epoch stats:     Loss: 2.7047 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:10:49,015 [INFO] - Validation epoch stats:   Loss: 2.7939 - Binary-Cell-Dice: 0.7500 - Binary-Cell-Jacard: 0.6523 - bPQ-Score: 0.5477 - mPQ-Score: 0.4031 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:10:49,025 [INFO] - New best model - save checkpoint
2023-09-20 09:11:53,065 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:11:53,412 [INFO] - Epoch: 24/130
2023-09-20 09:13:53,484 [INFO] - Training epoch stats:     Loss: 2.6922 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:18:10,691 [INFO] - Validation epoch stats:   Loss: 2.8873 - Binary-Cell-Dice: 0.7480 - Binary-Cell-Jacard: 0.6535 - bPQ-Score: 0.5663 - mPQ-Score: 0.4133 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:18:10,698 [INFO] - New best model - save checkpoint
2023-09-20 09:19:37,702 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:19:37,703 [INFO] - Epoch: 25/130
2023-09-20 09:21:42,053 [INFO] - Training epoch stats:     Loss: 2.6719 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:26:22,312 [INFO] - Validation epoch stats:   Loss: 2.9832 - Binary-Cell-Dice: 0.7546 - Binary-Cell-Jacard: 0.6586 - bPQ-Score: 0.5531 - mPQ-Score: 0.3844 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:26:47,477 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:26:47,478 [INFO] - Epoch: 26/130
2023-09-20 09:28:49,462 [INFO] - Training epoch stats:     Loss: 2.6631 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:33:03,463 [INFO] - Validation epoch stats:   Loss: 2.8502 - Binary-Cell-Dice: 0.7614 - Binary-Cell-Jacard: 0.6699 - bPQ-Score: 0.5702 - mPQ-Score: 0.4034 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:33:03,472 [INFO] - New best model - save checkpoint
2023-09-20 09:34:07,790 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:34:07,796 [INFO] - Epoch: 27/130
2023-09-20 09:36:08,457 [INFO] - Training epoch stats:     Loss: 2.6852 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:40:08,337 [INFO] - Validation epoch stats:   Loss: 2.7703 - Binary-Cell-Dice: 0.7505 - Binary-Cell-Jacard: 0.6537 - bPQ-Score: 0.5572 - mPQ-Score: 0.4044 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:40:38,662 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:40:38,663 [INFO] - Epoch: 28/130
2023-09-20 09:42:42,627 [INFO] - Training epoch stats:     Loss: 2.6846 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:46:44,017 [INFO] - Validation epoch stats:   Loss: 2.8322 - Binary-Cell-Dice: 0.7589 - Binary-Cell-Jacard: 0.6655 - bPQ-Score: 0.5637 - mPQ-Score: 0.4097 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:47:10,716 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:47:10,716 [INFO] - Epoch: 29/130
2023-09-20 09:49:10,125 [INFO] - Training epoch stats:     Loss: 2.6295 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:53:10,368 [INFO] - Validation epoch stats:   Loss: 2.9734 - Binary-Cell-Dice: 0.7638 - Binary-Cell-Jacard: 0.6728 - bPQ-Score: 0.5768 - mPQ-Score: 0.4006 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:53:10,372 [INFO] - New best model - save checkpoint
2023-09-20 09:53:44,118 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:53:44,119 [INFO] - Epoch: 30/130
2023-09-20 09:55:44,552 [INFO] - Training epoch stats:     Loss: 2.6440 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:00:28,294 [INFO] - Validation epoch stats:   Loss: 2.7221 - Binary-Cell-Dice: 0.7596 - Binary-Cell-Jacard: 0.6660 - bPQ-Score: 0.5691 - mPQ-Score: 0.4208 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:00:50,970 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:00:50,971 [INFO] - Epoch: 31/130
2023-09-20 10:02:52,393 [INFO] - Training epoch stats:     Loss: 2.6080 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:07:07,605 [INFO] - Validation epoch stats:   Loss: 2.6868 - Binary-Cell-Dice: 0.7628 - Binary-Cell-Jacard: 0.6714 - bPQ-Score: 0.5761 - mPQ-Score: 0.4226 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:07:36,548 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:07:36,549 [INFO] - Epoch: 32/130
2023-09-20 10:09:36,384 [INFO] - Training epoch stats:     Loss: 2.5706 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:13:48,902 [INFO] - Validation epoch stats:   Loss: 2.7345 - Binary-Cell-Dice: 0.7526 - Binary-Cell-Jacard: 0.6612 - bPQ-Score: 0.5720 - mPQ-Score: 0.4168 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:14:06,561 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:14:06,561 [INFO] - Epoch: 33/130
2023-09-20 10:16:06,857 [INFO] - Training epoch stats:     Loss: 2.6281 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:20:29,740 [INFO] - Validation epoch stats:   Loss: 2.7652 - Binary-Cell-Dice: 0.7647 - Binary-Cell-Jacard: 0.6759 - bPQ-Score: 0.5719 - mPQ-Score: 0.4094 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:21:04,416 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:21:04,416 [INFO] - Epoch: 34/130
2023-09-20 10:23:09,137 [INFO] - Training epoch stats:     Loss: 2.5926 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:27:22,478 [INFO] - Validation epoch stats:   Loss: 2.7452 - Binary-Cell-Dice: 0.7432 - Binary-Cell-Jacard: 0.6471 - bPQ-Score: 0.5544 - mPQ-Score: 0.3965 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:27:40,863 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:27:40,864 [INFO] - Epoch: 35/130
2023-09-20 10:29:40,614 [INFO] - Training epoch stats:     Loss: 2.5123 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:33:52,997 [INFO] - Validation epoch stats:   Loss: 2.7066 - Binary-Cell-Dice: 0.7456 - Binary-Cell-Jacard: 0.6552 - bPQ-Score: 0.5715 - mPQ-Score: 0.4177 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:34:09,852 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:34:09,852 [INFO] - Epoch: 36/130
2023-09-20 10:36:10,754 [INFO] - Training epoch stats:     Loss: 2.5595 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:40:11,006 [INFO] - Validation epoch stats:   Loss: 2.7508 - Binary-Cell-Dice: 0.7530 - Binary-Cell-Jacard: 0.6596 - bPQ-Score: 0.5763 - mPQ-Score: 0.4259 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:40:27,868 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:40:27,869 [INFO] - Epoch: 37/130
2023-09-20 10:42:26,792 [INFO] - Training epoch stats:     Loss: 2.5705 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:45:59,787 [INFO] - Validation epoch stats:   Loss: 2.7996 - Binary-Cell-Dice: 0.7319 - Binary-Cell-Jacard: 0.6283 - bPQ-Score: 0.5442 - mPQ-Score: 0.4031 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:46:17,263 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:46:17,264 [INFO] - Epoch: 38/130
2023-09-20 10:48:15,686 [INFO] - Training epoch stats:     Loss: 2.5164 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:52:10,972 [INFO] - Validation epoch stats:   Loss: 2.6858 - Binary-Cell-Dice: 0.7617 - Binary-Cell-Jacard: 0.6707 - bPQ-Score: 0.5800 - mPQ-Score: 0.4280 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:52:11,223 [INFO] - New best model - save checkpoint
2023-09-20 10:52:44,291 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:52:44,291 [INFO] - Epoch: 39/130
2023-09-20 10:54:42,018 [INFO] - Training epoch stats:     Loss: 2.5445 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:58:40,332 [INFO] - Validation epoch stats:   Loss: 2.9049 - Binary-Cell-Dice: 0.7458 - Binary-Cell-Jacard: 0.6497 - bPQ-Score: 0.5609 - mPQ-Score: 0.3897 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:59:10,244 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:59:10,246 [INFO] - Epoch: 40/130
2023-09-20 11:01:13,496 [INFO] - Training epoch stats:     Loss: 2.5494 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:05:07,180 [INFO] - Validation epoch stats:   Loss: 2.6916 - Binary-Cell-Dice: 0.7589 - Binary-Cell-Jacard: 0.6683 - bPQ-Score: 0.5707 - mPQ-Score: 0.4283 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:05:59,239 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:05:59,240 [INFO] - Epoch: 41/130
2023-09-20 11:08:04,486 [INFO] - Training epoch stats:     Loss: 2.4684 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:12:10,168 [INFO] - Validation epoch stats:   Loss: 2.7602 - Binary-Cell-Dice: 0.7607 - Binary-Cell-Jacard: 0.6723 - bPQ-Score: 0.5835 - mPQ-Score: 0.4265 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:12:10,178 [INFO] - New best model - save checkpoint
2023-09-20 11:13:08,079 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:13:08,079 [INFO] - Epoch: 42/130
2023-09-20 11:15:11,403 [INFO] - Training epoch stats:     Loss: 2.4873 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:19:18,161 [INFO] - Validation epoch stats:   Loss: 2.7564 - Binary-Cell-Dice: 0.7629 - Binary-Cell-Jacard: 0.6747 - bPQ-Score: 0.5862 - mPQ-Score: 0.4210 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:19:18,369 [INFO] - New best model - save checkpoint
2023-09-20 11:19:53,608 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:19:53,608 [INFO] - Epoch: 43/130
2023-09-20 11:21:52,949 [INFO] - Training epoch stats:     Loss: 2.5193 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:26:02,999 [INFO] - Validation epoch stats:   Loss: 2.7014 - Binary-Cell-Dice: 0.7540 - Binary-Cell-Jacard: 0.6589 - bPQ-Score: 0.5705 - mPQ-Score: 0.4219 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:26:32,311 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:26:32,312 [INFO] - Epoch: 44/130
2023-09-20 11:28:29,045 [INFO] - Training epoch stats:     Loss: 2.3976 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:32:49,589 [INFO] - Validation epoch stats:   Loss: 2.7288 - Binary-Cell-Dice: 0.7620 - Binary-Cell-Jacard: 0.6728 - bPQ-Score: 0.5906 - mPQ-Score: 0.4290 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:32:49,593 [INFO] - New best model - save checkpoint
2023-09-20 11:33:24,387 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:33:24,387 [INFO] - Epoch: 45/130
2023-09-20 11:35:36,646 [INFO] - Training epoch stats:     Loss: 2.4245 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:40:35,816 [INFO] - Validation epoch stats:   Loss: 2.6314 - Binary-Cell-Dice: 0.7644 - Binary-Cell-Jacard: 0.6717 - bPQ-Score: 0.5797 - mPQ-Score: 0.4339 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:41:09,437 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:41:09,437 [INFO] - Epoch: 46/130
2023-09-20 11:43:20,058 [INFO] - Training epoch stats:     Loss: 2.4032 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:48:08,403 [INFO] - Validation epoch stats:   Loss: 2.6687 - Binary-Cell-Dice: 0.7485 - Binary-Cell-Jacard: 0.6517 - bPQ-Score: 0.5647 - mPQ-Score: 0.4272 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:48:52,169 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:48:52,170 [INFO] - Epoch: 47/130
2023-09-20 11:50:55,005 [INFO] - Training epoch stats:     Loss: 2.4187 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:55:20,738 [INFO] - Validation epoch stats:   Loss: 2.6107 - Binary-Cell-Dice: 0.7561 - Binary-Cell-Jacard: 0.6632 - bPQ-Score: 0.5734 - mPQ-Score: 0.4307 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:55:52,552 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:55:52,553 [INFO] - Epoch: 48/130
2023-09-20 11:58:49,801 [INFO] - Training epoch stats:     Loss: 2.4537 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:02:53,870 [INFO] - Validation epoch stats:   Loss: 2.7533 - Binary-Cell-Dice: 0.7519 - Binary-Cell-Jacard: 0.6628 - bPQ-Score: 0.5777 - mPQ-Score: 0.4175 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:03:35,824 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:03:35,824 [INFO] - Epoch: 49/130
2023-09-20 12:05:39,046 [INFO] - Training epoch stats:     Loss: 2.4399 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:09:51,450 [INFO] - Validation epoch stats:   Loss: 2.6482 - Binary-Cell-Dice: 0.7637 - Binary-Cell-Jacard: 0.6771 - bPQ-Score: 0.5837 - mPQ-Score: 0.4344 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:10:35,603 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:10:35,604 [INFO] - Epoch: 50/130
2023-09-20 12:12:40,201 [INFO] - Training epoch stats:     Loss: 2.3832 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:18:00,358 [INFO] - Validation epoch stats:   Loss: 2.5931 - Binary-Cell-Dice: 0.7634 - Binary-Cell-Jacard: 0.6722 - bPQ-Score: 0.5745 - mPQ-Score: 0.4279 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:18:19,922 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:18:19,923 [INFO] - Epoch: 51/130
2023-09-20 12:20:19,058 [INFO] - Training epoch stats:     Loss: 2.3480 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:24:58,739 [INFO] - Validation epoch stats:   Loss: 2.7412 - Binary-Cell-Dice: 0.7469 - Binary-Cell-Jacard: 0.6495 - bPQ-Score: 0.5648 - mPQ-Score: 0.4005 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:25:16,689 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:25:16,689 [INFO] - Epoch: 52/130
2023-09-20 12:27:15,674 [INFO] - Training epoch stats:     Loss: 2.3528 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:32:00,509 [INFO] - Validation epoch stats:   Loss: 2.8864 - Binary-Cell-Dice: 0.7615 - Binary-Cell-Jacard: 0.6723 - bPQ-Score: 0.5860 - mPQ-Score: 0.4195 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:32:36,419 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:32:36,420 [INFO] - Epoch: 53/130
2023-09-20 12:35:07,746 [INFO] - Training epoch stats:     Loss: 2.3873 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:40:02,227 [INFO] - Validation epoch stats:   Loss: 2.6102 - Binary-Cell-Dice: 0.7662 - Binary-Cell-Jacard: 0.6792 - bPQ-Score: 0.5882 - mPQ-Score: 0.4438 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:41:22,105 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:41:22,106 [INFO] - Epoch: 54/130
2023-09-20 12:43:48,223 [INFO] - Training epoch stats:     Loss: 2.3898 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:48:49,741 [INFO] - Validation epoch stats:   Loss: 2.7209 - Binary-Cell-Dice: 0.7689 - Binary-Cell-Jacard: 0.6818 - bPQ-Score: 0.5943 - mPQ-Score: 0.4416 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:48:49,827 [INFO] - New best model - save checkpoint
2023-09-20 12:50:20,675 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:50:20,675 [INFO] - Epoch: 55/130
2023-09-20 12:52:21,554 [INFO] - Training epoch stats:     Loss: 2.4893 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:57:00,280 [INFO] - Validation epoch stats:   Loss: 2.7730 - Binary-Cell-Dice: 0.7649 - Binary-Cell-Jacard: 0.6784 - bPQ-Score: 0.5897 - mPQ-Score: 0.4308 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:57:54,479 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:57:54,480 [INFO] - Epoch: 56/130
2023-09-20 12:59:53,666 [INFO] - Training epoch stats:     Loss: 2.3941 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:04:40,348 [INFO] - Validation epoch stats:   Loss: 2.6750 - Binary-Cell-Dice: 0.7719 - Binary-Cell-Jacard: 0.6820 - bPQ-Score: 0.5858 - mPQ-Score: 0.4326 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:06:14,638 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:06:14,645 [INFO] - Epoch: 57/130
2023-09-20 13:08:14,666 [INFO] - Training epoch stats:     Loss: 2.3387 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:12:45,352 [INFO] - Validation epoch stats:   Loss: 2.6443 - Binary-Cell-Dice: 0.7683 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.5957 - mPQ-Score: 0.4459 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:12:45,361 [INFO] - New best model - save checkpoint
2023-09-20 13:14:28,275 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:14:28,278 [INFO] - Epoch: 58/130
2023-09-20 13:16:27,045 [INFO] - Training epoch stats:     Loss: 2.3677 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:21:13,706 [INFO] - Validation epoch stats:   Loss: 2.6679 - Binary-Cell-Dice: 0.7565 - Binary-Cell-Jacard: 0.6649 - bPQ-Score: 0.5760 - mPQ-Score: 0.4272 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:22:50,220 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:22:50,339 [INFO] - Epoch: 59/130
2023-09-20 13:25:20,234 [INFO] - Training epoch stats:     Loss: 2.3728 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:29:41,464 [INFO] - Validation epoch stats:   Loss: 2.6337 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6819 - bPQ-Score: 0.5897 - mPQ-Score: 0.4360 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:30:21,545 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:30:21,545 [INFO] - Epoch: 60/130
2023-09-20 13:32:32,692 [INFO] - Training epoch stats:     Loss: 2.3429 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:38:28,954 [INFO] - Validation epoch stats:   Loss: 2.6172 - Binary-Cell-Dice: 0.7697 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.5900 - mPQ-Score: 0.4447 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:39:31,291 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:39:31,298 [INFO] - Epoch: 61/130
2023-09-20 13:41:37,040 [INFO] - Training epoch stats:     Loss: 2.2849 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:49:25,751 [INFO] - Validation epoch stats:   Loss: 2.7476 - Binary-Cell-Dice: 0.7508 - Binary-Cell-Jacard: 0.6530 - bPQ-Score: 0.5672 - mPQ-Score: 0.4154 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:50:14,761 [DEBUG] - Old lr: 0.000100 - New lr: 0.000050
2023-09-20 13:50:14,762 [INFO] - Epoch: 62/130
2023-09-20 13:52:19,702 [INFO] - Training epoch stats:     Loss: 2.2384 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:58:35,637 [INFO] - Validation epoch stats:   Loss: 2.5957 - Binary-Cell-Dice: 0.7702 - Binary-Cell-Jacard: 0.6840 - bPQ-Score: 0.5985 - mPQ-Score: 0.4484 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:58:35,654 [INFO] - New best model - save checkpoint
2023-09-20 13:59:59,596 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 13:59:59,596 [INFO] - Epoch: 63/130
2023-09-20 14:02:18,134 [INFO] - Training epoch stats:     Loss: 2.1701 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:07:56,685 [INFO] - Validation epoch stats:   Loss: 2.5541 - Binary-Cell-Dice: 0.7712 - Binary-Cell-Jacard: 0.6852 - bPQ-Score: 0.5960 - mPQ-Score: 0.4566 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:08:45,123 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:08:45,124 [INFO] - Epoch: 64/130
2023-09-20 14:10:59,500 [INFO] - Training epoch stats:     Loss: 2.1664 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:16:43,564 [INFO] - Validation epoch stats:   Loss: 2.5710 - Binary-Cell-Dice: 0.7774 - Binary-Cell-Jacard: 0.6947 - bPQ-Score: 0.6050 - mPQ-Score: 0.4606 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:16:43,577 [INFO] - New best model - save checkpoint
2023-09-20 14:18:14,925 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:18:14,927 [INFO] - Epoch: 65/130
2023-09-20 14:20:13,541 [INFO] - Training epoch stats:     Loss: 2.1534 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:24:32,526 [INFO] - Validation epoch stats:   Loss: 2.5777 - Binary-Cell-Dice: 0.7750 - Binary-Cell-Jacard: 0.6915 - bPQ-Score: 0.6012 - mPQ-Score: 0.4580 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:25:50,820 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:25:50,909 [INFO] - Epoch: 66/130
2023-09-20 14:28:24,421 [INFO] - Training epoch stats:     Loss: 1.8844 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:32:45,363 [INFO] - Validation epoch stats:   Loss: 2.3866 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6894 - bPQ-Score: 0.6053 - mPQ-Score: 0.4497 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:32:45,775 [INFO] - New best model - save checkpoint
2023-09-20 14:35:54,744 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:35:54,752 [INFO] - Epoch: 67/130
2023-09-20 14:37:57,175 [INFO] - Training epoch stats:     Loss: 1.8622 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:43:02,263 [INFO] - Validation epoch stats:   Loss: 2.3301 - Binary-Cell-Dice: 0.7635 - Binary-Cell-Jacard: 0.6755 - bPQ-Score: 0.5925 - mPQ-Score: 0.4468 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:44:13,435 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:44:13,436 [INFO] - Epoch: 68/130
2023-09-20 14:46:31,642 [INFO] - Training epoch stats:     Loss: 1.8436 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:51:02,972 [INFO] - Validation epoch stats:   Loss: 2.3419 - Binary-Cell-Dice: 0.7771 - Binary-Cell-Jacard: 0.6939 - bPQ-Score: 0.6076 - mPQ-Score: 0.4579 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:51:02,983 [INFO] - New best model - save checkpoint
2023-09-20 14:53:47,765 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:53:47,847 [INFO] - Epoch: 69/130
2023-09-20 14:56:17,659 [INFO] - Training epoch stats:     Loss: 1.8662 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:00:30,374 [INFO] - Validation epoch stats:   Loss: 2.3272 - Binary-Cell-Dice: 0.7737 - Binary-Cell-Jacard: 0.6893 - bPQ-Score: 0.6038 - mPQ-Score: 0.4569 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:00:50,820 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:00:50,820 [INFO] - Epoch: 70/130
2023-09-20 15:02:51,439 [INFO] - Training epoch stats:     Loss: 1.8572 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:07:15,599 [INFO] - Validation epoch stats:   Loss: 2.3149 - Binary-Cell-Dice: 0.7672 - Binary-Cell-Jacard: 0.6813 - bPQ-Score: 0.5956 - mPQ-Score: 0.4512 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:07:43,547 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:07:43,547 [INFO] - Epoch: 71/130
2023-09-20 15:09:46,933 [INFO] - Training epoch stats:     Loss: 1.8282 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:14:03,786 [INFO] - Validation epoch stats:   Loss: 2.3314 - Binary-Cell-Dice: 0.7777 - Binary-Cell-Jacard: 0.6957 - bPQ-Score: 0.6058 - mPQ-Score: 0.4603 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:14:20,404 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:14:20,405 [INFO] - Epoch: 72/130
2023-09-20 15:16:19,944 [INFO] - Training epoch stats:     Loss: 1.7924 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:20:29,422 [INFO] - Validation epoch stats:   Loss: 2.3455 - Binary-Cell-Dice: 0.7672 - Binary-Cell-Jacard: 0.6819 - bPQ-Score: 0.5993 - mPQ-Score: 0.4457 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:21:18,437 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:21:18,437 [INFO] - Epoch: 73/130
2023-09-20 15:23:54,691 [INFO] - Training epoch stats:     Loss: 1.8421 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:28:11,145 [INFO] - Validation epoch stats:   Loss: 2.3638 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6859 - bPQ-Score: 0.5999 - mPQ-Score: 0.4491 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:28:27,367 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:28:27,368 [INFO] - Epoch: 74/130
2023-09-20 15:30:26,996 [INFO] - Training epoch stats:     Loss: 1.7850 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:34:39,266 [INFO] - Validation epoch stats:   Loss: 2.3574 - Binary-Cell-Dice: 0.7717 - Binary-Cell-Jacard: 0.6874 - bPQ-Score: 0.5970 - mPQ-Score: 0.4482 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:34:59,304 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:34:59,305 [INFO] - Epoch: 75/130
2023-09-20 15:36:59,390 [INFO] - Training epoch stats:     Loss: 1.7860 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:41:18,560 [INFO] - Validation epoch stats:   Loss: 2.4367 - Binary-Cell-Dice: 0.7755 - Binary-Cell-Jacard: 0.6929 - bPQ-Score: 0.6055 - mPQ-Score: 0.4514 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:42:08,685 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:42:08,686 [INFO] - Epoch: 76/130
2023-09-20 15:44:13,108 [INFO] - Training epoch stats:     Loss: 1.8135 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:49:30,906 [INFO] - Validation epoch stats:   Loss: 2.3248 - Binary-Cell-Dice: 0.7768 - Binary-Cell-Jacard: 0.6939 - bPQ-Score: 0.6071 - mPQ-Score: 0.4624 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:49:53,071 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:49:53,071 [INFO] - Epoch: 77/130
2023-09-20 15:51:53,371 [INFO] - Training epoch stats:     Loss: 1.7494 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:56:48,569 [INFO] - Validation epoch stats:   Loss: 2.3112 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6912 - bPQ-Score: 0.6053 - mPQ-Score: 0.4593 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:57:10,750 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:57:10,751 [INFO] - Epoch: 78/130
2023-09-20 15:59:10,850 [INFO] - Training epoch stats:     Loss: 1.7182 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:04:59,942 [INFO] - Validation epoch stats:   Loss: 2.3332 - Binary-Cell-Dice: 0.7713 - Binary-Cell-Jacard: 0.6849 - bPQ-Score: 0.5987 - mPQ-Score: 0.4467 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:05:39,250 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:05:39,251 [INFO] - Epoch: 79/130
2023-09-20 16:07:41,083 [INFO] - Training epoch stats:     Loss: 1.8385 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:11:39,125 [INFO] - Validation epoch stats:   Loss: 2.3426 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6816 - bPQ-Score: 0.5976 - mPQ-Score: 0.4487 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:12:34,136 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:12:34,137 [INFO] - Epoch: 80/130
2023-09-20 16:14:34,930 [INFO] - Training epoch stats:     Loss: 1.7614 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:18:42,815 [INFO] - Validation epoch stats:   Loss: 2.3418 - Binary-Cell-Dice: 0.7749 - Binary-Cell-Jacard: 0.6900 - bPQ-Score: 0.5962 - mPQ-Score: 0.4463 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:19:57,083 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:19:57,083 [INFO] - Epoch: 81/130
2023-09-20 16:22:01,276 [INFO] - Training epoch stats:     Loss: 1.7642 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:26:07,468 [INFO] - Validation epoch stats:   Loss: 2.3234 - Binary-Cell-Dice: 0.7698 - Binary-Cell-Jacard: 0.6856 - bPQ-Score: 0.6015 - mPQ-Score: 0.4519 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:26:39,743 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:26:39,744 [INFO] - Epoch: 82/130
2023-09-20 16:28:43,609 [INFO] - Training epoch stats:     Loss: 1.7513 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:32:50,211 [INFO] - Validation epoch stats:   Loss: 2.4225 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6819 - bPQ-Score: 0.5989 - mPQ-Score: 0.4509 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:33:23,688 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:33:23,688 [INFO] - Epoch: 83/130
2023-09-20 16:35:28,737 [INFO] - Training epoch stats:     Loss: 1.7545 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:40:23,994 [INFO] - Validation epoch stats:   Loss: 2.3954 - Binary-Cell-Dice: 0.7719 - Binary-Cell-Jacard: 0.6896 - bPQ-Score: 0.6049 - mPQ-Score: 0.4415 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:41:10,742 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:41:10,742 [INFO] - Epoch: 84/130
2023-09-20 16:43:14,883 [INFO] - Training epoch stats:     Loss: 1.7535 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:47:18,755 [INFO] - Validation epoch stats:   Loss: 2.3349 - Binary-Cell-Dice: 0.7735 - Binary-Cell-Jacard: 0.6896 - bPQ-Score: 0.6025 - mPQ-Score: 0.4530 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:48:03,679 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:48:03,680 [INFO] - Epoch: 85/130
2023-09-20 16:50:08,114 [INFO] - Training epoch stats:     Loss: 1.6796 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:54:20,314 [INFO] - Validation epoch stats:   Loss: 2.3267 - Binary-Cell-Dice: 0.7727 - Binary-Cell-Jacard: 0.6878 - bPQ-Score: 0.5999 - mPQ-Score: 0.4491 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:55:15,603 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:55:15,604 [INFO] - Epoch: 86/130
2023-09-20 16:57:18,773 [INFO] - Training epoch stats:     Loss: 1.7128 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:02:10,483 [INFO] - Validation epoch stats:   Loss: 2.3228 - Binary-Cell-Dice: 0.7699 - Binary-Cell-Jacard: 0.6862 - bPQ-Score: 0.6005 - mPQ-Score: 0.4536 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:02:42,257 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:02:42,258 [INFO] - Epoch: 87/130
2023-09-20 17:04:46,049 [INFO] - Training epoch stats:     Loss: 1.7325 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:09:10,082 [INFO] - Validation epoch stats:   Loss: 2.3697 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6832 - bPQ-Score: 0.5989 - mPQ-Score: 0.4443 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:09:30,914 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:09:30,914 [INFO] - Epoch: 88/130
2023-09-20 17:11:31,222 [INFO] - Training epoch stats:     Loss: 1.6887 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:15:56,790 [INFO] - Validation epoch stats:   Loss: 2.3525 - Binary-Cell-Dice: 0.7707 - Binary-Cell-Jacard: 0.6880 - bPQ-Score: 0.6040 - mPQ-Score: 0.4526 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:16:15,080 [DEBUG] - Old lr: 0.000050 - New lr: 0.000025
2023-09-20 17:16:15,080 [INFO] - Epoch: 89/130
2023-09-20 17:18:14,872 [INFO] - Training epoch stats:     Loss: 1.6561 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:23:07,457 [INFO] - Validation epoch stats:   Loss: 2.3136 - Binary-Cell-Dice: 0.7687 - Binary-Cell-Jacard: 0.6832 - bPQ-Score: 0.5994 - mPQ-Score: 0.4530 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:23:24,368 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:23:24,369 [INFO] - Epoch: 90/130
2023-09-20 17:25:27,956 [INFO] - Training epoch stats:     Loss: 1.6634 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:30:03,617 [INFO] - Validation epoch stats:   Loss: 2.3475 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6908 - bPQ-Score: 0.6068 - mPQ-Score: 0.4579 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:30:21,456 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:30:21,456 [INFO] - Epoch: 91/130
2023-09-20 17:32:21,636 [INFO] - Training epoch stats:     Loss: 1.7116 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:36:56,652 [INFO] - Validation epoch stats:   Loss: 2.3395 - Binary-Cell-Dice: 0.7785 - Binary-Cell-Jacard: 0.6968 - bPQ-Score: 0.6114 - mPQ-Score: 0.4590 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:36:56,663 [INFO] - New best model - save checkpoint
2023-09-20 17:38:22,957 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:38:22,957 [INFO] - Epoch: 92/130
2023-09-20 17:40:25,245 [INFO] - Training epoch stats:     Loss: 1.6170 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:44:57,326 [INFO] - Validation epoch stats:   Loss: 2.3139 - Binary-Cell-Dice: 0.7780 - Binary-Cell-Jacard: 0.6963 - bPQ-Score: 0.6107 - mPQ-Score: 0.4615 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:45:15,471 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:45:15,472 [INFO] - Epoch: 93/130
2023-09-20 17:47:17,236 [INFO] - Training epoch stats:     Loss: 1.6657 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:51:28,574 [INFO] - Validation epoch stats:   Loss: 2.3391 - Binary-Cell-Dice: 0.7789 - Binary-Cell-Jacard: 0.6964 - bPQ-Score: 0.6079 - mPQ-Score: 0.4540 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:51:45,822 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:51:45,823 [INFO] - Epoch: 94/130
2023-09-20 17:53:49,192 [INFO] - Training epoch stats:     Loss: 1.6497 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:58:01,328 [INFO] - Validation epoch stats:   Loss: 2.3087 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6830 - bPQ-Score: 0.5959 - mPQ-Score: 0.4516 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:58:36,735 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 17:58:36,736 [INFO] - Epoch: 95/130
2023-09-20 18:00:40,512 [INFO] - Training epoch stats:     Loss: 1.6627 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:04:38,053 [INFO] - Validation epoch stats:   Loss: 2.3325 - Binary-Cell-Dice: 0.7716 - Binary-Cell-Jacard: 0.6878 - bPQ-Score: 0.6065 - mPQ-Score: 0.4542 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:04:56,311 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:04:56,311 [INFO] - Epoch: 96/130
2023-09-20 18:06:55,246 [INFO] - Training epoch stats:     Loss: 1.6422 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:10:51,911 [INFO] - Validation epoch stats:   Loss: 2.3128 - Binary-Cell-Dice: 0.7778 - Binary-Cell-Jacard: 0.6954 - bPQ-Score: 0.6103 - mPQ-Score: 0.4609 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:11:24,530 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:11:24,688 [INFO] - Epoch: 97/130
2023-09-20 18:13:28,816 [INFO] - Training epoch stats:     Loss: 1.6457 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:17:41,947 [INFO] - Validation epoch stats:   Loss: 2.3377 - Binary-Cell-Dice: 0.7741 - Binary-Cell-Jacard: 0.6915 - bPQ-Score: 0.6071 - mPQ-Score: 0.4560 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:17:59,876 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:17:59,876 [INFO] - Epoch: 98/130
2023-09-20 18:20:00,145 [INFO] - Training epoch stats:     Loss: 1.6372 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:25:08,194 [INFO] - Validation epoch stats:   Loss: 2.3192 - Binary-Cell-Dice: 0.7764 - Binary-Cell-Jacard: 0.6940 - bPQ-Score: 0.6073 - mPQ-Score: 0.4573 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:25:40,431 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:25:40,432 [INFO] - Epoch: 99/130
2023-09-20 18:27:43,464 [INFO] - Training epoch stats:     Loss: 1.6218 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:32:22,216 [INFO] - Validation epoch stats:   Loss: 2.3207 - Binary-Cell-Dice: 0.7735 - Binary-Cell-Jacard: 0.6902 - bPQ-Score: 0.6060 - mPQ-Score: 0.4599 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:32:42,118 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:32:42,118 [INFO] - Epoch: 100/130
2023-09-20 18:34:42,183 [INFO] - Training epoch stats:     Loss: 1.6065 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:38:58,711 [INFO] - Validation epoch stats:   Loss: 2.3312 - Binary-Cell-Dice: 0.7765 - Binary-Cell-Jacard: 0.6935 - bPQ-Score: 0.6061 - mPQ-Score: 0.4588 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:39:16,693 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:39:16,694 [INFO] - Epoch: 101/130
2023-09-20 18:41:17,523 [INFO] - Training epoch stats:     Loss: 1.6227 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:45:52,566 [INFO] - Validation epoch stats:   Loss: 2.3363 - Binary-Cell-Dice: 0.7769 - Binary-Cell-Jacard: 0.6942 - bPQ-Score: 0.6093 - mPQ-Score: 0.4631 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:46:10,244 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:46:10,245 [INFO] - Epoch: 102/130
2023-09-20 18:48:11,166 [INFO] - Training epoch stats:     Loss: 1.6223 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:52:26,014 [INFO] - Validation epoch stats:   Loss: 2.3194 - Binary-Cell-Dice: 0.7763 - Binary-Cell-Jacard: 0.6945 - bPQ-Score: 0.6105 - mPQ-Score: 0.4603 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:52:43,970 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:52:43,970 [INFO] - Epoch: 103/130
2023-09-20 18:54:45,378 [INFO] - Training epoch stats:     Loss: 1.6506 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:59:39,871 [INFO] - Validation epoch stats:   Loss: 2.3382 - Binary-Cell-Dice: 0.7757 - Binary-Cell-Jacard: 0.6932 - bPQ-Score: 0.6079 - mPQ-Score: 0.4571 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:59:56,706 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:59:56,706 [INFO] - Epoch: 104/130
2023-09-20 19:01:56,360 [INFO] - Training epoch stats:     Loss: 1.6382 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:06:19,037 [INFO] - Validation epoch stats:   Loss: 2.3179 - Binary-Cell-Dice: 0.7779 - Binary-Cell-Jacard: 0.6957 - bPQ-Score: 0.6082 - mPQ-Score: 0.4630 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:06:36,646 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:06:36,646 [INFO] - Epoch: 105/130
2023-09-20 19:08:36,927 [INFO] - Training epoch stats:     Loss: 1.5668 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:13:00,355 [INFO] - Validation epoch stats:   Loss: 2.3253 - Binary-Cell-Dice: 0.7778 - Binary-Cell-Jacard: 0.6960 - bPQ-Score: 0.6104 - mPQ-Score: 0.4613 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:13:22,554 [DEBUG] - Old lr: 0.000025 - New lr: 0.000013
2023-09-20 19:13:22,555 [INFO] - Epoch: 106/130
2023-09-20 19:15:22,828 [INFO] - Training epoch stats:     Loss: 1.5552 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:19:42,483 [INFO] - Validation epoch stats:   Loss: 2.3218 - Binary-Cell-Dice: 0.7761 - Binary-Cell-Jacard: 0.6939 - bPQ-Score: 0.6088 - mPQ-Score: 0.4591 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:20:02,826 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:20:02,827 [INFO] - Epoch: 107/130
2023-09-20 19:22:02,709 [INFO] - Training epoch stats:     Loss: 1.5454 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:26:26,095 [INFO] - Validation epoch stats:   Loss: 2.3197 - Binary-Cell-Dice: 0.7780 - Binary-Cell-Jacard: 0.6962 - bPQ-Score: 0.6116 - mPQ-Score: 0.4640 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:26:26,106 [INFO] - New best model - save checkpoint
2023-09-20 19:27:04,452 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:27:04,452 [INFO] - Epoch: 108/130
2023-09-20 19:29:03,861 [INFO] - Training epoch stats:     Loss: 1.5371 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:33:28,766 [INFO] - Validation epoch stats:   Loss: 2.3214 - Binary-Cell-Dice: 0.7762 - Binary-Cell-Jacard: 0.6940 - bPQ-Score: 0.6081 - mPQ-Score: 0.4576 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:33:45,603 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:33:45,604 [INFO] - Epoch: 109/130
2023-09-20 19:35:45,379 [INFO] - Training epoch stats:     Loss: 1.5632 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:39:51,549 [INFO] - Validation epoch stats:   Loss: 2.3247 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6963 - bPQ-Score: 0.6122 - mPQ-Score: 0.4594 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:39:51,554 [INFO] - New best model - save checkpoint
2023-09-20 19:40:28,821 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:40:28,822 [INFO] - Epoch: 110/130
2023-09-20 19:42:29,293 [INFO] - Training epoch stats:     Loss: 1.5760 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:46:35,119 [INFO] - Validation epoch stats:   Loss: 2.3156 - Binary-Cell-Dice: 0.7773 - Binary-Cell-Jacard: 0.6948 - bPQ-Score: 0.6091 - mPQ-Score: 0.4620 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:46:52,820 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:46:52,820 [INFO] - Epoch: 111/130
2023-09-20 19:48:52,684 [INFO] - Training epoch stats:     Loss: 1.5002 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:53:08,870 [INFO] - Validation epoch stats:   Loss: 2.3189 - Binary-Cell-Dice: 0.7801 - Binary-Cell-Jacard: 0.6995 - bPQ-Score: 0.6131 - mPQ-Score: 0.4651 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:53:08,880 [INFO] - New best model - save checkpoint
2023-09-20 19:53:54,223 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:53:54,224 [INFO] - Epoch: 112/130
2023-09-20 19:55:53,441 [INFO] - Training epoch stats:     Loss: 1.5703 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:59:55,382 [INFO] - Validation epoch stats:   Loss: 2.3300 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6924 - bPQ-Score: 0.6083 - mPQ-Score: 0.4598 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:00:11,538 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:00:11,538 [INFO] - Epoch: 113/130
2023-09-20 20:02:10,709 [INFO] - Training epoch stats:     Loss: 1.5494 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:06:43,845 [INFO] - Validation epoch stats:   Loss: 2.3357 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6919 - bPQ-Score: 0.6071 - mPQ-Score: 0.4583 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:07:14,904 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:07:14,904 [INFO] - Epoch: 114/130
2023-09-20 20:09:19,301 [INFO] - Training epoch stats:     Loss: 1.5674 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:13:36,475 [INFO] - Validation epoch stats:   Loss: 2.3540 - Binary-Cell-Dice: 0.7746 - Binary-Cell-Jacard: 0.6915 - bPQ-Score: 0.6068 - mPQ-Score: 0.4563 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:13:53,175 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:13:53,175 [INFO] - Epoch: 115/130
2023-09-20 20:15:53,182 [INFO] - Training epoch stats:     Loss: 1.5404 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:20:53,806 [INFO] - Validation epoch stats:   Loss: 2.3494 - Binary-Cell-Dice: 0.7763 - Binary-Cell-Jacard: 0.6942 - bPQ-Score: 0.6106 - mPQ-Score: 0.4589 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:21:27,162 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:21:27,163 [INFO] - Epoch: 116/130
2023-09-20 20:23:31,268 [INFO] - Training epoch stats:     Loss: 1.5523 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:28:12,422 [INFO] - Validation epoch stats:   Loss: 2.3546 - Binary-Cell-Dice: 0.7765 - Binary-Cell-Jacard: 0.6950 - bPQ-Score: 0.6110 - mPQ-Score: 0.4600 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:28:28,768 [DEBUG] - Old lr: 0.000013 - New lr: 0.000006
2023-09-20 20:28:28,768 [INFO] - Epoch: 117/130
2023-09-20 20:30:28,085 [INFO] - Training epoch stats:     Loss: 1.5757 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:34:52,436 [INFO] - Validation epoch stats:   Loss: 2.3243 - Binary-Cell-Dice: 0.7762 - Binary-Cell-Jacard: 0.6939 - bPQ-Score: 0.6089 - mPQ-Score: 0.4601 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:35:29,474 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 20:35:29,475 [INFO] - Epoch: 118/130
2023-09-20 20:37:33,585 [INFO] - Training epoch stats:     Loss: 1.4958 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:41:50,049 [INFO] - Validation epoch stats:   Loss: 2.3346 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6970 - bPQ-Score: 0.6113 - mPQ-Score: 0.4618 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:42:08,257 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 20:42:08,257 [INFO] - Epoch: 119/130
2023-09-20 20:44:08,925 [INFO] - Training epoch stats:     Loss: 1.4974 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:48:20,027 [INFO] - Validation epoch stats:   Loss: 2.3231 - Binary-Cell-Dice: 0.7762 - Binary-Cell-Jacard: 0.6942 - bPQ-Score: 0.6093 - mPQ-Score: 0.4614 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:48:36,377 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 20:48:36,378 [INFO] - Epoch: 120/130
2023-09-20 20:50:36,635 [INFO] - Training epoch stats:     Loss: 1.5091 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:55:02,473 [INFO] - Validation epoch stats:   Loss: 2.3180 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6927 - bPQ-Score: 0.6073 - mPQ-Score: 0.4623 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:55:20,002 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 20:55:20,002 [INFO] - Epoch: 121/130
2023-09-20 20:57:19,677 [INFO] - Training epoch stats:     Loss: 1.5595 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:02:08,998 [INFO] - Validation epoch stats:   Loss: 2.3299 - Binary-Cell-Dice: 0.7773 - Binary-Cell-Jacard: 0.6954 - bPQ-Score: 0.6101 - mPQ-Score: 0.4591 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:02:30,190 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:02:30,191 [INFO] - Epoch: 122/130
2023-09-20 21:04:33,124 [INFO] - Training epoch stats:     Loss: 1.4853 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:09:02,010 [INFO] - Validation epoch stats:   Loss: 2.3198 - Binary-Cell-Dice: 0.7754 - Binary-Cell-Jacard: 0.6932 - bPQ-Score: 0.6086 - mPQ-Score: 0.4627 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:09:18,932 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:09:18,932 [INFO] - Epoch: 123/130
2023-09-20 21:11:18,577 [INFO] - Training epoch stats:     Loss: 1.5489 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:15:47,801 [INFO] - Validation epoch stats:   Loss: 2.3191 - Binary-Cell-Dice: 0.7735 - Binary-Cell-Jacard: 0.6900 - bPQ-Score: 0.6058 - mPQ-Score: 0.4609 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:16:04,696 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:16:04,697 [INFO] - Epoch: 124/130
2023-09-20 21:18:04,207 [INFO] - Training epoch stats:     Loss: 1.4916 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:22:52,093 [INFO] - Validation epoch stats:   Loss: 2.3203 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6920 - bPQ-Score: 0.6082 - mPQ-Score: 0.4616 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:23:19,666 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:23:19,666 [INFO] - Epoch: 125/130
2023-09-20 21:25:19,293 [INFO] - Training epoch stats:     Loss: 1.4858 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:29:31,084 [INFO] - Validation epoch stats:   Loss: 2.3223 - Binary-Cell-Dice: 0.7754 - Binary-Cell-Jacard: 0.6929 - bPQ-Score: 0.6083 - mPQ-Score: 0.4622 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:29:49,982 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:29:49,982 [INFO] - Epoch: 126/130
2023-09-20 21:31:49,725 [INFO] - Training epoch stats:     Loss: 1.4647 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:36:19,651 [INFO] - Validation epoch stats:   Loss: 2.3275 - Binary-Cell-Dice: 0.7774 - Binary-Cell-Jacard: 0.6956 - bPQ-Score: 0.6095 - mPQ-Score: 0.4618 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:36:42,880 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:36:42,880 [INFO] - Epoch: 127/130
2023-09-20 21:38:42,792 [INFO] - Training epoch stats:     Loss: 1.4879 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:43:04,739 [INFO] - Validation epoch stats:   Loss: 2.3556 - Binary-Cell-Dice: 0.7749 - Binary-Cell-Jacard: 0.6920 - bPQ-Score: 0.6077 - mPQ-Score: 0.4537 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:43:32,910 [DEBUG] - Old lr: 0.000006 - New lr: 0.000003
2023-09-20 21:43:32,910 [INFO] - Epoch: 128/130
2023-09-20 21:45:35,074 [INFO] - Training epoch stats:     Loss: 1.5278 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:49:46,080 [INFO] - Validation epoch stats:   Loss: 2.3300 - Binary-Cell-Dice: 0.7752 - Binary-Cell-Jacard: 0.6927 - bPQ-Score: 0.6077 - mPQ-Score: 0.4576 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:50:27,536 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 21:50:27,537 [INFO] - Epoch: 129/130
2023-09-20 21:52:31,363 [INFO] - Training epoch stats:     Loss: 1.4874 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:56:38,297 [INFO] - Validation epoch stats:   Loss: 2.3399 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6926 - bPQ-Score: 0.6074 - mPQ-Score: 0.4588 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:56:56,412 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 21:56:56,412 [INFO] - Epoch: 130/130
2023-09-20 21:58:57,729 [INFO] - Training epoch stats:     Loss: 1.5011 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:04:11,277 [INFO] - Validation epoch stats:   Loss: 2.3292 - Binary-Cell-Dice: 0.7758 - Binary-Cell-Jacard: 0.6932 - bPQ-Score: 0.6081 - mPQ-Score: 0.4602 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:04:56,250 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:04:56,258 [INFO] -
