2023-09-20 06:08:11,920 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-20 06:08:11,982 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f3116c8e5b0>]
2023-09-20 06:08:11,983 [INFO] - Using GPU: cuda:0
2023-09-20 06:08:11,983 [INFO] - Using device: cuda:0
2023-09-20 06:08:11,983 [INFO] - Loss functions:
2023-09-20 06:08:11,984 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 1}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON><PERSON>(), 'weight': 1}}}
2023-09-20 06:08:17,774 [INFO] -
Model: StarDistRN50(
  (encoder): ResNet(
    (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (relu): ReLU(inplace=True)
    (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (layer1): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer2): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer3): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (4): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (5): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer4): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
  )
  (up1): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4): Upsample(scale_factor=2.0, mode=bilinear)
  (features): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (out_prob): outconv(
    (conv): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (out_ray): outconv(
    (conv): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
  )
  (up1_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4_seg): Upsample(scale_factor=2.0, mode=bilinear)
  (out_seg): outconv(
    (conv): Conv2d(256, 6, kernel_size=(1, 1), stride=(1, 1))
  )
  (final_activation_ray): ReLU()
)
2023-09-20 06:08:19,644 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
StarDistRN50                                  [1, 6, 256, 256]          --
├─ResNet: 1-1                                 [1, 256, 128, 128]        --
│    └─Conv2d: 2-1                            [1, 64, 128, 128]         9,408
│    └─BatchNorm2d: 2-2                       [1, 64, 128, 128]         128
│    └─ReLU: 2-3                              [1, 64, 128, 128]         --
│    └─Sequential: 2-4                        [1, 256, 128, 128]        --
│    │    └─Bottleneck: 3-1                   [1, 256, 128, 128]        75,008
│    │    └─Bottleneck: 3-2                   [1, 256, 128, 128]        70,400
│    │    └─Bottleneck: 3-3                   [1, 256, 128, 128]        70,400
│    └─Sequential: 2-5                        [1, 512, 64, 64]          --
│    │    └─Bottleneck: 3-4                   [1, 512, 64, 64]          379,392
│    │    └─Bottleneck: 3-5                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-6                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-7                   [1, 512, 64, 64]          280,064
│    └─Sequential: 2-6                        [1, 1024, 32, 32]         --
│    │    └─Bottleneck: 3-8                   [1, 1024, 32, 32]         1,512,448
│    │    └─Bottleneck: 3-9                   [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-10                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-11                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-12                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-13                  [1, 1024, 32, 32]         1,117,184
│    └─Sequential: 2-7                        [1, 2048, 16, 16]         --
│    │    └─Bottleneck: 3-14                  [1, 2048, 16, 16]         6,039,552
│    │    └─Bottleneck: 3-15                  [1, 2048, 16, 16]         4,462,592
│    │    └─Bottleneck: 3-16                  [1, 2048, 16, 16]         4,462,592
├─up: 1-2                                     [1, 1024, 32, 32]         --
│    └─Upsample: 2-8                          [1, 2048, 32, 32]         --
│    └─double_conv: 2-9                       [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-17                  [1, 1024, 32, 32]         37,754,880
├─up: 1-3                                     [1, 512, 64, 64]          --
│    └─Upsample: 2-10                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-11                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-18                  [1, 512, 64, 64]          9,440,256
├─up: 1-4                                     [1, 256, 128, 128]        --
│    └─Upsample: 2-12                         [1, 512, 128, 128]        --
│    └─double_conv: 2-13                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-19                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-5                               [1, 256, 256, 256]        --
├─Conv2d: 1-6                                 [1, 256, 256, 256]        590,080
├─outconv: 1-7                                [1, 1, 256, 256]          --
│    └─Conv2d: 2-14                           [1, 1, 256, 256]          257
├─outconv: 1-8                                [1, 32, 256, 256]         --
│    └─Conv2d: 2-15                           [1, 32, 256, 256]         8,224
├─ReLU: 1-9                                   [1, 32, 256, 256]         --
├─up: 1-10                                    [1, 1024, 32, 32]         --
│    └─Upsample: 2-16                         [1, 2048, 32, 32]         --
│    └─double_conv: 2-17                      [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-20                  [1, 1024, 32, 32]         37,754,880
├─up: 1-11                                    [1, 512, 64, 64]          --
│    └─Upsample: 2-18                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-19                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-21                  [1, 512, 64, 64]          9,440,256
├─up: 1-12                                    [1, 256, 128, 128]        --
│    └─Upsample: 2-20                         [1, 512, 128, 128]        --
│    └─double_conv: 2-21                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-22                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-13                              [1, 256, 256, 256]        --
├─outconv: 1-14                               [1, 6, 256, 256]          --
│    └─Conv2d: 2-22                           [1, 6, 256, 256]          1,542
===============================================================================================
Total params: 123,220,071
Trainable params: 123,220,071
Non-trainable params: 0
Total mult-adds (G): 292.18
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1503.13
Params size (MB): 492.88
Estimated Total Size (MB): 1996.80
===============================================================================================
2023-09-20 06:08:37,474 [INFO] - Loaded Adam Optimizer with following hyperparameters:
2023-09-20 06:08:37,480 [INFO] - {'lr': 0.0001}
2023-09-20 06:08:37,480 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-20 06:09:08,311 [INFO] - Using RandomSampler
2023-09-20 06:09:08,316 [INFO] - Instantiate Trainer
2023-09-20 06:09:08,316 [INFO] - Calling Trainer Fit
2023-09-20 06:09:08,317 [INFO] - Starting training, total number of epochs: 130
2023-09-20 06:09:08,317 [INFO] - Epoch: 1/130
2023-09-20 06:11:18,818 [INFO] - Training epoch stats:     Loss: 4.8217 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:39:01,287 [INFO] - Validation epoch stats:   Loss: 3.9287 - Binary-Cell-Dice: 0.5832 - Binary-Cell-Jacard: 0.4495 - bPQ-Score: 0.0264 - mPQ-Score: 0.0038 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:39:01,291 [INFO] - New best model - save checkpoint
2023-09-20 06:40:08,952 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:40:08,953 [INFO] - Epoch: 2/130
2023-09-20 06:42:09,007 [INFO] - Training epoch stats:     Loss: 3.7300 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:48:01,157 [INFO] - Validation epoch stats:   Loss: 3.5859 - Binary-Cell-Dice: 0.6626 - Binary-Cell-Jacard: 0.5326 - bPQ-Score: 0.1494 - mPQ-Score: 0.1117 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:48:01,164 [INFO] - New best model - save checkpoint
2023-09-20 06:48:40,717 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:48:40,717 [INFO] - Epoch: 3/130
2023-09-20 06:50:30,884 [INFO] - Training epoch stats:     Loss: 3.5337 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:55:33,665 [INFO] - Validation epoch stats:   Loss: 3.4371 - Binary-Cell-Dice: 0.6720 - Binary-Cell-Jacard: 0.5446 - bPQ-Score: 0.3028 - mPQ-Score: 0.1991 - Tissue-MC-Acc.: 0.0000
2023-09-20 06:55:33,676 [INFO] - New best model - save checkpoint
2023-09-20 06:56:56,310 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 06:56:56,312 [INFO] - Epoch: 4/130
2023-09-20 06:58:44,328 [INFO] - Training epoch stats:     Loss: 3.3875 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:10:24,660 [INFO] - Validation epoch stats:   Loss: 3.3618 - Binary-Cell-Dice: 0.6841 - Binary-Cell-Jacard: 0.5555 - bPQ-Score: 0.4007 - mPQ-Score: 0.2713 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:10:24,664 [INFO] - New best model - save checkpoint
2023-09-20 07:11:15,842 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:11:15,843 [INFO] - Epoch: 5/130
2023-09-20 07:13:12,615 [INFO] - Training epoch stats:     Loss: 3.2713 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:32:07,249 [INFO] - Validation epoch stats:   Loss: 3.2276 - Binary-Cell-Dice: 0.6912 - Binary-Cell-Jacard: 0.5719 - bPQ-Score: 0.4033 - mPQ-Score: 0.2690 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:32:07,259 [INFO] - New best model - save checkpoint
2023-09-20 07:33:14,650 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:33:14,651 [INFO] - Epoch: 6/130
2023-09-20 07:35:13,269 [INFO] - Training epoch stats:     Loss: 3.1861 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:41:29,412 [INFO] - Validation epoch stats:   Loss: 3.2339 - Binary-Cell-Dice: 0.7028 - Binary-Cell-Jacard: 0.5852 - bPQ-Score: 0.4378 - mPQ-Score: 0.3016 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:41:29,532 [INFO] - New best model - save checkpoint
2023-09-20 07:43:23,005 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:43:23,013 [INFO] - Epoch: 7/130
2023-09-20 07:45:14,798 [INFO] - Training epoch stats:     Loss: 3.1339 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:50:01,925 [INFO] - Validation epoch stats:   Loss: 3.2215 - Binary-Cell-Dice: 0.7005 - Binary-Cell-Jacard: 0.5809 - bPQ-Score: 0.3778 - mPQ-Score: 0.2480 - Tissue-MC-Acc.: 0.0000
2023-09-20 07:50:38,070 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 07:50:38,070 [INFO] - Epoch: 8/130
2023-09-20 07:52:27,172 [INFO] - Training epoch stats:     Loss: 3.0674 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:04:04,764 [INFO] - Validation epoch stats:   Loss: 3.0827 - Binary-Cell-Dice: 0.7307 - Binary-Cell-Jacard: 0.6217 - bPQ-Score: 0.4634 - mPQ-Score: 0.3315 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:04:04,830 [INFO] - New best model - save checkpoint
2023-09-20 08:05:23,860 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:05:23,861 [INFO] - Epoch: 9/130
2023-09-20 08:07:12,298 [INFO] - Training epoch stats:     Loss: 3.0562 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:11:47,970 [INFO] - Validation epoch stats:   Loss: 3.1436 - Binary-Cell-Dice: 0.7295 - Binary-Cell-Jacard: 0.6210 - bPQ-Score: 0.5009 - mPQ-Score: 0.3527 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:11:48,083 [INFO] - New best model - save checkpoint
2023-09-20 08:13:10,578 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:13:10,578 [INFO] - Epoch: 10/130
2023-09-20 08:14:59,155 [INFO] - Training epoch stats:     Loss: 2.9973 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:19:23,799 [INFO] - Validation epoch stats:   Loss: 3.1435 - Binary-Cell-Dice: 0.7382 - Binary-Cell-Jacard: 0.6308 - bPQ-Score: 0.5160 - mPQ-Score: 0.3529 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:19:23,838 [INFO] - New best model - save checkpoint
2023-09-20 08:20:44,543 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:20:44,544 [INFO] - Epoch: 11/130
2023-09-20 08:22:34,057 [INFO] - Training epoch stats:     Loss: 2.8855 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:27:55,379 [INFO] - Validation epoch stats:   Loss: 2.9698 - Binary-Cell-Dice: 0.7445 - Binary-Cell-Jacard: 0.6395 - bPQ-Score: 0.5030 - mPQ-Score: 0.3662 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:28:27,815 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:28:27,815 [INFO] - Epoch: 12/130
2023-09-20 08:30:17,866 [INFO] - Training epoch stats:     Loss: 2.9284 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:35:30,223 [INFO] - Validation epoch stats:   Loss: 2.9890 - Binary-Cell-Dice: 0.7522 - Binary-Cell-Jacard: 0.6505 - bPQ-Score: 0.5214 - mPQ-Score: 0.3859 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:35:30,232 [INFO] - New best model - save checkpoint
2023-09-20 08:36:40,327 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:36:40,328 [INFO] - Epoch: 13/130
2023-09-20 08:38:31,687 [INFO] - Training epoch stats:     Loss: 2.8624 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:42:53,829 [INFO] - Validation epoch stats:   Loss: 3.2009 - Binary-Cell-Dice: 0.7234 - Binary-Cell-Jacard: 0.6140 - bPQ-Score: 0.4865 - mPQ-Score: 0.3289 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:43:47,335 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:43:47,335 [INFO] - Epoch: 14/130
2023-09-20 08:45:37,349 [INFO] - Training epoch stats:     Loss: 2.8432 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:49:34,080 [INFO] - Validation epoch stats:   Loss: 2.9302 - Binary-Cell-Dice: 0.7467 - Binary-Cell-Jacard: 0.6427 - bPQ-Score: 0.5320 - mPQ-Score: 0.3913 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:49:34,139 [INFO] - New best model - save checkpoint
2023-09-20 08:51:50,684 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:51:51,269 [INFO] - Epoch: 15/130
2023-09-20 08:53:42,168 [INFO] - Training epoch stats:     Loss: 2.8442 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:57:36,278 [INFO] - Validation epoch stats:   Loss: 2.9844 - Binary-Cell-Dice: 0.7426 - Binary-Cell-Jacard: 0.6393 - bPQ-Score: 0.5470 - mPQ-Score: 0.3924 - Tissue-MC-Acc.: 0.0000
2023-09-20 08:57:36,491 [INFO] - New best model - save checkpoint
2023-09-20 08:59:28,781 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 08:59:28,789 [INFO] - Epoch: 16/130
2023-09-20 09:01:19,782 [INFO] - Training epoch stats:     Loss: 2.8926 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:05:44,758 [INFO] - Validation epoch stats:   Loss: 2.9141 - Binary-Cell-Dice: 0.7355 - Binary-Cell-Jacard: 0.6265 - bPQ-Score: 0.4990 - mPQ-Score: 0.3652 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:06:21,373 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:06:21,373 [INFO] - Epoch: 17/130
2023-09-20 09:08:12,272 [INFO] - Training epoch stats:     Loss: 2.8748 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:12:46,666 [INFO] - Validation epoch stats:   Loss: 2.9471 - Binary-Cell-Dice: 0.7613 - Binary-Cell-Jacard: 0.6631 - bPQ-Score: 0.5469 - mPQ-Score: 0.3947 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:13:09,236 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:13:09,236 [INFO] - Epoch: 18/130
2023-09-20 09:15:15,444 [INFO] - Training epoch stats:     Loss: 2.8191 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:19:45,191 [INFO] - Validation epoch stats:   Loss: 2.9804 - Binary-Cell-Dice: 0.7594 - Binary-Cell-Jacard: 0.6631 - bPQ-Score: 0.5604 - mPQ-Score: 0.4071 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:19:45,553 [INFO] - New best model - save checkpoint
2023-09-20 09:20:52,140 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:20:52,140 [INFO] - Epoch: 19/130
2023-09-20 09:22:44,234 [INFO] - Training epoch stats:     Loss: 2.7563 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:27:22,939 [INFO] - Validation epoch stats:   Loss: 3.0455 - Binary-Cell-Dice: 0.7536 - Binary-Cell-Jacard: 0.6524 - bPQ-Score: 0.5537 - mPQ-Score: 0.3865 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:27:59,742 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:27:59,742 [INFO] - Epoch: 20/130
2023-09-20 09:29:52,355 [INFO] - Training epoch stats:     Loss: 2.7544 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:34:29,425 [INFO] - Validation epoch stats:   Loss: 2.8759 - Binary-Cell-Dice: 0.7617 - Binary-Cell-Jacard: 0.6673 - bPQ-Score: 0.5630 - mPQ-Score: 0.4012 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:34:29,434 [INFO] - New best model - save checkpoint
2023-09-20 09:35:47,565 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:35:47,566 [INFO] - Epoch: 21/130
2023-09-20 09:37:40,395 [INFO] - Training epoch stats:     Loss: 2.7636 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:41:57,590 [INFO] - Validation epoch stats:   Loss: 2.8405 - Binary-Cell-Dice: 0.7458 - Binary-Cell-Jacard: 0.6459 - bPQ-Score: 0.5514 - mPQ-Score: 0.4073 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:42:13,563 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:42:13,564 [INFO] - Epoch: 22/130
2023-09-20 09:44:03,590 [INFO] - Training epoch stats:     Loss: 2.7294 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:48:42,031 [INFO] - Validation epoch stats:   Loss: 3.0377 - Binary-Cell-Dice: 0.7487 - Binary-Cell-Jacard: 0.6461 - bPQ-Score: 0.5424 - mPQ-Score: 0.3899 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:48:59,491 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:48:59,491 [INFO] - Epoch: 23/130
2023-09-20 09:50:49,298 [INFO] - Training epoch stats:     Loss: 2.7033 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:55:31,536 [INFO] - Validation epoch stats:   Loss: 2.8360 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6831 - bPQ-Score: 0.5755 - mPQ-Score: 0.4242 - Tissue-MC-Acc.: 0.0000
2023-09-20 09:55:31,545 [INFO] - New best model - save checkpoint
2023-09-20 09:56:35,633 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 09:56:35,633 [INFO] - Epoch: 24/130
2023-09-20 09:58:23,790 [INFO] - Training epoch stats:     Loss: 2.7047 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:02:58,433 [INFO] - Validation epoch stats:   Loss: 3.0176 - Binary-Cell-Dice: 0.7651 - Binary-Cell-Jacard: 0.6728 - bPQ-Score: 0.5652 - mPQ-Score: 0.3986 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:03:15,233 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:03:15,234 [INFO] - Epoch: 25/130
2023-09-20 10:05:02,672 [INFO] - Training epoch stats:     Loss: 2.6778 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:09:34,527 [INFO] - Validation epoch stats:   Loss: 2.8760 - Binary-Cell-Dice: 0.7633 - Binary-Cell-Jacard: 0.6683 - bPQ-Score: 0.5627 - mPQ-Score: 0.4042 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:09:52,610 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:09:52,611 [INFO] - Epoch: 26/130
2023-09-20 10:11:40,663 [INFO] - Training epoch stats:     Loss: 2.7149 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:16:06,837 [INFO] - Validation epoch stats:   Loss: 3.2290 - Binary-Cell-Dice: 0.7597 - Binary-Cell-Jacard: 0.6638 - bPQ-Score: 0.5683 - mPQ-Score: 0.3743 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:16:23,753 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:16:23,754 [INFO] - Epoch: 27/130
2023-09-20 10:18:11,474 [INFO] - Training epoch stats:     Loss: 2.7340 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:22:54,799 [INFO] - Validation epoch stats:   Loss: 2.9133 - Binary-Cell-Dice: 0.7655 - Binary-Cell-Jacard: 0.6727 - bPQ-Score: 0.5720 - mPQ-Score: 0.4035 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:23:11,730 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:23:11,730 [INFO] - Epoch: 28/130
2023-09-20 10:25:00,670 [INFO] - Training epoch stats:     Loss: 2.6959 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:29:31,134 [INFO] - Validation epoch stats:   Loss: 2.8020 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6722 - bPQ-Score: 0.5765 - mPQ-Score: 0.4290 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:29:31,139 [INFO] - New best model - save checkpoint
2023-09-20 10:30:07,604 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:30:07,604 [INFO] - Epoch: 29/130
2023-09-20 10:31:56,486 [INFO] - Training epoch stats:     Loss: 2.6570 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:36:29,463 [INFO] - Validation epoch stats:   Loss: 2.8113 - Binary-Cell-Dice: 0.7671 - Binary-Cell-Jacard: 0.6718 - bPQ-Score: 0.5657 - mPQ-Score: 0.4139 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:37:01,888 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:37:01,889 [INFO] - Epoch: 30/130
2023-09-20 10:38:50,253 [INFO] - Training epoch stats:     Loss: 2.6012 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:43:09,354 [INFO] - Validation epoch stats:   Loss: 2.7702 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6760 - bPQ-Score: 0.5874 - mPQ-Score: 0.4346 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:43:09,358 [INFO] - New best model - save checkpoint
2023-09-20 10:43:45,524 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:43:45,525 [INFO] - Epoch: 31/130
2023-09-20 10:45:35,198 [INFO] - Training epoch stats:     Loss: 2.5978 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:49:58,291 [INFO] - Validation epoch stats:   Loss: 2.7757 - Binary-Cell-Dice: 0.7583 - Binary-Cell-Jacard: 0.6642 - bPQ-Score: 0.5715 - mPQ-Score: 0.4179 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:50:25,780 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:50:25,781 [INFO] - Epoch: 32/130
2023-09-20 10:52:18,635 [INFO] - Training epoch stats:     Loss: 2.6525 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:56:49,317 [INFO] - Validation epoch stats:   Loss: 2.8404 - Binary-Cell-Dice: 0.7570 - Binary-Cell-Jacard: 0.6615 - bPQ-Score: 0.5780 - mPQ-Score: 0.4371 - Tissue-MC-Acc.: 0.0000
2023-09-20 10:57:19,393 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 10:57:19,393 [INFO] - Epoch: 33/130
2023-09-20 10:59:12,529 [INFO] - Training epoch stats:     Loss: 2.6498 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:03:44,691 [INFO] - Validation epoch stats:   Loss: 2.8204 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6754 - bPQ-Score: 0.5760 - mPQ-Score: 0.4213 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:04:13,347 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:04:13,348 [INFO] - Epoch: 34/130
2023-09-20 11:06:06,055 [INFO] - Training epoch stats:     Loss: 2.6135 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:10:45,994 [INFO] - Validation epoch stats:   Loss: 2.8849 - Binary-Cell-Dice: 0.7706 - Binary-Cell-Jacard: 0.6791 - bPQ-Score: 0.5887 - mPQ-Score: 0.4335 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:10:46,004 [INFO] - New best model - save checkpoint
2023-09-20 11:11:58,463 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:11:58,463 [INFO] - Epoch: 35/130
2023-09-20 11:13:51,214 [INFO] - Training epoch stats:     Loss: 2.5838 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:18:02,206 [INFO] - Validation epoch stats:   Loss: 2.9296 - Binary-Cell-Dice: 0.7716 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.5902 - mPQ-Score: 0.4245 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:18:02,210 [INFO] - New best model - save checkpoint
2023-09-20 11:18:36,538 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:18:36,538 [INFO] - Epoch: 36/130
2023-09-20 11:20:25,774 [INFO] - Training epoch stats:     Loss: 2.5362 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:25:23,519 [INFO] - Validation epoch stats:   Loss: 2.7334 - Binary-Cell-Dice: 0.7789 - Binary-Cell-Jacard: 0.6900 - bPQ-Score: 0.5966 - mPQ-Score: 0.4464 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:25:23,524 [INFO] - New best model - save checkpoint
2023-09-20 11:26:20,203 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:26:20,203 [INFO] - Epoch: 37/130
2023-09-20 11:28:19,419 [INFO] - Training epoch stats:     Loss: 2.5252 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:33:21,271 [INFO] - Validation epoch stats:   Loss: 2.7339 - Binary-Cell-Dice: 0.7749 - Binary-Cell-Jacard: 0.6852 - bPQ-Score: 0.5969 - mPQ-Score: 0.4525 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:33:21,302 [INFO] - New best model - save checkpoint
2023-09-20 11:34:00,545 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:34:00,545 [INFO] - Epoch: 38/130
2023-09-20 11:35:48,826 [INFO] - Training epoch stats:     Loss: 2.4833 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:40:48,818 [INFO] - Validation epoch stats:   Loss: 2.7413 - Binary-Cell-Dice: 0.7734 - Binary-Cell-Jacard: 0.6831 - bPQ-Score: 0.5908 - mPQ-Score: 0.4477 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:41:21,809 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:41:21,810 [INFO] - Epoch: 39/130
2023-09-20 11:43:11,667 [INFO] - Training epoch stats:     Loss: 2.5163 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:48:10,151 [INFO] - Validation epoch stats:   Loss: 2.8122 - Binary-Cell-Dice: 0.7706 - Binary-Cell-Jacard: 0.6765 - bPQ-Score: 0.5932 - mPQ-Score: 0.4290 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:48:56,682 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:48:56,683 [INFO] - Epoch: 40/130
2023-09-20 11:50:45,564 [INFO] - Training epoch stats:     Loss: 2.4770 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:55:52,733 [INFO] - Validation epoch stats:   Loss: 2.6922 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6839 - bPQ-Score: 0.5985 - mPQ-Score: 0.4544 - Tissue-MC-Acc.: 0.0000
2023-09-20 11:55:52,742 [INFO] - New best model - save checkpoint
2023-09-20 11:57:33,124 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 11:57:33,503 [INFO] - Epoch: 41/130
2023-09-20 11:59:25,273 [INFO] - Training epoch stats:     Loss: 2.4674 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:04:13,922 [INFO] - Validation epoch stats:   Loss: 2.8347 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6848 - bPQ-Score: 0.6018 - mPQ-Score: 0.4465 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:04:13,926 [INFO] - New best model - save checkpoint
2023-09-20 12:04:55,217 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:04:55,217 [INFO] - Epoch: 42/130
2023-09-20 12:06:44,493 [INFO] - Training epoch stats:     Loss: 2.5129 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:11:13,194 [INFO] - Validation epoch stats:   Loss: 2.8544 - Binary-Cell-Dice: 0.7531 - Binary-Cell-Jacard: 0.6545 - bPQ-Score: 0.5710 - mPQ-Score: 0.4161 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:12:09,982 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:12:09,983 [INFO] - Epoch: 43/130
2023-09-20 12:14:05,987 [INFO] - Training epoch stats:     Loss: 2.4808 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:18:48,441 [INFO] - Validation epoch stats:   Loss: 2.7831 - Binary-Cell-Dice: 0.7783 - Binary-Cell-Jacard: 0.6888 - bPQ-Score: 0.5888 - mPQ-Score: 0.4227 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:19:13,340 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:19:13,341 [INFO] - Epoch: 44/130
2023-09-20 12:21:01,957 [INFO] - Training epoch stats:     Loss: 2.4932 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:25:34,526 [INFO] - Validation epoch stats:   Loss: 2.8182 - Binary-Cell-Dice: 0.7548 - Binary-Cell-Jacard: 0.6573 - bPQ-Score: 0.5749 - mPQ-Score: 0.4325 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:26:06,460 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:26:06,461 [INFO] - Epoch: 45/130
2023-09-20 12:27:55,745 [INFO] - Training epoch stats:     Loss: 2.4029 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:32:35,382 [INFO] - Validation epoch stats:   Loss: 2.7511 - Binary-Cell-Dice: 0.7805 - Binary-Cell-Jacard: 0.6892 - bPQ-Score: 0.5941 - mPQ-Score: 0.4361 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:33:10,446 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:33:10,447 [INFO] - Epoch: 46/130
2023-09-20 12:34:59,013 [INFO] - Training epoch stats:     Loss: 2.4779 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:39:39,309 [INFO] - Validation epoch stats:   Loss: 2.7142 - Binary-Cell-Dice: 0.7803 - Binary-Cell-Jacard: 0.6915 - bPQ-Score: 0.6029 - mPQ-Score: 0.4554 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:39:39,319 [INFO] - New best model - save checkpoint
2023-09-20 12:41:49,283 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:41:49,289 [INFO] - Epoch: 47/130
2023-09-20 12:43:38,633 [INFO] - Training epoch stats:     Loss: 2.4187 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:48:15,327 [INFO] - Validation epoch stats:   Loss: 2.7271 - Binary-Cell-Dice: 0.7626 - Binary-Cell-Jacard: 0.6670 - bPQ-Score: 0.5828 - mPQ-Score: 0.4412 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:48:59,697 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:48:59,698 [INFO] - Epoch: 48/130
2023-09-20 12:51:50,933 [INFO] - Training epoch stats:     Loss: 2.4953 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:56:22,336 [INFO] - Validation epoch stats:   Loss: 2.8599 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6756 - bPQ-Score: 0.5948 - mPQ-Score: 0.4367 - Tissue-MC-Acc.: 0.0000
2023-09-20 12:57:20,311 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 12:57:20,311 [INFO] - Epoch: 49/130
2023-09-20 12:59:43,707 [INFO] - Training epoch stats:     Loss: 2.4990 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:04:31,372 [INFO] - Validation epoch stats:   Loss: 2.7650 - Binary-Cell-Dice: 0.7681 - Binary-Cell-Jacard: 0.6788 - bPQ-Score: 0.5925 - mPQ-Score: 0.4318 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:05:57,732 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:05:57,867 [INFO] - Epoch: 50/130
2023-09-20 13:08:05,221 [INFO] - Training epoch stats:     Loss: 2.3960 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:12:54,727 [INFO] - Validation epoch stats:   Loss: 2.7144 - Binary-Cell-Dice: 0.7643 - Binary-Cell-Jacard: 0.6687 - bPQ-Score: 0.5774 - mPQ-Score: 0.4217 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:13:57,324 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-20 13:13:57,325 [INFO] - Epoch: 51/130
2023-09-20 13:16:16,475 [INFO] - Training epoch stats:     Loss: 2.4114 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:20:50,003 [INFO] - Validation epoch stats:   Loss: 2.7976 - Binary-Cell-Dice: 0.7794 - Binary-Cell-Jacard: 0.6920 - bPQ-Score: 0.6058 - mPQ-Score: 0.4478 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:20:50,012 [INFO] - New best model - save checkpoint
2023-09-20 13:23:20,166 [DEBUG] - Old lr: 0.000100 - New lr: 0.000050
2023-09-20 13:23:20,172 [INFO] - Epoch: 52/130
2023-09-20 13:25:10,674 [INFO] - Training epoch stats:     Loss: 2.4025 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:29:59,919 [INFO] - Validation epoch stats:   Loss: 2.7520 - Binary-Cell-Dice: 0.7830 - Binary-Cell-Jacard: 0.6943 - bPQ-Score: 0.6060 - mPQ-Score: 0.4493 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:29:59,990 [INFO] - New best model - save checkpoint
2023-09-20 13:30:53,056 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 13:30:53,057 [INFO] - Epoch: 53/130
2023-09-20 13:32:47,545 [INFO] - Training epoch stats:     Loss: 2.3363 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:52:52,245 [INFO] - Validation epoch stats:   Loss: 2.7024 - Binary-Cell-Dice: 0.7818 - Binary-Cell-Jacard: 0.6948 - bPQ-Score: 0.6101 - mPQ-Score: 0.4597 - Tissue-MC-Acc.: 0.0000
2023-09-20 13:52:52,256 [INFO] - New best model - save checkpoint
2023-09-20 13:53:35,097 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 13:53:35,098 [INFO] - Epoch: 54/130
2023-09-20 13:55:30,612 [INFO] - Training epoch stats:     Loss: 2.2960 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:09:13,238 [INFO] - Validation epoch stats:   Loss: 2.6698 - Binary-Cell-Dice: 0.7804 - Binary-Cell-Jacard: 0.6934 - bPQ-Score: 0.6054 - mPQ-Score: 0.4555 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:09:50,041 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:09:50,042 [INFO] - Epoch: 55/130
2023-09-20 14:12:09,616 [INFO] - Training epoch stats:     Loss: 2.2628 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:17:09,698 [INFO] - Validation epoch stats:   Loss: 2.6876 - Binary-Cell-Dice: 0.7844 - Binary-Cell-Jacard: 0.6989 - bPQ-Score: 0.6088 - mPQ-Score: 0.4602 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:18:07,024 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:18:07,026 [INFO] - Epoch: 56/130
2023-09-20 14:20:03,452 [INFO] - Training epoch stats:     Loss: 2.2518 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:24:54,528 [INFO] - Validation epoch stats:   Loss: 2.6483 - Binary-Cell-Dice: 0.7777 - Binary-Cell-Jacard: 0.6902 - bPQ-Score: 0.6058 - mPQ-Score: 0.4598 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:26:05,098 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:26:05,099 [INFO] - Epoch: 57/130
2023-09-20 14:28:15,044 [INFO] - Training epoch stats:     Loss: 2.2488 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:32:43,232 [INFO] - Validation epoch stats:   Loss: 2.6326 - Binary-Cell-Dice: 0.7852 - Binary-Cell-Jacard: 0.6997 - bPQ-Score: 0.6115 - mPQ-Score: 0.4639 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:32:43,242 [INFO] - New best model - save checkpoint
2023-09-20 14:35:53,849 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:35:53,957 [INFO] - Epoch: 58/130
2023-09-20 14:37:48,106 [INFO] - Training epoch stats:     Loss: 2.2857 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:43:22,329 [INFO] - Validation epoch stats:   Loss: 2.6360 - Binary-Cell-Dice: 0.7767 - Binary-Cell-Jacard: 0.6884 - bPQ-Score: 0.6047 - mPQ-Score: 0.4597 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:44:30,663 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:44:30,664 [INFO] - Epoch: 59/130
2023-09-20 14:46:21,521 [INFO] - Training epoch stats:     Loss: 2.2240 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:51:34,006 [INFO] - Validation epoch stats:   Loss: 2.6685 - Binary-Cell-Dice: 0.7822 - Binary-Cell-Jacard: 0.6971 - bPQ-Score: 0.6119 - mPQ-Score: 0.4613 - Tissue-MC-Acc.: 0.0000
2023-09-20 14:51:34,240 [INFO] - New best model - save checkpoint
2023-09-20 14:54:16,959 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 14:54:16,966 [INFO] - Epoch: 60/130
2023-09-20 14:56:07,565 [INFO] - Training epoch stats:     Loss: 2.2133 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:01:06,597 [INFO] - Validation epoch stats:   Loss: 2.6398 - Binary-Cell-Dice: 0.7801 - Binary-Cell-Jacard: 0.6933 - bPQ-Score: 0.6111 - mPQ-Score: 0.4688 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:01:25,430 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:01:25,430 [INFO] - Epoch: 61/130
2023-09-20 15:03:14,051 [INFO] - Training epoch stats:     Loss: 2.2072 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:08:17,994 [INFO] - Validation epoch stats:   Loss: 2.6556 - Binary-Cell-Dice: 0.7826 - Binary-Cell-Jacard: 0.6958 - bPQ-Score: 0.6122 - mPQ-Score: 0.4594 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:08:18,004 [INFO] - New best model - save checkpoint
2023-09-20 15:09:05,261 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:09:05,261 [INFO] - Epoch: 62/130
2023-09-20 15:10:54,656 [INFO] - Training epoch stats:     Loss: 2.2155 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:15:46,216 [INFO] - Validation epoch stats:   Loss: 2.7378 - Binary-Cell-Dice: 0.7766 - Binary-Cell-Jacard: 0.6889 - bPQ-Score: 0.6047 - mPQ-Score: 0.4530 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:16:20,193 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:16:20,193 [INFO] - Epoch: 63/130
2023-09-20 15:18:12,268 [INFO] - Training epoch stats:     Loss: 2.1907 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:22:52,038 [INFO] - Validation epoch stats:   Loss: 2.6232 - Binary-Cell-Dice: 0.7844 - Binary-Cell-Jacard: 0.6965 - bPQ-Score: 0.6077 - mPQ-Score: 0.4643 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:24:06,999 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:24:07,000 [INFO] - Epoch: 64/130
2023-09-20 15:25:58,147 [INFO] - Training epoch stats:     Loss: 2.1850 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:30:05,740 [INFO] - Validation epoch stats:   Loss: 2.6985 - Binary-Cell-Dice: 0.7745 - Binary-Cell-Jacard: 0.6864 - bPQ-Score: 0.6078 - mPQ-Score: 0.4605 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:30:25,747 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:30:25,748 [INFO] - Epoch: 65/130
2023-09-20 15:32:18,608 [INFO] - Training epoch stats:     Loss: 2.1822 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:36:41,019 [INFO] - Validation epoch stats:   Loss: 2.6766 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6761 - bPQ-Score: 0.5963 - mPQ-Score: 0.4506 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:37:16,680 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:37:16,681 [INFO] - Epoch: 66/130
2023-09-20 15:39:07,831 [INFO] - Training epoch stats:     Loss: 2.0223 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:43:44,987 [INFO] - Validation epoch stats:   Loss: 2.4725 - Binary-Cell-Dice: 0.7855 - Binary-Cell-Jacard: 0.6992 - bPQ-Score: 0.6132 - mPQ-Score: 0.4690 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:43:44,997 [INFO] - New best model - save checkpoint
2023-09-20 15:45:16,321 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:45:16,321 [INFO] - Epoch: 67/130
2023-09-20 15:47:18,206 [INFO] - Training epoch stats:     Loss: 1.7001 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:52:01,271 [INFO] - Validation epoch stats:   Loss: 2.4119 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6779 - bPQ-Score: 0.5959 - mPQ-Score: 0.4516 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:52:51,018 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 15:52:51,018 [INFO] - Epoch: 68/130
2023-09-20 15:54:39,878 [INFO] - Training epoch stats:     Loss: 1.7168 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:59:11,627 [INFO] - Validation epoch stats:   Loss: 2.4098 - Binary-Cell-Dice: 0.7859 - Binary-Cell-Jacard: 0.7002 - bPQ-Score: 0.6146 - mPQ-Score: 0.4584 - Tissue-MC-Acc.: 0.0000
2023-09-20 15:59:11,637 [INFO] - New best model - save checkpoint
2023-09-20 16:01:04,777 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:01:04,785 [INFO] - Epoch: 69/130
2023-09-20 16:02:57,676 [INFO] - Training epoch stats:     Loss: 1.7159 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:07:23,905 [INFO] - Validation epoch stats:   Loss: 2.3664 - Binary-Cell-Dice: 0.7831 - Binary-Cell-Jacard: 0.6970 - bPQ-Score: 0.6109 - mPQ-Score: 0.4677 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:07:42,146 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:07:42,146 [INFO] - Epoch: 70/130
2023-09-20 16:09:32,285 [INFO] - Training epoch stats:     Loss: 1.7045 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:14:14,709 [INFO] - Validation epoch stats:   Loss: 2.3837 - Binary-Cell-Dice: 0.7841 - Binary-Cell-Jacard: 0.6992 - bPQ-Score: 0.6102 - mPQ-Score: 0.4610 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:14:32,736 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:14:32,737 [INFO] - Epoch: 71/130
2023-09-20 16:16:22,494 [INFO] - Training epoch stats:     Loss: 1.6972 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:21:06,646 [INFO] - Validation epoch stats:   Loss: 2.3570 - Binary-Cell-Dice: 0.7843 - Binary-Cell-Jacard: 0.7010 - bPQ-Score: 0.6106 - mPQ-Score: 0.4589 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:21:51,386 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:21:51,386 [INFO] - Epoch: 72/130
2023-09-20 16:23:45,109 [INFO] - Training epoch stats:     Loss: 1.7011 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:28:20,949 [INFO] - Validation epoch stats:   Loss: 2.3993 - Binary-Cell-Dice: 0.7813 - Binary-Cell-Jacard: 0.6967 - bPQ-Score: 0.6120 - mPQ-Score: 0.4606 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:28:41,648 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:28:41,648 [INFO] - Epoch: 73/130
2023-09-20 16:30:33,112 [INFO] - Training epoch stats:     Loss: 1.7549 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:35:09,826 [INFO] - Validation epoch stats:   Loss: 2.3937 - Binary-Cell-Dice: 0.7818 - Binary-Cell-Jacard: 0.6943 - bPQ-Score: 0.6057 - mPQ-Score: 0.4557 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:36:16,149 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:36:16,150 [INFO] - Epoch: 74/130
2023-09-20 16:38:06,936 [INFO] - Training epoch stats:     Loss: 1.6733 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:42:42,812 [INFO] - Validation epoch stats:   Loss: 2.3773 - Binary-Cell-Dice: 0.7835 - Binary-Cell-Jacard: 0.7000 - bPQ-Score: 0.6133 - mPQ-Score: 0.4661 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:43:15,151 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:43:15,152 [INFO] - Epoch: 75/130
2023-09-20 16:45:08,967 [INFO] - Training epoch stats:     Loss: 1.6301 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:49:45,086 [INFO] - Validation epoch stats:   Loss: 2.3738 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.7018 - bPQ-Score: 0.6194 - mPQ-Score: 0.4727 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:49:45,117 [INFO] - New best model - save checkpoint
2023-09-20 16:50:25,866 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:50:25,867 [INFO] - Epoch: 76/130
2023-09-20 16:52:15,034 [INFO] - Training epoch stats:     Loss: 1.6175 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:56:51,874 [INFO] - Validation epoch stats:   Loss: 2.3635 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.6990 - bPQ-Score: 0.6126 - mPQ-Score: 0.4614 - Tissue-MC-Acc.: 0.0000
2023-09-20 16:57:51,172 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 16:57:51,172 [INFO] - Epoch: 77/130
2023-09-20 16:59:41,910 [INFO] - Training epoch stats:     Loss: 1.6285 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:04:25,522 [INFO] - Validation epoch stats:   Loss: 2.3682 - Binary-Cell-Dice: 0.7871 - Binary-Cell-Jacard: 0.7010 - bPQ-Score: 0.6172 - mPQ-Score: 0.4625 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:04:43,550 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:04:43,550 [INFO] - Epoch: 78/130
2023-09-20 17:06:33,356 [INFO] - Training epoch stats:     Loss: 1.6561 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:11:06,068 [INFO] - Validation epoch stats:   Loss: 2.3472 - Binary-Cell-Dice: 0.7773 - Binary-Cell-Jacard: 0.6897 - bPQ-Score: 0.6074 - mPQ-Score: 0.4612 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:11:45,807 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:11:45,808 [INFO] - Epoch: 79/130
2023-09-20 17:13:37,315 [INFO] - Training epoch stats:     Loss: 1.6353 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:18:18,978 [INFO] - Validation epoch stats:   Loss: 2.3607 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.7008 - bPQ-Score: 0.6156 - mPQ-Score: 0.4724 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:18:59,434 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:18:59,435 [INFO] - Epoch: 80/130
2023-09-20 17:20:48,827 [INFO] - Training epoch stats:     Loss: 1.6285 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:25:13,251 [INFO] - Validation epoch stats:   Loss: 2.3875 - Binary-Cell-Dice: 0.7699 - Binary-Cell-Jacard: 0.6803 - bPQ-Score: 0.5956 - mPQ-Score: 0.4488 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:25:46,854 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:25:46,854 [INFO] - Epoch: 81/130
2023-09-20 17:27:37,646 [INFO] - Training epoch stats:     Loss: 1.6698 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:32:22,826 [INFO] - Validation epoch stats:   Loss: 2.3772 - Binary-Cell-Dice: 0.7805 - Binary-Cell-Jacard: 0.6925 - bPQ-Score: 0.6062 - mPQ-Score: 0.4591 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:32:40,836 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:32:40,836 [INFO] - Epoch: 82/130
2023-09-20 17:34:29,119 [INFO] - Training epoch stats:     Loss: 1.6190 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:39:09,990 [INFO] - Validation epoch stats:   Loss: 2.4158 - Binary-Cell-Dice: 0.7828 - Binary-Cell-Jacard: 0.6986 - bPQ-Score: 0.6138 - mPQ-Score: 0.4547 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:39:27,822 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:39:27,823 [INFO] - Epoch: 83/130
2023-09-20 17:41:18,016 [INFO] - Training epoch stats:     Loss: 1.5828 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:46:02,816 [INFO] - Validation epoch stats:   Loss: 2.3817 - Binary-Cell-Dice: 0.7747 - Binary-Cell-Jacard: 0.6869 - bPQ-Score: 0.6065 - mPQ-Score: 0.4587 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:46:23,453 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:46:23,453 [INFO] - Epoch: 84/130
2023-09-20 17:48:13,509 [INFO] - Training epoch stats:     Loss: 1.5624 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:53:14,789 [INFO] - Validation epoch stats:   Loss: 2.3936 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.7004 - bPQ-Score: 0.6156 - mPQ-Score: 0.4678 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:53:32,569 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 17:53:32,569 [INFO] - Epoch: 85/130
2023-09-20 17:55:22,533 [INFO] - Training epoch stats:     Loss: 1.5957 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 17:59:55,139 [INFO] - Validation epoch stats:   Loss: 2.3721 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.7033 - bPQ-Score: 0.6186 - mPQ-Score: 0.4740 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:00:14,170 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:00:14,171 [INFO] - Epoch: 86/130
2023-09-20 18:02:04,428 [INFO] - Training epoch stats:     Loss: 1.6258 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:06:36,457 [INFO] - Validation epoch stats:   Loss: 2.4108 - Binary-Cell-Dice: 0.7766 - Binary-Cell-Jacard: 0.6892 - bPQ-Score: 0.6071 - mPQ-Score: 0.4563 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:06:53,672 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:06:53,673 [INFO] - Epoch: 87/130
2023-09-20 18:08:43,165 [INFO] - Training epoch stats:     Loss: 1.5974 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:13:19,474 [INFO] - Validation epoch stats:   Loss: 2.3794 - Binary-Cell-Dice: 0.7836 - Binary-Cell-Jacard: 0.6991 - bPQ-Score: 0.6142 - mPQ-Score: 0.4651 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:13:39,490 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:13:39,490 [INFO] - Epoch: 88/130
2023-09-20 18:15:28,319 [INFO] - Training epoch stats:     Loss: 1.5965 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:20:04,493 [INFO] - Validation epoch stats:   Loss: 2.3912 - Binary-Cell-Dice: 0.7838 - Binary-Cell-Jacard: 0.6994 - bPQ-Score: 0.6157 - mPQ-Score: 0.4590 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:20:51,910 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-20 18:20:51,911 [INFO] - Epoch: 89/130
2023-09-20 18:22:40,334 [INFO] - Training epoch stats:     Loss: 1.5215 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:27:15,915 [INFO] - Validation epoch stats:   Loss: 2.4225 - Binary-Cell-Dice: 0.7872 - Binary-Cell-Jacard: 0.7025 - bPQ-Score: 0.6146 - mPQ-Score: 0.4470 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:27:55,150 [DEBUG] - Old lr: 0.000050 - New lr: 0.000025
2023-09-20 18:27:55,151 [INFO] - Epoch: 90/130
2023-09-20 18:29:46,977 [INFO] - Training epoch stats:     Loss: 1.5370 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:34:15,840 [INFO] - Validation epoch stats:   Loss: 2.3424 - Binary-Cell-Dice: 0.7810 - Binary-Cell-Jacard: 0.6960 - bPQ-Score: 0.6130 - mPQ-Score: 0.4669 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:34:34,251 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:34:34,252 [INFO] - Epoch: 91/130
2023-09-20 18:36:23,974 [INFO] - Training epoch stats:     Loss: 1.5517 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:40:54,828 [INFO] - Validation epoch stats:   Loss: 2.3449 - Binary-Cell-Dice: 0.7873 - Binary-Cell-Jacard: 0.7040 - bPQ-Score: 0.6170 - mPQ-Score: 0.4725 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:41:31,489 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:41:31,490 [INFO] - Epoch: 92/130
2023-09-20 18:43:21,509 [INFO] - Training epoch stats:     Loss: 1.5022 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:47:56,449 [INFO] - Validation epoch stats:   Loss: 2.3435 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.7038 - bPQ-Score: 0.6174 - mPQ-Score: 0.4718 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:48:14,171 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:48:14,171 [INFO] - Epoch: 93/130
2023-09-20 18:50:03,335 [INFO] - Training epoch stats:     Loss: 1.4796 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:54:44,011 [INFO] - Validation epoch stats:   Loss: 2.3533 - Binary-Cell-Dice: 0.7852 - Binary-Cell-Jacard: 0.7003 - bPQ-Score: 0.6131 - mPQ-Score: 0.4667 - Tissue-MC-Acc.: 0.0000
2023-09-20 18:55:24,174 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 18:55:24,175 [INFO] - Epoch: 94/130
2023-09-20 18:57:13,746 [INFO] - Training epoch stats:     Loss: 1.4864 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:01:47,722 [INFO] - Validation epoch stats:   Loss: 2.3592 - Binary-Cell-Dice: 0.7810 - Binary-Cell-Jacard: 0.6961 - bPQ-Score: 0.6103 - mPQ-Score: 0.4700 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:02:08,259 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:02:08,259 [INFO] - Epoch: 95/130
2023-09-20 19:03:56,670 [INFO] - Training epoch stats:     Loss: 1.4884 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:08:29,561 [INFO] - Validation epoch stats:   Loss: 2.3770 - Binary-Cell-Dice: 0.7823 - Binary-Cell-Jacard: 0.6956 - bPQ-Score: 0.6144 - mPQ-Score: 0.4651 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:08:46,774 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:08:46,774 [INFO] - Epoch: 96/130
2023-09-20 19:10:36,257 [INFO] - Training epoch stats:     Loss: 1.4640 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:15:12,060 [INFO] - Validation epoch stats:   Loss: 2.3525 - Binary-Cell-Dice: 0.7887 - Binary-Cell-Jacard: 0.7056 - bPQ-Score: 0.6188 - mPQ-Score: 0.4729 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:15:29,135 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:15:29,136 [INFO] - Epoch: 97/130
2023-09-20 19:17:17,449 [INFO] - Training epoch stats:     Loss: 1.4864 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:21:37,630 [INFO] - Validation epoch stats:   Loss: 2.3755 - Binary-Cell-Dice: 0.7811 - Binary-Cell-Jacard: 0.6971 - bPQ-Score: 0.6150 - mPQ-Score: 0.4663 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:21:55,034 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:21:55,034 [INFO] - Epoch: 98/130
2023-09-20 19:23:45,128 [INFO] - Training epoch stats:     Loss: 1.4605 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:28:25,555 [INFO] - Validation epoch stats:   Loss: 2.3543 - Binary-Cell-Dice: 0.7869 - Binary-Cell-Jacard: 0.7024 - bPQ-Score: 0.6168 - mPQ-Score: 0.4679 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:28:42,381 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:28:42,381 [INFO] - Epoch: 99/130
2023-09-20 19:30:32,327 [INFO] - Training epoch stats:     Loss: 1.4800 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:35:14,572 [INFO] - Validation epoch stats:   Loss: 2.3791 - Binary-Cell-Dice: 0.7848 - Binary-Cell-Jacard: 0.6999 - bPQ-Score: 0.6148 - mPQ-Score: 0.4622 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:35:32,934 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:35:32,935 [INFO] - Epoch: 100/130
2023-09-20 19:37:22,912 [INFO] - Training epoch stats:     Loss: 1.4654 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:42:02,372 [INFO] - Validation epoch stats:   Loss: 2.3630 - Binary-Cell-Dice: 0.7835 - Binary-Cell-Jacard: 0.6985 - bPQ-Score: 0.6122 - mPQ-Score: 0.4672 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:42:21,082 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-20 19:42:21,083 [INFO] - Epoch: 101/130
2023-09-20 19:44:11,384 [INFO] - Training epoch stats:     Loss: 1.4619 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:48:41,796 [INFO] - Validation epoch stats:   Loss: 2.3563 - Binary-Cell-Dice: 0.7846 - Binary-Cell-Jacard: 0.6992 - bPQ-Score: 0.6159 - mPQ-Score: 0.4724 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:48:58,832 [DEBUG] - Old lr: 0.000025 - New lr: 0.000013
2023-09-20 19:48:58,833 [INFO] - Epoch: 102/130
2023-09-20 19:50:47,637 [INFO] - Training epoch stats:     Loss: 1.4466 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:55:27,298 [INFO] - Validation epoch stats:   Loss: 2.3583 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.7030 - bPQ-Score: 0.6173 - mPQ-Score: 0.4724 - Tissue-MC-Acc.: 0.0000
2023-09-20 19:55:54,884 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 19:55:54,885 [INFO] - Epoch: 103/130
2023-09-20 19:57:43,831 [INFO] - Training epoch stats:     Loss: 1.4357 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:02:15,711 [INFO] - Validation epoch stats:   Loss: 2.3566 - Binary-Cell-Dice: 0.7873 - Binary-Cell-Jacard: 0.7044 - bPQ-Score: 0.6185 - mPQ-Score: 0.4713 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:02:32,938 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:02:32,938 [INFO] - Epoch: 104/130
2023-09-20 20:04:20,600 [INFO] - Training epoch stats:     Loss: 1.4394 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:08:52,446 [INFO] - Validation epoch stats:   Loss: 2.3744 - Binary-Cell-Dice: 0.7838 - Binary-Cell-Jacard: 0.6983 - bPQ-Score: 0.6149 - mPQ-Score: 0.4658 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:09:10,856 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:09:10,857 [INFO] - Epoch: 105/130
2023-09-20 20:11:01,019 [INFO] - Training epoch stats:     Loss: 1.4226 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:15:40,557 [INFO] - Validation epoch stats:   Loss: 2.3709 - Binary-Cell-Dice: 0.7877 - Binary-Cell-Jacard: 0.7029 - bPQ-Score: 0.6199 - mPQ-Score: 0.4713 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:15:40,561 [INFO] - New best model - save checkpoint
2023-09-20 20:16:18,135 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:16:18,135 [INFO] - Epoch: 106/130
2023-09-20 20:18:05,958 [INFO] - Training epoch stats:     Loss: 1.4663 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:22:58,550 [INFO] - Validation epoch stats:   Loss: 2.3606 - Binary-Cell-Dice: 0.7875 - Binary-Cell-Jacard: 0.7027 - bPQ-Score: 0.6194 - mPQ-Score: 0.4688 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:23:15,921 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:23:15,921 [INFO] - Epoch: 107/130
2023-09-20 20:25:05,663 [INFO] - Training epoch stats:     Loss: 1.4375 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:29:42,325 [INFO] - Validation epoch stats:   Loss: 2.3727 - Binary-Cell-Dice: 0.7887 - Binary-Cell-Jacard: 0.7066 - bPQ-Score: 0.6224 - mPQ-Score: 0.4719 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:29:42,330 [INFO] - New best model - save checkpoint
2023-09-20 20:30:15,720 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:30:15,721 [INFO] - Epoch: 108/130
2023-09-20 20:32:05,009 [INFO] - Training epoch stats:     Loss: 1.4535 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:36:35,807 [INFO] - Validation epoch stats:   Loss: 2.3657 - Binary-Cell-Dice: 0.7856 - Binary-Cell-Jacard: 0.7028 - bPQ-Score: 0.6185 - mPQ-Score: 0.4718 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:37:00,701 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:37:00,701 [INFO] - Epoch: 109/130
2023-09-20 20:38:54,078 [INFO] - Training epoch stats:     Loss: 1.4078 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:43:32,819 [INFO] - Validation epoch stats:   Loss: 2.3665 - Binary-Cell-Dice: 0.7835 - Binary-Cell-Jacard: 0.6989 - bPQ-Score: 0.6122 - mPQ-Score: 0.4664 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:43:48,870 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:43:48,870 [INFO] - Epoch: 110/130
2023-09-20 20:45:38,056 [INFO] - Training epoch stats:     Loss: 1.4123 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:50:15,134 [INFO] - Validation epoch stats:   Loss: 2.3707 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.7030 - bPQ-Score: 0.6189 - mPQ-Score: 0.4695 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:50:46,392 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:50:46,393 [INFO] - Epoch: 111/130
2023-09-20 20:52:36,795 [INFO] - Training epoch stats:     Loss: 1.3947 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:57:12,297 [INFO] - Validation epoch stats:   Loss: 2.3604 - Binary-Cell-Dice: 0.7872 - Binary-Cell-Jacard: 0.7042 - bPQ-Score: 0.6168 - mPQ-Score: 0.4714 - Tissue-MC-Acc.: 0.0000
2023-09-20 20:57:36,363 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-20 20:57:36,363 [INFO] - Epoch: 112/130
2023-09-20 20:59:25,905 [INFO] - Training epoch stats:     Loss: 1.4382 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:04:04,117 [INFO] - Validation epoch stats:   Loss: 2.3724 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.7015 - bPQ-Score: 0.6168 - mPQ-Score: 0.4676 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:04:35,964 [DEBUG] - Old lr: 0.000013 - New lr: 0.000006
2023-09-20 21:04:35,965 [INFO] - Epoch: 113/130
2023-09-20 21:06:26,368 [INFO] - Training epoch stats:     Loss: 1.4416 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:11:00,114 [INFO] - Validation epoch stats:   Loss: 2.3735 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.7030 - bPQ-Score: 0.6177 - mPQ-Score: 0.4676 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:11:18,303 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:11:18,304 [INFO] - Epoch: 114/130
2023-09-20 21:13:08,764 [INFO] - Training epoch stats:     Loss: 1.4036 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:17:45,023 [INFO] - Validation epoch stats:   Loss: 2.3685 - Binary-Cell-Dice: 0.7853 - Binary-Cell-Jacard: 0.7009 - bPQ-Score: 0.6158 - mPQ-Score: 0.4679 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:18:26,113 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:18:26,114 [INFO] - Epoch: 115/130
2023-09-20 21:20:35,138 [INFO] - Training epoch stats:     Loss: 1.4072 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:25:16,539 [INFO] - Validation epoch stats:   Loss: 2.3693 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.7025 - bPQ-Score: 0.6192 - mPQ-Score: 0.4703 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:25:33,514 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:25:33,515 [INFO] - Epoch: 116/130
2023-09-20 21:27:22,477 [INFO] - Training epoch stats:     Loss: 1.4058 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:31:54,973 [INFO] - Validation epoch stats:   Loss: 2.3687 - Binary-Cell-Dice: 0.7871 - Binary-Cell-Jacard: 0.7030 - bPQ-Score: 0.6198 - mPQ-Score: 0.4717 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:32:27,836 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:32:27,836 [INFO] - Epoch: 117/130
2023-09-20 21:34:17,162 [INFO] - Training epoch stats:     Loss: 1.3989 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:39:00,311 [INFO] - Validation epoch stats:   Loss: 2.3716 - Binary-Cell-Dice: 0.7878 - Binary-Cell-Jacard: 0.7040 - bPQ-Score: 0.6193 - mPQ-Score: 0.4699 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:39:27,911 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:39:27,912 [INFO] - Epoch: 118/130
2023-09-20 21:41:15,906 [INFO] - Training epoch stats:     Loss: 1.4087 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:45:38,745 [INFO] - Validation epoch stats:   Loss: 2.3828 - Binary-Cell-Dice: 0.7863 - Binary-Cell-Jacard: 0.7032 - bPQ-Score: 0.6186 - mPQ-Score: 0.4699 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:45:57,958 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:45:57,959 [INFO] - Epoch: 119/130
2023-09-20 21:47:46,058 [INFO] - Training epoch stats:     Loss: 1.4017 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:52:14,112 [INFO] - Validation epoch stats:   Loss: 2.3800 - Binary-Cell-Dice: 0.7868 - Binary-Cell-Jacard: 0.7026 - bPQ-Score: 0.6176 - mPQ-Score: 0.4669 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:52:33,060 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:52:33,060 [INFO] - Epoch: 120/130
2023-09-20 21:54:21,561 [INFO] - Training epoch stats:     Loss: 1.4085 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:58:59,144 [INFO] - Validation epoch stats:   Loss: 2.3715 - Binary-Cell-Dice: 0.7863 - Binary-Cell-Jacard: 0.7028 - bPQ-Score: 0.6171 - mPQ-Score: 0.4726 - Tissue-MC-Acc.: 0.0000
2023-09-20 21:59:31,461 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 21:59:31,461 [INFO] - Epoch: 121/130
2023-09-20 22:01:21,205 [INFO] - Training epoch stats:     Loss: 1.3806 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:06:06,561 [INFO] - Validation epoch stats:   Loss: 2.3742 - Binary-Cell-Dice: 0.7845 - Binary-Cell-Jacard: 0.6999 - bPQ-Score: 0.6157 - mPQ-Score: 0.4670 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:06:56,141 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 22:06:56,142 [INFO] - Epoch: 122/130
2023-09-20 22:08:48,778 [INFO] - Training epoch stats:     Loss: 1.4045 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:13:21,247 [INFO] - Validation epoch stats:   Loss: 2.3646 - Binary-Cell-Dice: 0.7853 - Binary-Cell-Jacard: 0.7014 - bPQ-Score: 0.6168 - mPQ-Score: 0.4689 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:13:39,096 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-20 22:13:39,096 [INFO] - Epoch: 123/130
2023-09-20 22:15:28,461 [INFO] - Training epoch stats:     Loss: 1.3964 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:19:46,437 [INFO] - Validation epoch stats:   Loss: 2.3737 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.7009 - bPQ-Score: 0.6150 - mPQ-Score: 0.4704 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:20:19,816 [DEBUG] - Old lr: 0.000006 - New lr: 0.000003
2023-09-20 22:20:19,816 [INFO] - Epoch: 124/130
2023-09-20 22:22:13,033 [INFO] - Training epoch stats:     Loss: 1.3541 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:26:32,824 [INFO] - Validation epoch stats:   Loss: 2.3708 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.7020 - bPQ-Score: 0.6177 - mPQ-Score: 0.4714 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:27:11,905 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:27:11,906 [INFO] - Epoch: 125/130
2023-09-20 22:29:05,570 [INFO] - Training epoch stats:     Loss: 1.3643 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:33:14,804 [INFO] - Validation epoch stats:   Loss: 2.3765 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.7017 - bPQ-Score: 0.6177 - mPQ-Score: 0.4701 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:33:39,002 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:33:39,002 [INFO] - Epoch: 126/130
2023-09-20 22:35:32,677 [INFO] - Training epoch stats:     Loss: 1.4205 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:39:52,797 [INFO] - Validation epoch stats:   Loss: 2.3722 - Binary-Cell-Dice: 0.7858 - Binary-Cell-Jacard: 0.7018 - bPQ-Score: 0.6173 - mPQ-Score: 0.4696 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:40:11,567 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:40:11,567 [INFO] - Epoch: 127/130
2023-09-20 22:42:01,128 [INFO] - Training epoch stats:     Loss: 1.3586 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:46:08,361 [INFO] - Validation epoch stats:   Loss: 2.3661 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.7028 - bPQ-Score: 0.6195 - mPQ-Score: 0.4709 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:46:26,620 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:46:26,621 [INFO] - Epoch: 128/130
2023-09-20 22:48:16,442 [INFO] - Training epoch stats:     Loss: 1.3646 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:52:50,720 [INFO] - Validation epoch stats:   Loss: 2.3706 - Binary-Cell-Dice: 0.7869 - Binary-Cell-Jacard: 0.7029 - bPQ-Score: 0.6172 - mPQ-Score: 0.4700 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:53:09,656 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:53:09,656 [INFO] - Epoch: 129/130
2023-09-20 22:54:59,208 [INFO] - Training epoch stats:     Loss: 1.4012 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:59:12,930 [INFO] - Validation epoch stats:   Loss: 2.3690 - Binary-Cell-Dice: 0.7851 - Binary-Cell-Jacard: 0.7012 - bPQ-Score: 0.6162 - mPQ-Score: 0.4690 - Tissue-MC-Acc.: 0.0000
2023-09-20 22:59:30,284 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 22:59:30,284 [INFO] - Epoch: 130/130
2023-09-20 23:01:20,532 [INFO] - Training epoch stats:     Loss: 1.4156 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:05:53,799 [INFO] - Validation epoch stats:   Loss: 2.3776 - Binary-Cell-Dice: 0.7871 - Binary-Cell-Jacard: 0.7028 - bPQ-Score: 0.6189 - mPQ-Score: 0.4725 - Tissue-MC-Acc.: 0.0000
2023-09-20 23:06:14,978 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-20 23:06:14,986 [INFO] -
