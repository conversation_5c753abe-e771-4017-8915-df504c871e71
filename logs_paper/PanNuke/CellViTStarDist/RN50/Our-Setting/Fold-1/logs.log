2023-09-18 23:54:35,248 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-18 23:54:39,624 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7ef9066beca0>]
2023-09-18 23:54:39,624 [INFO] - Using GPU: cuda:0
2023-09-18 23:54:39,624 [INFO] - Using device: cuda:0
2023-09-18 23:54:39,625 [INFO] - Loss functions:
2023-09-18 23:54:39,625 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON><PERSON>(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-18 23:54:52,205 [INFO] -
Model: StarDistRN50(
  (encoder): ResNet(
    (conv1): Conv2d(3, 64, kernel_size=(7, 7), stride=(2, 2), padding=(3, 3), bias=False)
    (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (relu): ReLU(inplace=True)
    (maxpool): MaxPool2d(kernel_size=3, stride=2, padding=1, dilation=1, ceil_mode=False)
    (layer1): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(64, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(256, 64, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(64, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer2): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(256, 512, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(512, 128, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(128, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer3): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(512, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(512, 1024, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (3): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (4): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (5): Bottleneck(
        (conv1): Conv2d(1024, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(256, 1024, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(1024, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
    (layer4): Sequential(
      (0): Bottleneck(
        (conv1): Conv2d(1024, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(2, 2), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
        (downsample): Sequential(
          (0): Conv2d(1024, 2048, kernel_size=(1, 1), stride=(2, 2), bias=False)
          (1): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        )
      )
      (1): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
      (2): Bottleneck(
        (conv1): Conv2d(2048, 512, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv2): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
        (bn2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (conv3): Conv2d(512, 2048, kernel_size=(1, 1), stride=(1, 1), bias=False)
        (bn3): BatchNorm2d(2048, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (relu): ReLU(inplace=True)
      )
    )
  )
  (up1): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4): Upsample(scale_factor=2.0, mode=bilinear)
  (features): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (out_prob): outconv(
    (conv): Conv2d(256, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (out_ray): outconv(
    (conv): Conv2d(256, 32, kernel_size=(1, 1), stride=(1, 1))
  )
  (up1_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(3072, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(1024, 1024, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(128, 1024, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up2_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(1536, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(64, 512, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(64, 512, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up3_seg): up(
    (up): Upsample(scale_factor=2.0, mode=bilinear)
    (conv): double_conv(
      (conv): Sequential(
        (0): Conv2d(768, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): GroupNorm(32, 256, eps=1e-05, affine=True)
        (2): ELU(alpha=1.0, inplace=True)
        (3): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (4): GroupNorm(32, 256, eps=1e-05, affine=True)
        (5): ELU(alpha=1.0, inplace=True)
      )
    )
  )
  (up4_seg): Upsample(scale_factor=2.0, mode=bilinear)
  (out_seg): outconv(
    (conv): Conv2d(256, 6, kernel_size=(1, 1), stride=(1, 1))
  )
  (final_activation_ray): ReLU()
)
2023-09-18 23:54:54,866 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
StarDistRN50                                  [1, 6, 256, 256]          --
├─ResNet: 1-1                                 [1, 256, 128, 128]        --
│    └─Conv2d: 2-1                            [1, 64, 128, 128]         9,408
│    └─BatchNorm2d: 2-2                       [1, 64, 128, 128]         128
│    └─ReLU: 2-3                              [1, 64, 128, 128]         --
│    └─Sequential: 2-4                        [1, 256, 128, 128]        --
│    │    └─Bottleneck: 3-1                   [1, 256, 128, 128]        75,008
│    │    └─Bottleneck: 3-2                   [1, 256, 128, 128]        70,400
│    │    └─Bottleneck: 3-3                   [1, 256, 128, 128]        70,400
│    └─Sequential: 2-5                        [1, 512, 64, 64]          --
│    │    └─Bottleneck: 3-4                   [1, 512, 64, 64]          379,392
│    │    └─Bottleneck: 3-5                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-6                   [1, 512, 64, 64]          280,064
│    │    └─Bottleneck: 3-7                   [1, 512, 64, 64]          280,064
│    └─Sequential: 2-6                        [1, 1024, 32, 32]         --
│    │    └─Bottleneck: 3-8                   [1, 1024, 32, 32]         1,512,448
│    │    └─Bottleneck: 3-9                   [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-10                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-11                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-12                  [1, 1024, 32, 32]         1,117,184
│    │    └─Bottleneck: 3-13                  [1, 1024, 32, 32]         1,117,184
│    └─Sequential: 2-7                        [1, 2048, 16, 16]         --
│    │    └─Bottleneck: 3-14                  [1, 2048, 16, 16]         6,039,552
│    │    └─Bottleneck: 3-15                  [1, 2048, 16, 16]         4,462,592
│    │    └─Bottleneck: 3-16                  [1, 2048, 16, 16]         4,462,592
├─up: 1-2                                     [1, 1024, 32, 32]         --
│    └─Upsample: 2-8                          [1, 2048, 32, 32]         --
│    └─double_conv: 2-9                       [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-17                  [1, 1024, 32, 32]         37,754,880
├─up: 1-3                                     [1, 512, 64, 64]          --
│    └─Upsample: 2-10                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-11                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-18                  [1, 512, 64, 64]          9,440,256
├─up: 1-4                                     [1, 256, 128, 128]        --
│    └─Upsample: 2-12                         [1, 512, 128, 128]        --
│    └─double_conv: 2-13                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-19                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-5                               [1, 256, 256, 256]        --
├─Conv2d: 1-6                                 [1, 256, 256, 256]        590,080
├─outconv: 1-7                                [1, 1, 256, 256]          --
│    └─Conv2d: 2-14                           [1, 1, 256, 256]          257
├─outconv: 1-8                                [1, 32, 256, 256]         --
│    └─Conv2d: 2-15                           [1, 32, 256, 256]         8,224
├─ReLU: 1-9                                   [1, 32, 256, 256]         --
├─up: 1-10                                    [1, 1024, 32, 32]         --
│    └─Upsample: 2-16                         [1, 2048, 32, 32]         --
│    └─double_conv: 2-17                      [1, 1024, 32, 32]         --
│    │    └─Sequential: 3-20                  [1, 1024, 32, 32]         37,754,880
├─up: 1-11                                    [1, 512, 64, 64]          --
│    └─Upsample: 2-18                         [1, 1024, 64, 64]         --
│    └─double_conv: 2-19                      [1, 512, 64, 64]          --
│    │    └─Sequential: 3-21                  [1, 512, 64, 64]          9,440,256
├─up: 1-12                                    [1, 256, 128, 128]        --
│    └─Upsample: 2-20                         [1, 512, 128, 128]        --
│    └─double_conv: 2-21                      [1, 256, 128, 128]        --
│    │    └─Sequential: 3-22                  [1, 256, 128, 128]        2,360,832
├─Upsample: 1-13                              [1, 256, 256, 256]        --
├─outconv: 1-14                               [1, 6, 256, 256]          --
│    └─Conv2d: 2-22                           [1, 6, 256, 256]          1,542
===============================================================================================
Total params: 123,220,071
Trainable params: 123,220,071
Non-trainable params: 0
Total mult-adds (G): 292.18
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1503.13
Params size (MB): 492.88
Estimated Total Size (MB): 1996.80
===============================================================================================
2023-09-18 23:55:53,984 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-18 23:55:53,995 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-18 23:55:53,995 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-18 23:57:11,006 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-18 23:57:11,439 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314, 0.7691,
        0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9667, 0.9823,
        0.9883, 0.9902, 1.0044, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200, 1.0215,
        1.0260, 1.0278, 1.0367, 1.0449, 1.0536, 1.0910, 1.0913, 1.0922, 1.0927,
        1.1088, 1.1094, 1.1143, 1.1149, 1.1161, 1.1173, 1.1216, 1.1256, 1.1287,
        1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1480, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1660, 1.1777, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2580, 1.2595, 1.2621, 1.2651, 1.2663, 1.2666, 1.2669, 1.2678, 1.2680,
        1.2699, 1.2829, 1.2835, 1.2844, 1.2850, 1.2869, 1.2884, 1.2890, 1.2899,
        1.2902, 1.2905, 1.2915, 1.2929, 1.2972, 1.3055, 1.3072, 1.3141, 1.3156,
        1.3165, 1.3219, 1.3221, 1.3227, 1.3234, 1.3276, 1.3282, 1.3294, 1.3306,
        1.3321, 1.3380, 1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3766,
        1.3885, 1.3891, 1.3940, 1.3971, 1.4033, 1.4081, 1.4099, 1.4111, 1.4197,
        1.4275, 1.4277, 1.4332, 1.4336, 1.4362, 1.4377, 1.4407, 1.4419, 1.4436,
        1.4455, 1.4585, 1.4591, 1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897,
        1.4975, 1.5089, 1.5130, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5641,
        1.5647, 1.5696, 1.5702, 1.5712, 1.5746, 1.5953, 1.6174, 1.6260, 1.6871,
        1.6874, 1.7349, 1.7468, 1.7502, 1.7695, 1.8002, 1.8615, 1.8627, 1.8866,
        1.9432])
2023-09-18 23:57:11,440 [INFO] - Instantiate Trainer
2023-09-18 23:57:11,441 [INFO] - Calling Trainer Fit
2023-09-18 23:57:11,441 [INFO] - Starting training, total number of epochs: 130
2023-09-18 23:57:11,441 [INFO] - Epoch: 1/130
2023-09-19 00:00:25,673 [INFO] - Training epoch stats:     Loss: 3.9239 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:04:40,849 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-19 00:04:40,859 [INFO] - Epoch: 2/130
2023-09-19 00:07:06,957 [INFO] - Training epoch stats:     Loss: 3.5961 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:10:09,816 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-19 00:10:09,825 [INFO] - Epoch: 3/130
2023-09-19 00:12:28,196 [INFO] - Training epoch stats:     Loss: 3.5112 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:14:30,059 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-19 00:14:30,068 [INFO] - Epoch: 4/130
2023-09-19 00:16:35,596 [INFO] - Training epoch stats:     Loss: 3.4598 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:17:15,925 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-19 00:17:15,925 [INFO] - Epoch: 5/130
2023-09-19 00:19:17,529 [INFO] - Training epoch stats:     Loss: 3.4123 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:19:34,994 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-19 00:19:34,994 [INFO] - Epoch: 6/130
2023-09-19 00:21:35,795 [INFO] - Training epoch stats:     Loss: 3.3925 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:22:14,388 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-19 00:22:14,389 [INFO] - Epoch: 7/130
2023-09-19 00:24:16,894 [INFO] - Training epoch stats:     Loss: 3.3570 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:24:54,621 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-19 00:24:54,621 [INFO] - Epoch: 8/130
2023-09-19 00:26:55,958 [INFO] - Training epoch stats:     Loss: 3.3382 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:27:12,253 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-19 00:27:12,253 [INFO] - Epoch: 9/130
2023-09-19 00:29:12,185 [INFO] - Training epoch stats:     Loss: 3.3093 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:29:58,879 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-19 00:29:58,880 [INFO] - Epoch: 10/130
2023-09-19 00:32:04,770 [INFO] - Training epoch stats:     Loss: 3.2956 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:37:50,235 [INFO] - Validation epoch stats:   Loss: 3.2126 - Binary-Cell-Dice: 0.7316 - Binary-Cell-Jacard: 0.6292 - bPQ-Score: 0.5143 - mPQ-Score: 0.3660 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:37:50,274 [INFO] - New best model - save checkpoint
2023-09-19 00:39:08,634 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-19 00:39:08,641 [INFO] - Epoch: 11/130
2023-09-19 00:41:14,875 [INFO] - Training epoch stats:     Loss: 3.2906 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:41:34,251 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-19 00:41:34,251 [INFO] - Epoch: 12/130
2023-09-19 00:43:38,907 [INFO] - Training epoch stats:     Loss: 3.2715 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:44:13,824 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-19 00:44:13,825 [INFO] - Epoch: 13/130
2023-09-19 00:46:14,793 [INFO] - Training epoch stats:     Loss: 3.2630 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:47:06,719 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-19 00:47:06,719 [INFO] - Epoch: 14/130
2023-09-19 00:49:11,325 [INFO] - Training epoch stats:     Loss: 3.2363 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:50:05,762 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-19 00:50:05,762 [INFO] - Epoch: 15/130
2023-09-19 00:52:09,123 [INFO] - Training epoch stats:     Loss: 3.2346 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:52:46,570 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-19 00:52:46,571 [INFO] - Epoch: 16/130
2023-09-19 00:54:47,847 [INFO] - Training epoch stats:     Loss: 3.2421 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:55:09,838 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-19 00:55:09,838 [INFO] - Epoch: 17/130
2023-09-19 00:57:07,949 [INFO] - Training epoch stats:     Loss: 3.2251 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 00:57:45,968 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-19 00:57:45,969 [INFO] - Epoch: 18/130
2023-09-19 00:59:45,400 [INFO] - Training epoch stats:     Loss: 3.2249 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:00:03,229 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-19 01:00:03,229 [INFO] - Epoch: 19/130
2023-09-19 01:02:00,495 [INFO] - Training epoch stats:     Loss: 3.2003 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:02:32,274 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-19 01:02:32,275 [INFO] - Epoch: 20/130
2023-09-19 01:04:30,735 [INFO] - Training epoch stats:     Loss: 3.1944 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:11:38,584 [INFO] - Validation epoch stats:   Loss: 3.1837 - Binary-Cell-Dice: 0.7565 - Binary-Cell-Jacard: 0.6644 - bPQ-Score: 0.5554 - mPQ-Score: 0.3891 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:11:38,588 [INFO] - New best model - save checkpoint
2023-09-19 01:12:12,182 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-19 01:12:12,182 [INFO] - Epoch: 21/130
2023-09-19 01:14:08,899 [INFO] - Training epoch stats:     Loss: 3.2002 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:14:41,741 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-19 01:14:41,742 [INFO] - Epoch: 22/130
2023-09-19 01:16:40,835 [INFO] - Training epoch stats:     Loss: 3.1880 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:17:29,362 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-19 01:17:29,363 [INFO] - Epoch: 23/130
2023-09-19 01:19:32,514 [INFO] - Training epoch stats:     Loss: 3.1751 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:20:24,938 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-19 01:20:24,939 [INFO] - Epoch: 24/130
2023-09-19 01:22:28,234 [INFO] - Training epoch stats:     Loss: 3.1703 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:22:43,971 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-19 01:22:43,971 [INFO] - Epoch: 25/130
2023-09-19 01:24:43,556 [INFO] - Training epoch stats:     Loss: 3.1721 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:25:20,336 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-19 01:25:20,337 [INFO] - Epoch: 26/130
2023-09-19 01:27:20,433 [INFO] - Training epoch stats:     Loss: 3.1814 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:27:54,995 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-19 01:27:54,996 [INFO] - Epoch: 27/130
2023-09-19 01:30:17,384 [INFO] - Training epoch stats:     Loss: 3.1647 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:30:34,385 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-19 01:30:34,386 [INFO] - Epoch: 28/130
2023-09-19 01:32:35,233 [INFO] - Training epoch stats:     Loss: 3.1493 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:33:37,439 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-19 01:33:37,440 [INFO] - Epoch: 29/130
2023-09-19 01:37:57,915 [INFO] - Training epoch stats:     Loss: 3.1544 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:39:06,912 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-19 01:39:06,912 [INFO] - Epoch: 30/130
2023-09-19 01:44:01,659 [INFO] - Training epoch stats:     Loss: 3.1581 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:51:29,181 [INFO] - Validation epoch stats:   Loss: 3.1247 - Binary-Cell-Dice: 0.7690 - Binary-Cell-Jacard: 0.6777 - bPQ-Score: 0.5820 - mPQ-Score: 0.4325 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:51:29,292 [INFO] - New best model - save checkpoint
2023-09-19 01:53:19,091 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-19 01:53:19,100 [INFO] - Epoch: 31/130
2023-09-19 01:55:17,767 [INFO] - Training epoch stats:     Loss: 3.1375 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:56:30,000 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-19 01:56:30,000 [INFO] - Epoch: 32/130
2023-09-19 01:58:30,981 [INFO] - Training epoch stats:     Loss: 3.1286 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 01:58:49,230 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-19 01:58:49,231 [INFO] - Epoch: 33/130
2023-09-19 02:00:46,387 [INFO] - Training epoch stats:     Loss: 3.1333 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:01:03,082 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-19 02:01:03,082 [INFO] - Epoch: 34/130
2023-09-19 02:03:01,365 [INFO] - Training epoch stats:     Loss: 3.1374 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:03:19,411 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-19 02:03:19,412 [INFO] - Epoch: 35/130
2023-09-19 02:05:19,149 [INFO] - Training epoch stats:     Loss: 3.1257 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:05:55,320 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-19 02:05:55,320 [INFO] - Epoch: 36/130
2023-09-19 02:07:55,838 [INFO] - Training epoch stats:     Loss: 3.1182 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:09:01,320 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-19 02:09:01,321 [INFO] - Epoch: 37/130
2023-09-19 02:11:35,044 [INFO] - Training epoch stats:     Loss: 3.1136 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:12:51,882 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-19 02:12:51,883 [INFO] - Epoch: 38/130
2023-09-19 02:14:58,208 [INFO] - Training epoch stats:     Loss: 3.1167 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:15:28,624 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-19 02:15:28,624 [INFO] - Epoch: 39/130
2023-09-19 02:17:28,830 [INFO] - Training epoch stats:     Loss: 3.1136 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:18:16,236 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-19 02:18:16,237 [INFO] - Epoch: 40/130
2023-09-19 02:20:15,869 [INFO] - Training epoch stats:     Loss: 3.1196 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:26:54,286 [INFO] - Validation epoch stats:   Loss: 3.1028 - Binary-Cell-Dice: 0.7616 - Binary-Cell-Jacard: 0.6726 - bPQ-Score: 0.5792 - mPQ-Score: 0.4314 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:27:22,885 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-19 02:27:22,886 [INFO] - Epoch: 41/130
2023-09-19 02:29:26,577 [INFO] - Training epoch stats:     Loss: 3.1053 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:30:17,196 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-19 02:30:17,197 [INFO] - Epoch: 42/130
2023-09-19 02:32:44,501 [INFO] - Training epoch stats:     Loss: 3.1112 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:33:11,030 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-19 02:33:11,031 [INFO] - Epoch: 43/130
2023-09-19 02:35:09,928 [INFO] - Training epoch stats:     Loss: 3.0952 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:35:45,795 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-19 02:35:45,796 [INFO] - Epoch: 44/130
2023-09-19 02:37:42,931 [INFO] - Training epoch stats:     Loss: 3.1045 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:38:22,239 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-19 02:38:22,240 [INFO] - Epoch: 45/130
2023-09-19 02:40:23,637 [INFO] - Training epoch stats:     Loss: 3.0945 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:40:38,353 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-19 02:40:38,354 [INFO] - Epoch: 46/130
2023-09-19 02:42:37,650 [INFO] - Training epoch stats:     Loss: 3.0967 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:43:23,300 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-19 02:43:23,300 [INFO] - Epoch: 47/130
2023-09-19 02:45:27,825 [INFO] - Training epoch stats:     Loss: 3.0851 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:46:22,310 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-19 02:46:22,311 [INFO] - Epoch: 48/130
2023-09-19 02:48:54,358 [INFO] - Training epoch stats:     Loss: 3.0950 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:49:41,280 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-19 02:49:41,280 [INFO] - Epoch: 49/130
2023-09-19 02:51:39,740 [INFO] - Training epoch stats:     Loss: 3.0740 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 02:52:11,746 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-19 02:52:11,746 [INFO] - Epoch: 50/130
2023-09-19 02:54:10,390 [INFO] - Training epoch stats:     Loss: 3.0780 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:00:16,929 [INFO] - Validation epoch stats:   Loss: 3.0933 - Binary-Cell-Dice: 0.7657 - Binary-Cell-Jacard: 0.6786 - bPQ-Score: 0.5869 - mPQ-Score: 0.4399 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:00:16,937 [INFO] - New best model - save checkpoint
2023-09-19 03:01:49,111 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-19 03:01:49,112 [INFO] - Epoch: 51/130
2023-09-19 03:04:28,389 [INFO] - Training epoch stats:     Loss: 3.0994 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:05:23,857 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-19 03:05:23,857 [INFO] - Epoch: 52/130
2023-09-19 03:07:24,873 [INFO] - Training epoch stats:     Loss: 3.0940 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:07:50,836 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-19 03:07:50,837 [INFO] - Epoch: 53/130
2023-09-19 03:09:48,948 [INFO] - Training epoch stats:     Loss: 3.0891 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:10:57,527 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-19 03:10:57,527 [INFO] - Epoch: 54/130
2023-09-19 03:12:57,178 [INFO] - Training epoch stats:     Loss: 3.0795 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:13:13,717 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-19 03:13:13,718 [INFO] - Epoch: 55/130
2023-09-19 03:15:12,323 [INFO] - Training epoch stats:     Loss: 3.0873 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:15:52,055 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-19 03:15:52,055 [INFO] - Epoch: 56/130
2023-09-19 03:17:54,418 [INFO] - Training epoch stats:     Loss: 3.0825 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:18:43,421 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-19 03:18:43,422 [INFO] - Epoch: 57/130
2023-09-19 03:20:47,996 [INFO] - Training epoch stats:     Loss: 3.0753 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:21:04,737 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-19 03:21:04,738 [INFO] - Epoch: 58/130
2023-09-19 03:23:04,596 [INFO] - Training epoch stats:     Loss: 3.0873 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:24:09,055 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-19 03:24:09,055 [INFO] - Epoch: 59/130
2023-09-19 03:26:09,441 [INFO] - Training epoch stats:     Loss: 3.0747 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:26:35,852 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-19 03:26:35,853 [INFO] - Epoch: 60/130
2023-09-19 03:28:36,177 [INFO] - Training epoch stats:     Loss: 3.0780 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:35:57,378 [INFO] - Validation epoch stats:   Loss: 3.0844 - Binary-Cell-Dice: 0.7671 - Binary-Cell-Jacard: 0.6819 - bPQ-Score: 0.5942 - mPQ-Score: 0.4480 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:35:57,382 [INFO] - New best model - save checkpoint
2023-09-19 03:36:52,518 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-19 03:36:52,518 [INFO] - Epoch: 61/130
2023-09-19 03:39:23,656 [INFO] - Training epoch stats:     Loss: 3.0689 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:39:51,798 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-19 03:39:51,798 [INFO] - Epoch: 62/130
2023-09-19 03:41:49,936 [INFO] - Training epoch stats:     Loss: 3.0751 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:42:20,797 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-19 03:42:20,797 [INFO] - Epoch: 63/130
2023-09-19 03:44:18,830 [INFO] - Training epoch stats:     Loss: 3.0724 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:44:43,925 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-19 03:44:43,926 [INFO] - Epoch: 64/130
2023-09-19 03:46:40,794 [INFO] - Training epoch stats:     Loss: 3.0703 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:47:12,534 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-19 03:47:12,534 [INFO] - Epoch: 65/130
2023-09-19 03:49:10,287 [INFO] - Training epoch stats:     Loss: 3.0668 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:49:57,075 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-19 03:49:57,075 [INFO] - Epoch: 66/130
2023-09-19 03:51:55,843 [INFO] - Training epoch stats:     Loss: 3.0805 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:52:15,180 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-19 03:52:15,181 [INFO] - Epoch: 67/130
2023-09-19 03:54:15,410 [INFO] - Training epoch stats:     Loss: 3.0610 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:54:46,996 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-19 03:54:46,997 [INFO] - Epoch: 68/130
2023-09-19 03:56:48,659 [INFO] - Training epoch stats:     Loss: 3.0593 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:57:03,036 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-19 03:57:03,037 [INFO] - Epoch: 69/130
2023-09-19 03:59:02,797 [INFO] - Training epoch stats:     Loss: 3.0843 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 03:59:40,440 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-19 03:59:40,440 [INFO] - Epoch: 70/130
2023-09-19 04:02:12,375 [INFO] - Training epoch stats:     Loss: 3.0645 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:09:41,112 [INFO] - Validation epoch stats:   Loss: 3.0862 - Binary-Cell-Dice: 0.7739 - Binary-Cell-Jacard: 0.6902 - bPQ-Score: 0.6012 - mPQ-Score: 0.4585 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:09:41,122 [INFO] - New best model - save checkpoint
2023-09-19 04:10:47,148 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-19 04:10:47,177 [INFO] - Epoch: 71/130
2023-09-19 04:12:47,173 [INFO] - Training epoch stats:     Loss: 3.0519 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:13:48,970 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-19 04:13:48,974 [INFO] - Epoch: 72/130
2023-09-19 04:15:50,030 [INFO] - Training epoch stats:     Loss: 3.0687 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:16:18,180 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-19 04:16:18,181 [INFO] - Epoch: 73/130
2023-09-19 04:18:16,279 [INFO] - Training epoch stats:     Loss: 3.0606 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:19:30,683 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-19 04:19:30,684 [INFO] - Epoch: 74/130
2023-09-19 04:21:32,114 [INFO] - Training epoch stats:     Loss: 3.0474 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:22:37,108 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-19 04:22:37,108 [INFO] - Epoch: 75/130
2023-09-19 04:24:38,764 [INFO] - Training epoch stats:     Loss: 3.0561 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:25:05,135 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-19 04:25:05,135 [INFO] - Epoch: 76/130
2023-09-19 04:27:02,883 [INFO] - Training epoch stats:     Loss: 3.0585 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:27:37,696 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-19 04:27:37,697 [INFO] - Epoch: 77/130
2023-09-19 04:29:36,306 [INFO] - Training epoch stats:     Loss: 3.0614 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:29:54,578 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-19 04:29:54,578 [INFO] - Epoch: 78/130
2023-09-19 04:31:53,697 [INFO] - Training epoch stats:     Loss: 3.0482 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:32:09,349 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-19 04:32:09,349 [INFO] - Epoch: 79/130
2023-09-19 04:34:08,238 [INFO] - Training epoch stats:     Loss: 3.0445 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:34:36,519 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-19 04:34:36,520 [INFO] - Epoch: 80/130
2023-09-19 04:36:38,904 [INFO] - Training epoch stats:     Loss: 3.0552 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:44:03,481 [INFO] - Validation epoch stats:   Loss: 3.0868 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6911 - bPQ-Score: 0.6013 - mPQ-Score: 0.4573 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:44:03,490 [INFO] - New best model - save checkpoint
2023-09-19 04:45:09,834 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-19 04:45:09,834 [INFO] - Epoch: 81/130
2023-09-19 04:47:10,378 [INFO] - Training epoch stats:     Loss: 3.0535 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:47:41,623 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-19 04:47:41,623 [INFO] - Epoch: 82/130
2023-09-19 04:49:43,509 [INFO] - Training epoch stats:     Loss: 3.0510 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:50:17,713 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-19 04:50:17,713 [INFO] - Epoch: 83/130
2023-09-19 04:52:19,543 [INFO] - Training epoch stats:     Loss: 3.0590 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:52:38,060 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-19 04:52:38,061 [INFO] - Epoch: 84/130
2023-09-19 04:54:36,348 [INFO] - Training epoch stats:     Loss: 3.0472 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:55:11,086 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-19 04:55:11,087 [INFO] - Epoch: 85/130
2023-09-19 04:57:09,388 [INFO] - Training epoch stats:     Loss: 3.0540 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:57:25,288 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-19 04:57:25,289 [INFO] - Epoch: 86/130
2023-09-19 04:59:22,570 [INFO] - Training epoch stats:     Loss: 3.0547 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 04:59:53,462 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-19 04:59:53,463 [INFO] - Epoch: 87/130
2023-09-19 05:01:52,730 [INFO] - Training epoch stats:     Loss: 3.0392 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:02:09,364 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-19 05:02:09,365 [INFO] - Epoch: 88/130
2023-09-19 05:04:09,435 [INFO] - Training epoch stats:     Loss: 3.0644 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:04:40,565 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:04:40,566 [INFO] - Epoch: 89/130
2023-09-19 05:06:41,510 [INFO] - Training epoch stats:     Loss: 3.0489 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:07:13,869 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:07:13,870 [INFO] - Epoch: 90/130
2023-09-19 05:09:15,644 [INFO] - Training epoch stats:     Loss: 3.0629 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:17:48,591 [INFO] - Validation epoch stats:   Loss: 3.0843 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6906 - bPQ-Score: 0.6012 - mPQ-Score: 0.4581 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:18:04,113 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:18:04,114 [INFO] - Epoch: 91/130
2023-09-19 05:20:01,889 [INFO] - Training epoch stats:     Loss: 3.0536 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:20:18,977 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:20:18,978 [INFO] - Epoch: 92/130
2023-09-19 05:22:17,213 [INFO] - Training epoch stats:     Loss: 3.0495 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:22:33,353 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:22:33,354 [INFO] - Epoch: 93/130
2023-09-19 05:24:31,441 [INFO] - Training epoch stats:     Loss: 3.0386 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:25:14,022 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-19 05:25:14,023 [INFO] - Epoch: 94/130
2023-09-19 05:27:12,210 [INFO] - Training epoch stats:     Loss: 3.0542 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:28:14,365 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-19 05:28:14,365 [INFO] - Epoch: 95/130
2023-09-19 05:30:13,734 [INFO] - Training epoch stats:     Loss: 3.0493 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:31:22,165 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:31:22,165 [INFO] - Epoch: 96/130
2023-09-19 05:33:21,424 [INFO] - Training epoch stats:     Loss: 3.0458 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:34:20,112 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:34:20,113 [INFO] - Epoch: 97/130
2023-09-19 05:36:22,532 [INFO] - Training epoch stats:     Loss: 3.0663 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:36:50,250 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:36:50,251 [INFO] - Epoch: 98/130
2023-09-19 05:38:46,940 [INFO] - Training epoch stats:     Loss: 3.0469 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:39:50,138 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:39:50,139 [INFO] - Epoch: 99/130
2023-09-19 05:41:51,907 [INFO] - Training epoch stats:     Loss: 3.0499 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:42:07,566 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:42:07,566 [INFO] - Epoch: 100/130
2023-09-19 05:44:05,468 [INFO] - Training epoch stats:     Loss: 3.0659 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:52:13,135 [INFO] - Validation epoch stats:   Loss: 3.0796 - Binary-Cell-Dice: 0.7755 - Binary-Cell-Jacard: 0.6921 - bPQ-Score: 0.6028 - mPQ-Score: 0.4586 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:52:13,144 [INFO] - New best model - save checkpoint
2023-09-19 05:52:43,703 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:52:43,703 [INFO] - Epoch: 101/130
2023-09-19 05:54:42,629 [INFO] - Training epoch stats:     Loss: 3.0412 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:55:16,020 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:55:16,021 [INFO] - Epoch: 102/130
2023-09-19 05:57:17,498 [INFO] - Training epoch stats:     Loss: 3.0625 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 05:57:33,529 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 05:57:33,529 [INFO] - Epoch: 103/130
2023-09-19 05:59:31,769 [INFO] - Training epoch stats:     Loss: 3.0555 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:00:03,299 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-19 06:00:03,300 [INFO] - Epoch: 104/130
2023-09-19 06:02:05,916 [INFO] - Training epoch stats:     Loss: 3.0516 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:02:34,176 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-19 06:02:34,177 [INFO] - Epoch: 105/130
2023-09-19 06:04:35,497 [INFO] - Training epoch stats:     Loss: 3.0480 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:05:04,530 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:05:04,530 [INFO] - Epoch: 106/130
2023-09-19 06:07:07,150 [INFO] - Training epoch stats:     Loss: 3.0616 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:07:22,364 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:07:22,365 [INFO] - Epoch: 107/130
2023-09-19 06:09:21,550 [INFO] - Training epoch stats:     Loss: 3.0510 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:09:36,839 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:09:36,839 [INFO] - Epoch: 108/130
2023-09-19 06:11:35,024 [INFO] - Training epoch stats:     Loss: 3.0472 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:12:06,430 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:12:06,430 [INFO] - Epoch: 109/130
2023-09-19 06:14:04,663 [INFO] - Training epoch stats:     Loss: 3.0521 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:14:39,789 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:14:39,790 [INFO] - Epoch: 110/130
2023-09-19 06:17:05,293 [INFO] - Training epoch stats:     Loss: 3.0553 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:25:51,846 [INFO] - Validation epoch stats:   Loss: 3.0841 - Binary-Cell-Dice: 0.7732 - Binary-Cell-Jacard: 0.6896 - bPQ-Score: 0.6004 - mPQ-Score: 0.4567 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:26:28,280 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:26:28,281 [INFO] - Epoch: 111/130
2023-09-19 06:28:30,045 [INFO] - Training epoch stats:     Loss: 3.0609 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:29:01,141 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:29:01,141 [INFO] - Epoch: 112/130
2023-09-19 06:31:03,275 [INFO] - Training epoch stats:     Loss: 3.0485 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:31:34,491 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:31:34,492 [INFO] - Epoch: 113/130
2023-09-19 06:33:36,072 [INFO] - Training epoch stats:     Loss: 3.0536 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:34:08,688 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:34:08,689 [INFO] - Epoch: 114/130
2023-09-19 06:36:10,573 [INFO] - Training epoch stats:     Loss: 3.0341 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:36:25,617 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:36:25,618 [INFO] - Epoch: 115/130
2023-09-19 06:38:24,873 [INFO] - Training epoch stats:     Loss: 3.0573 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:38:59,461 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:38:59,461 [INFO] - Epoch: 116/130
2023-09-19 06:41:02,598 [INFO] - Training epoch stats:     Loss: 3.0388 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:41:17,188 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:41:17,188 [INFO] - Epoch: 117/130
2023-09-19 06:43:16,687 [INFO] - Training epoch stats:     Loss: 3.0419 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:43:45,898 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:43:45,899 [INFO] - Epoch: 118/130
2023-09-19 06:47:04,696 [INFO] - Training epoch stats:     Loss: 3.0339 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:47:38,644 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:47:38,644 [INFO] - Epoch: 119/130
2023-09-19 06:50:06,825 [INFO] - Training epoch stats:     Loss: 3.0461 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 06:51:13,750 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 06:51:13,751 [INFO] - Epoch: 120/130
2023-09-19 06:53:13,051 [INFO] - Training epoch stats:     Loss: 3.0441 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:02:22,610 [INFO] - Validation epoch stats:   Loss: 3.0853 - Binary-Cell-Dice: 0.7744 - Binary-Cell-Jacard: 0.6911 - bPQ-Score: 0.6018 - mPQ-Score: 0.4588 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:02:55,587 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 07:02:55,587 [INFO] - Epoch: 121/130
2023-09-19 07:04:56,114 [INFO] - Training epoch stats:     Loss: 3.0467 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:05:15,914 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 07:05:15,915 [INFO] - Epoch: 122/130
2023-09-19 07:07:14,644 [INFO] - Training epoch stats:     Loss: 3.0436 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:07:46,938 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 07:07:46,939 [INFO] - Epoch: 123/130
2023-09-19 07:09:48,339 [INFO] - Training epoch stats:     Loss: 3.0482 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:10:14,945 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 07:10:14,946 [INFO] - Epoch: 124/130
2023-09-19 07:12:16,643 [INFO] - Training epoch stats:     Loss: 3.0564 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:12:50,669 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-19 07:12:50,670 [INFO] - Epoch: 125/130
2023-09-19 07:14:52,703 [INFO] - Training epoch stats:     Loss: 3.0449 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:15:10,335 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-19 07:15:10,335 [INFO] - Epoch: 126/130
2023-09-19 07:17:08,939 [INFO] - Training epoch stats:     Loss: 3.0476 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:18:09,907 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-19 07:18:09,908 [INFO] - Epoch: 127/130
2023-09-19 07:20:46,048 [INFO] - Training epoch stats:     Loss: 3.0339 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:21:56,567 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-19 07:21:56,568 [INFO] - Epoch: 128/130
2023-09-19 07:23:55,503 [INFO] - Training epoch stats:     Loss: 3.0566 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:24:38,414 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-19 07:24:38,414 [INFO] - Epoch: 129/130
2023-09-19 07:26:35,466 [INFO] - Training epoch stats:     Loss: 3.0571 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:27:28,949 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-19 07:27:28,949 [INFO] - Epoch: 130/130
2023-09-19 07:29:25,967 [INFO] - Training epoch stats:     Loss: 3.0484 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:37:03,055 [INFO] - Validation epoch stats:   Loss: 3.0809 - Binary-Cell-Dice: 0.7744 - Binary-Cell-Jacard: 0.6913 - bPQ-Score: 0.6024 - mPQ-Score: 0.4588 - Tissue-MC-Acc.: 0.0000
2023-09-19 07:37:20,693 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-19 07:37:20,696 [INFO] -
