2023-09-18 06:55:30,150 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-18 06:55:30,241 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f54c5fae6a0>]
2023-09-18 06:55:30,241 [INFO] - Using GPU: cuda:0
2023-09-18 06:55:30,241 [INFO] - Using device: cuda:0
2023-09-18 06:55:30,242 [INFO] - Loss functions:
2023-09-18 06:55:30,242 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON><PERSON>(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-18 06:55:42,372 [INFO] - Loaded CellViT-SAM model with backbone: SAM-H
2023-09-18 06:55:42,377 [INFO] -
Model: CellViTSAMStarDist(
  (encoder): ViTCellViTDeit(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 1280, kernel_size=(16, 16), stride=(16, 16))
    )
    (blocks): ModuleList(
      (0-31): 32 x Block(
        (norm1): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=1280, out_features=3840, bias=True)
          (proj): Linear(in_features=1280, out_features=1280, bias=True)
        )
        (norm2): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=1280, out_features=5120, bias=True)
          (lin2): Linear(in_features=5120, out_features=1280, bias=True)
          (act): GELU(approximate='none')
        )
      )
    )
    (neck): Sequential(
      (0): Conv2d(1280, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (1): LayerNorm2d()
      (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
      (3): LayerNorm2d()
    )
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (stardist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (dist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
)
2023-09-18 06:55:44,514 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViTSAMStarDist                            [1, 6, 256, 256]          --
├─ViTCellViTDeit: 1-1                         [1, 256]                  5,242,880
│    └─PatchEmbed: 2-1                        [1, 16, 16, 1280]         --
│    │    └─Conv2d: 3-1                       [1, 1280, 16, 16]         (984,320)
│    └─ModuleList: 2-2                        --                        --
│    │    └─Block: 3-2                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-3                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-4                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-5                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-6                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-7                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-8                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-9                        [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-10                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-11                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-12                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-13                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-14                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-15                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-16                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-17                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-18                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-19                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-20                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-21                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-22                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-23                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-24                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-25                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-26                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-27                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-28                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-29                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-30                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-31                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-32                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-33                       [1, 16, 16, 1280]         (19,697,760)
│    └─Sequential: 2-3                        [1, 256, 16, 16]          --
│    │    └─Conv2d: 3-34                      [1, 256, 16, 16]          (327,680)
│    │    └─LayerNorm2d: 3-35                 [1, 256, 16, 16]          (512)
│    │    └─Conv2d: 3-36                      [1, 256, 16, 16]          (589,824)
│    │    └─LayerNorm2d: 3-37                 [1, 256, 16, 16]          (512)
├─Linear: 1-2                                 [1, 19]                   4,883
├─Sequential: 1-11                            --                        (recursive)
│    └─ConvTranspose2d: 2-4                   [1, 512, 32, 32]          2,621,952
├─Sequential: 1-4                             [1, 512, 32, 32]          --
│    └─Deconv2DBlock: 2-5                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-38                  [1, 512, 32, 32]          4,982,784
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-6                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-39                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-40                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-41                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-42             [1, 256, 64, 64]          524,544
├─Sequential: 1-6                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-7                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-43                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-8                     [1, 256, 64, 64]          --
│    │    └─Sequential: 3-44                  [1, 256, 64, 64]          1,115,136
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-9                        [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-45                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-46                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-47             [1, 128, 128, 128]        131,200
├─Sequential: 1-8                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-10                    [1, 512, 32, 32]          --
│    │    └─Sequential: 3-48                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-11                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-49                  [1, 256, 64, 64]          1,115,136
│    └─Deconv2DBlock: 2-12                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-50                  [1, 128, 128, 128]        279,040
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-13                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-51                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-52                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-53             [1, 64, 256, 256]         32,832
├─Sequential: 1-10                            [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-14                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-54                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-15                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-55                  [1, 64, 256, 256]         18,624
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-16                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-56                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-57                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-58                      [1, 32, 256, 256]         2,080
├─Sequential: 1-20                            --                        (recursive)
│    └─ConvTranspose2d: 2-17                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-13                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-18                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-59                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-19                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-60                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-61                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-62                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-63             [1, 256, 64, 64]          524,544
├─Sequential: 1-15                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-64                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-21                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-65                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-22                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-66                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-67                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-68             [1, 128, 128, 128]        131,200
├─Sequential: 1-17                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-69                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-24                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-70                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-71                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-26                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-72                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-73                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-74             [1, 64, 256, 256]         32,832
├─Sequential: 1-19                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-27                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-75                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-28                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-76                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-29                       [1, 1, 256, 256]          --
│    │    └─Conv2DBlock: 3-77                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-78                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-79                      [1, 1, 256, 256]          65
├─Sequential: 1-29                            --                        (recursive)
│    └─ConvTranspose2d: 2-30                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-22                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-31                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-80                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-32                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-81                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-82                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-83                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-84             [1, 256, 64, 64]          524,544
├─Sequential: 1-24                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-85                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-34                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-86                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-35                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-87                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-88                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-89             [1, 128, 128, 128]        131,200
├─Sequential: 1-26                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-90                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-37                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-91                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-92                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-39                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-93                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-94                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-95             [1, 64, 256, 256]         32,832
├─Sequential: 1-28                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-40                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-96                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-41                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-97                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-42                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-98                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-99                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-100                     [1, 6, 256, 256]          390
===============================================================================================
Total params: 699,743,034
Trainable params: 62,716,986
Non-trainable params: 637,026,048
Total mult-adds (G): 214.33
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 3239.58
Params size (MB): 2777.19
Estimated Total Size (MB): 6017.55
===============================================================================================
2023-09-18 06:55:45,748 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-18 06:55:45,749 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-18 06:55:45,749 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-18 06:55:47,272 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-18 06:55:47,275 [INFO] - Unique-Weights: tensor([0.5339, 0.5660, 0.5673, 0.6560, 0.6651, 0.6822, 0.6835, 0.6972, 0.6984,
        0.7156, 0.7722, 0.7871, 0.8043, 0.8055, 0.8134, 0.8146, 0.8318, 0.8467,
        0.9033, 0.9205, 0.9217, 0.9367, 0.9604, 0.9629, 0.9664, 0.9682, 0.9853,
        0.9925, 0.9938, 0.9985, 1.0003, 1.0174, 1.0316, 1.0352, 1.0529, 1.0637,
        1.0650, 1.0691, 1.0700, 1.0703, 1.0825, 1.0869, 1.0875, 1.0924, 1.0930,
        1.0954, 1.1024, 1.1024, 1.1073, 1.1087, 1.1100, 1.1147, 1.1159, 1.1165,
        1.1178, 1.1181, 1.1190, 1.1196, 1.1202, 1.1245, 1.1251, 1.1259, 1.1263,
        1.1275, 1.1336, 1.1348, 1.1421, 1.1480, 1.1499, 1.1502, 1.1514, 1.1537,
        1.1580, 1.1592, 1.1669, 1.1799, 1.1812, 1.1848, 1.1911, 1.1923, 1.1987,
        1.2012, 1.2046, 1.2065, 1.2089, 1.2133, 1.2144, 1.2174, 1.2186, 1.2186,
        1.2198, 1.2235, 1.2352, 1.2358, 1.2364, 1.2370, 1.2401, 1.2407, 1.2413,
        1.2419, 1.2425, 1.2425, 1.2437, 1.2450, 1.2519, 1.2583, 1.2598, 1.2642,
        1.2661, 1.2664, 1.2676, 1.2685, 1.2740, 1.2742, 1.2746, 1.2754, 1.2759,
        1.2771, 1.2831, 1.2997, 1.3010, 1.3044, 1.3073, 1.3075, 1.3085, 1.3251,
        1.3295, 1.3306, 1.3337, 1.3407, 1.3419, 1.3482, 1.3542, 1.3560, 1.3563,
        1.3567, 1.3585, 1.3640, 1.3641, 1.3670, 1.3681, 1.3847, 1.3854, 1.3902,
        1.3908, 1.3921, 1.3933, 1.4018, 1.4159, 1.4194, 1.4237, 1.4310, 1.4480,
        1.4527, 1.4556, 1.4569, 1.4581, 1.4631, 1.4747, 1.4753, 1.4808, 1.4820,
        1.5046, 1.5050, 1.5059, 1.5137, 1.5180, 1.5292, 1.5392, 1.5578, 1.5793,
        1.5805, 1.5805, 1.6023, 1.6044, 1.6052, 1.6126, 1.6212, 1.7288, 1.7301,
        1.7540])
2023-09-18 06:55:47,276 [INFO] - Instantiate Trainer
2023-09-18 06:55:47,277 [INFO] - Calling Trainer Fit
2023-09-18 06:55:47,277 [INFO] - Starting training, total number of epochs: 130
2023-09-18 06:55:47,277 [INFO] - Epoch: 1/130
2023-09-18 06:57:13,833 [INFO] - Training epoch stats:     Loss: 4.4280 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0327
2023-09-18 06:58:22,477 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-18 06:58:22,551 [INFO] - Epoch: 2/130
2023-09-18 06:59:56,277 [INFO] - Training epoch stats:     Loss: 3.6827 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0294
2023-09-18 07:01:04,237 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-18 07:01:04,238 [INFO] - Epoch: 3/130
2023-09-18 07:02:09,114 [INFO] - Training epoch stats:     Loss: 3.4911 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0272
2023-09-18 07:04:16,069 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-18 07:04:16,073 [INFO] - Epoch: 4/130
2023-09-18 07:05:22,535 [INFO] - Training epoch stats:     Loss: 3.4348 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0272
2023-09-18 07:07:14,409 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-18 07:07:14,413 [INFO] - Epoch: 5/130
2023-09-18 07:08:23,165 [INFO] - Training epoch stats:     Loss: 3.3936 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0283
2023-09-18 07:10:48,167 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-18 07:10:48,173 [INFO] - Epoch: 6/130
2023-09-18 07:11:57,414 [INFO] - Training epoch stats:     Loss: 3.3846 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0268
2023-09-18 07:13:26,468 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-18 07:13:26,468 [INFO] - Epoch: 7/130
2023-09-18 07:14:33,385 [INFO] - Training epoch stats:     Loss: 3.3753 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 07:16:23,990 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-18 07:16:23,997 [INFO] - Epoch: 8/130
2023-09-18 07:17:31,350 [INFO] - Training epoch stats:     Loss: 3.3365 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0265
2023-09-18 07:18:24,431 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-18 07:18:24,431 [INFO] - Epoch: 9/130
2023-09-18 07:19:30,477 [INFO] - Training epoch stats:     Loss: 3.3284 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0283
2023-09-18 07:20:35,847 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-18 07:20:35,847 [INFO] - Epoch: 10/130
2023-09-18 07:21:42,336 [INFO] - Training epoch stats:     Loss: 3.3201 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 07:26:39,878 [INFO] - Validation epoch stats:   Loss: 3.2424 - Binary-Cell-Dice: 0.6717 - Binary-Cell-Jacard: 0.5486 - bPQ-Score: 0.3885 - mPQ-Score: 0.2715 - Tissue-MC-Acc.: 0.0250
2023-09-18 07:26:39,880 [INFO] - New best model - save checkpoint
2023-09-18 07:29:40,697 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-18 07:29:40,702 [INFO] - Epoch: 11/130
2023-09-18 07:30:48,083 [INFO] - Training epoch stats:     Loss: 3.3037 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0253
2023-09-18 07:33:04,697 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-18 07:33:04,745 [INFO] - Epoch: 12/130
2023-09-18 07:34:19,431 [INFO] - Training epoch stats:     Loss: 3.2805 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-18 07:35:48,410 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-18 07:35:48,411 [INFO] - Epoch: 13/130
2023-09-18 07:36:52,159 [INFO] - Training epoch stats:     Loss: 3.2734 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0265
2023-09-18 07:39:03,442 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-18 07:39:03,448 [INFO] - Epoch: 14/130
2023-09-18 07:40:12,798 [INFO] - Training epoch stats:     Loss: 3.2478 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0253
2023-09-18 07:42:40,865 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-18 07:42:40,870 [INFO] - Epoch: 15/130
2023-09-18 07:43:48,490 [INFO] - Training epoch stats:     Loss: 3.2649 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0268
2023-09-18 07:45:22,071 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-18 07:45:22,072 [INFO] - Epoch: 16/130
2023-09-18 07:46:25,844 [INFO] - Training epoch stats:     Loss: 3.2744 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0283
2023-09-18 07:48:14,800 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-18 07:48:14,806 [INFO] - Epoch: 17/130
2023-09-18 07:49:20,985 [INFO] - Training epoch stats:     Loss: 3.2476 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-18 07:52:03,762 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-18 07:52:03,766 [INFO] - Epoch: 18/130
2023-09-18 07:53:08,586 [INFO] - Training epoch stats:     Loss: 3.2348 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0268
2023-09-18 07:53:59,300 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-18 07:53:59,300 [INFO] - Epoch: 19/130
2023-09-18 07:55:02,649 [INFO] - Training epoch stats:     Loss: 3.2388 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0279
2023-09-18 07:55:40,076 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-18 07:55:40,077 [INFO] - Epoch: 20/130
2023-09-18 07:56:43,362 [INFO] - Training epoch stats:     Loss: 3.2343 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-18 08:01:45,977 [INFO] - Validation epoch stats:   Loss: 3.1705 - Binary-Cell-Dice: 0.7437 - Binary-Cell-Jacard: 0.6469 - bPQ-Score: 0.5344 - mPQ-Score: 0.3832 - Tissue-MC-Acc.: 0.0250
2023-09-18 08:01:45,980 [INFO] - New best model - save checkpoint
2023-09-18 08:03:56,492 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-18 08:03:56,564 [INFO] - Epoch: 21/130
2023-09-18 08:06:16,335 [INFO] - Training epoch stats:     Loss: 3.2217 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0261
2023-09-18 08:08:35,505 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-18 08:08:35,577 [INFO] - Epoch: 22/130
2023-09-18 08:09:57,542 [INFO] - Training epoch stats:     Loss: 3.2203 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0342
2023-09-18 08:12:45,747 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-18 08:12:45,751 [INFO] - Epoch: 23/130
2023-09-18 08:13:50,860 [INFO] - Training epoch stats:     Loss: 3.2139 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 08:15:10,738 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-18 08:15:10,739 [INFO] - Epoch: 24/130
2023-09-18 08:16:16,806 [INFO] - Training epoch stats:     Loss: 3.2298 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0191
2023-09-18 08:18:37,985 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-18 08:18:38,058 [INFO] - Epoch: 25/130
2023-09-18 08:20:12,640 [INFO] - Training epoch stats:     Loss: 3.2087 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0279
2023-09-18 08:22:05,080 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-18 08:22:05,118 [INFO] - Epoch: 26/130
2023-09-18 08:23:54,735 [INFO] - Training epoch stats:     Loss: 3.3695 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0220
2023-09-18 08:28:54,387 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-18 08:28:54,483 [INFO] - Epoch: 27/130
2023-09-18 08:31:31,805 [INFO] - Training epoch stats:     Loss: 3.2836 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-18 08:36:13,088 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-18 08:36:13,142 [INFO] - Epoch: 28/130
2023-09-18 08:38:28,182 [INFO] - Training epoch stats:     Loss: 3.2421 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0180
2023-09-18 08:43:33,817 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-18 08:43:33,821 [INFO] - Epoch: 29/130
2023-09-18 08:45:17,591 [INFO] - Training epoch stats:     Loss: 3.1993 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 08:48:53,112 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-18 08:48:53,180 [INFO] - Epoch: 30/130
2023-09-18 08:51:43,546 [INFO] - Training epoch stats:     Loss: 3.2105 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0169
2023-09-18 08:57:35,267 [INFO] - Validation epoch stats:   Loss: 3.1785 - Binary-Cell-Dice: 0.7517 - Binary-Cell-Jacard: 0.6536 - bPQ-Score: 0.5407 - mPQ-Score: 0.3789 - Tissue-MC-Acc.: 0.0222
2023-09-18 08:57:35,270 [INFO] - New best model - save checkpoint
2023-09-18 09:05:35,383 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-18 09:05:35,388 [INFO] - Epoch: 31/130
2023-09-18 09:07:15,738 [INFO] - Training epoch stats:     Loss: 3.1772 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0187
2023-09-18 09:11:43,926 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-18 09:11:43,927 [INFO] - Epoch: 32/130
2023-09-18 09:13:22,972 [INFO] - Training epoch stats:     Loss: 3.1425 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 09:21:48,217 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-18 09:21:48,231 [INFO] - Epoch: 33/130
2023-09-18 09:23:28,638 [INFO] - Training epoch stats:     Loss: 3.1306 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0169
2023-09-18 09:31:10,518 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-18 09:31:10,526 [INFO] - Epoch: 34/130
2023-09-18 09:32:55,394 [INFO] - Training epoch stats:     Loss: 3.1403 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0217
2023-09-18 09:39:58,229 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-18 09:39:58,235 [INFO] - Epoch: 35/130
2023-09-18 09:41:39,752 [INFO] - Training epoch stats:     Loss: 3.1230 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0176
2023-09-18 09:48:43,038 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-18 09:48:43,054 [INFO] - Epoch: 36/130
2023-09-18 09:50:27,398 [INFO] - Training epoch stats:     Loss: 3.1099 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0162
2023-09-18 09:56:43,285 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-18 09:56:43,286 [INFO] - Epoch: 37/130
2023-09-18 09:58:22,524 [INFO] - Training epoch stats:     Loss: 3.1008 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0191
2023-09-18 10:05:07,526 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-18 10:05:07,527 [INFO] - Epoch: 38/130
2023-09-18 10:06:48,222 [INFO] - Training epoch stats:     Loss: 3.0694 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 10:12:24,277 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-18 10:12:24,281 [INFO] - Epoch: 39/130
2023-09-18 10:14:04,892 [INFO] - Training epoch stats:     Loss: 3.0819 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0132
2023-09-18 10:17:58,209 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-18 10:17:58,294 [INFO] - Epoch: 40/130
2023-09-18 10:21:00,855 [INFO] - Training epoch stats:     Loss: 3.0711 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 10:25:48,996 [INFO] - Validation epoch stats:   Loss: 3.0943 - Binary-Cell-Dice: 0.7617 - Binary-Cell-Jacard: 0.6722 - bPQ-Score: 0.5685 - mPQ-Score: 0.4277 - Tissue-MC-Acc.: 0.0159
2023-09-18 10:25:49,045 [INFO] - New best model - save checkpoint
2023-09-18 10:36:31,927 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-18 10:36:32,011 [INFO] - Epoch: 41/130
2023-09-18 10:39:08,324 [INFO] - Training epoch stats:     Loss: 3.0615 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0169
2023-09-18 10:45:49,887 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-18 10:45:49,949 [INFO] - Epoch: 42/130
2023-09-18 10:47:50,562 [INFO] - Training epoch stats:     Loss: 3.0439 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0180
2023-09-18 10:55:47,666 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-18 10:55:47,971 [INFO] - Epoch: 43/130
2023-09-18 10:57:30,776 [INFO] - Training epoch stats:     Loss: 3.0388 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0176
2023-09-18 11:01:23,847 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-18 11:01:23,853 [INFO] - Epoch: 44/130
2023-09-18 11:03:05,691 [INFO] - Training epoch stats:     Loss: 3.0305 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0180
2023-09-18 11:09:48,785 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-18 11:09:48,792 [INFO] - Epoch: 45/130
2023-09-18 11:11:34,167 [INFO] - Training epoch stats:     Loss: 3.0215 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0162
2023-09-18 11:17:50,375 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-18 11:17:50,381 [INFO] - Epoch: 46/130
2023-09-18 11:19:31,876 [INFO] - Training epoch stats:     Loss: 3.0177 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0176
2023-09-18 11:25:08,304 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-18 11:25:08,387 [INFO] - Epoch: 47/130
2023-09-18 11:28:11,407 [INFO] - Training epoch stats:     Loss: 3.0081 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0191
2023-09-18 11:31:41,749 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-18 11:31:41,843 [INFO] - Epoch: 48/130
2023-09-18 11:33:21,235 [INFO] - Training epoch stats:     Loss: 2.9991 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0184
2023-09-18 11:37:45,676 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-18 11:37:45,678 [INFO] - Epoch: 49/130
2023-09-18 11:39:26,586 [INFO] - Training epoch stats:     Loss: 2.9964 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0209
2023-09-18 11:45:43,859 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-18 11:45:43,977 [INFO] - Epoch: 50/130
2023-09-18 11:49:15,071 [INFO] - Training epoch stats:     Loss: 2.9753 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 11:53:33,855 [INFO] - Validation epoch stats:   Loss: 3.0660 - Binary-Cell-Dice: 0.7609 - Binary-Cell-Jacard: 0.6709 - bPQ-Score: 0.5681 - mPQ-Score: 0.4344 - Tissue-MC-Acc.: 0.0163
2023-09-18 12:06:26,500 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-18 12:06:26,789 [INFO] - Epoch: 51/130
2023-09-18 12:09:00,922 [INFO] - Training epoch stats:     Loss: 2.9840 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 12:15:19,179 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-18 12:15:19,181 [INFO] - Epoch: 52/130
2023-09-18 12:17:00,237 [INFO] - Training epoch stats:     Loss: 2.9928 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0213
2023-09-18 12:23:03,296 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-18 12:23:03,296 [INFO] - Epoch: 53/130
2023-09-18 12:24:48,482 [INFO] - Training epoch stats:     Loss: 2.9692 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0213
2023-09-18 12:31:02,636 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-18 12:31:02,638 [INFO] - Epoch: 54/130
2023-09-18 12:32:42,911 [INFO] - Training epoch stats:     Loss: 2.9873 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0132
2023-09-18 12:39:19,622 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-18 12:39:19,627 [INFO] - Epoch: 55/130
2023-09-18 12:41:02,949 [INFO] - Training epoch stats:     Loss: 2.9749 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0162
2023-09-18 12:49:31,116 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-18 12:49:31,118 [INFO] - Epoch: 56/130
2023-09-18 12:51:14,384 [INFO] - Training epoch stats:     Loss: 2.9576 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0231
2023-09-18 12:56:31,831 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-18 12:56:31,833 [INFO] - Epoch: 57/130
2023-09-18 12:58:12,555 [INFO] - Training epoch stats:     Loss: 2.9558 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-18 13:04:21,575 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-18 13:04:21,710 [INFO] - Epoch: 58/130
2023-09-18 13:07:25,069 [INFO] - Training epoch stats:     Loss: 2.9582 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0209
2023-09-18 13:10:24,142 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-18 13:10:24,176 [INFO] - Epoch: 59/130
2023-09-18 13:12:06,844 [INFO] - Training epoch stats:     Loss: 2.9407 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0228
2023-09-18 13:19:05,664 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-18 13:19:05,671 [INFO] - Epoch: 60/130
2023-09-18 13:20:50,703 [INFO] - Training epoch stats:     Loss: 2.9368 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0231
2023-09-18 13:27:33,828 [INFO] - Validation epoch stats:   Loss: 3.0611 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6809 - bPQ-Score: 0.5804 - mPQ-Score: 0.4442 - Tissue-MC-Acc.: 0.0166
2023-09-18 13:27:33,884 [INFO] - New best model - save checkpoint
2023-09-18 13:34:23,879 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-18 13:34:23,882 [INFO] - Epoch: 61/130
2023-09-18 13:36:06,222 [INFO] - Training epoch stats:     Loss: 2.9420 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0217
2023-09-18 13:43:12,601 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-18 13:43:12,606 [INFO] - Epoch: 62/130
2023-09-18 13:44:53,632 [INFO] - Training epoch stats:     Loss: 2.9347 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0231
2023-09-18 13:50:09,939 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-18 13:50:09,946 [INFO] - Epoch: 63/130
2023-09-18 13:51:50,555 [INFO] - Training epoch stats:     Loss: 2.9277 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 14:00:44,758 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-18 14:00:44,766 [INFO] - Epoch: 64/130
2023-09-18 14:02:30,853 [INFO] - Training epoch stats:     Loss: 2.9348 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 14:07:30,703 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-18 14:07:30,705 [INFO] - Epoch: 65/130
2023-09-18 14:09:11,085 [INFO] - Training epoch stats:     Loss: 2.9285 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0165
2023-09-18 14:16:27,171 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-18 14:16:27,179 [INFO] - Epoch: 66/130
2023-09-18 14:18:10,900 [INFO] - Training epoch stats:     Loss: 2.9225 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0220
2023-09-18 14:25:25,674 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-18 14:25:25,700 [INFO] - Epoch: 67/130
2023-09-18 14:27:07,037 [INFO] - Training epoch stats:     Loss: 2.9393 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0184
2023-09-18 14:31:02,077 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-18 14:31:02,235 [INFO] - Epoch: 68/130
2023-09-18 14:33:01,737 [INFO] - Training epoch stats:     Loss: 2.9192 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0187
2023-09-18 14:38:43,054 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-18 14:38:43,108 [INFO] - Epoch: 69/130
2023-09-18 14:40:45,645 [INFO] - Training epoch stats:     Loss: 2.9227 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0257
2023-09-18 14:46:50,962 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-18 14:46:51,035 [INFO] - Epoch: 70/130
2023-09-18 14:49:08,905 [INFO] - Training epoch stats:     Loss: 2.9199 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0191
2023-09-18 14:55:20,913 [INFO] - Validation epoch stats:   Loss: 3.0500 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6811 - bPQ-Score: 0.5781 - mPQ-Score: 0.4473 - Tissue-MC-Acc.: 0.0174
2023-09-18 14:59:43,341 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-18 14:59:43,380 [INFO] - Epoch: 71/130
2023-09-18 15:01:56,656 [INFO] - Training epoch stats:     Loss: 2.9067 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 15:08:49,091 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-18 15:08:49,292 [INFO] - Epoch: 72/130
2023-09-18 15:10:33,475 [INFO] - Training epoch stats:     Loss: 2.8994 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0265
2023-09-18 15:15:25,540 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-18 15:15:25,543 [INFO] - Epoch: 73/130
2023-09-18 15:17:05,027 [INFO] - Training epoch stats:     Loss: 2.9128 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0276
2023-09-18 15:21:57,008 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-18 15:21:57,012 [INFO] - Epoch: 74/130
2023-09-18 15:24:45,419 [INFO] - Training epoch stats:     Loss: 2.8936 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 15:34:16,145 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-18 15:34:16,152 [INFO] - Epoch: 75/130
2023-09-18 15:35:59,733 [INFO] - Training epoch stats:     Loss: 2.9051 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0198
2023-09-18 15:41:34,885 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-18 15:41:34,887 [INFO] - Epoch: 76/130
2023-09-18 15:43:15,919 [INFO] - Training epoch stats:     Loss: 2.9017 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0209
2023-09-18 15:53:08,852 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-18 15:53:08,859 [INFO] - Epoch: 77/130
2023-09-18 15:54:54,851 [INFO] - Training epoch stats:     Loss: 2.8996 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 15:56:28,667 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-18 15:56:28,670 [INFO] - Epoch: 78/130
2023-09-18 15:58:10,888 [INFO] - Training epoch stats:     Loss: 2.8942 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0158
2023-09-18 16:04:45,463 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-18 16:04:45,615 [INFO] - Epoch: 79/130
2023-09-18 16:08:28,245 [INFO] - Training epoch stats:     Loss: 2.8919 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0224
2023-09-18 16:14:25,294 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 16:14:25,302 [INFO] - Epoch: 80/130
2023-09-18 16:16:08,664 [INFO] - Training epoch stats:     Loss: 2.8850 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 16:21:29,217 [INFO] - Validation epoch stats:   Loss: 3.0563 - Binary-Cell-Dice: 0.7624 - Binary-Cell-Jacard: 0.6740 - bPQ-Score: 0.5723 - mPQ-Score: 0.4397 - Tissue-MC-Acc.: 0.0222
2023-09-18 16:30:02,087 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 16:30:02,093 [INFO] - Epoch: 81/130
2023-09-18 16:31:43,028 [INFO] - Training epoch stats:     Loss: 2.8999 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0165
2023-09-18 16:37:14,699 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 16:37:14,845 [INFO] - Epoch: 82/130
2023-09-18 16:38:59,815 [INFO] - Training epoch stats:     Loss: 2.8876 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0235
2023-09-18 16:48:32,332 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-18 16:48:32,337 [INFO] - Epoch: 83/130
2023-09-18 16:50:18,844 [INFO] - Training epoch stats:     Loss: 2.8880 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0228
2023-09-18 16:54:30,149 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 16:54:30,160 [INFO] - Epoch: 84/130
2023-09-18 16:56:11,643 [INFO] - Training epoch stats:     Loss: 2.8754 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 17:04:00,762 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 17:04:00,771 [INFO] - Epoch: 85/130
2023-09-18 17:05:47,186 [INFO] - Training epoch stats:     Loss: 2.8797 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 17:11:14,053 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 17:11:14,058 [INFO] - Epoch: 86/130
2023-09-18 17:12:58,413 [INFO] - Training epoch stats:     Loss: 2.8961 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0158
2023-09-18 17:17:34,302 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 17:17:34,309 [INFO] - Epoch: 87/130
2023-09-18 17:19:17,169 [INFO] - Training epoch stats:     Loss: 2.8724 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 17:25:01,989 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-18 17:25:02,079 [INFO] - Epoch: 88/130
2023-09-18 17:27:44,830 [INFO] - Training epoch stats:     Loss: 2.8722 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0209
2023-09-18 17:31:07,561 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:31:07,564 [INFO] - Epoch: 89/130
2023-09-18 17:32:47,842 [INFO] - Training epoch stats:     Loss: 2.8828 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 17:41:16,237 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:41:16,287 [INFO] - Epoch: 90/130
2023-09-18 17:43:00,509 [INFO] - Training epoch stats:     Loss: 2.8779 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0158
2023-09-18 17:48:18,805 [INFO] - Validation epoch stats:   Loss: 3.0571 - Binary-Cell-Dice: 0.7627 - Binary-Cell-Jacard: 0.6734 - bPQ-Score: 0.5684 - mPQ-Score: 0.4367 - Tissue-MC-Acc.: 0.0230
2023-09-18 17:52:52,444 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:52:52,450 [INFO] - Epoch: 91/130
2023-09-18 17:54:35,687 [INFO] - Training epoch stats:     Loss: 2.8698 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0213
2023-09-18 18:03:13,087 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 18:03:13,095 [INFO] - Epoch: 92/130
2023-09-18 18:04:59,317 [INFO] - Training epoch stats:     Loss: 2.8892 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-18 18:09:51,185 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 18:09:51,189 [INFO] - Epoch: 93/130
2023-09-18 18:11:37,964 [INFO] - Training epoch stats:     Loss: 2.8884 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0231
2023-09-18 18:17:58,081 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 18:17:58,713 [INFO] - Epoch: 94/130
2023-09-18 18:19:39,614 [INFO] - Training epoch stats:     Loss: 2.8795 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0224
2023-09-18 18:23:08,697 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-18 18:23:08,748 [INFO] - Epoch: 95/130
2023-09-18 18:25:02,156 [INFO] - Training epoch stats:     Loss: 2.8732 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0261
2023-09-18 18:30:01,034 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:30:01,041 [INFO] - Epoch: 96/130
2023-09-18 18:31:45,544 [INFO] - Training epoch stats:     Loss: 2.8738 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0184
2023-09-18 18:37:46,234 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:37:46,237 [INFO] - Epoch: 97/130
2023-09-18 18:39:26,740 [INFO] - Training epoch stats:     Loss: 2.8822 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0224
2023-09-18 18:44:29,151 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:44:29,218 [INFO] - Epoch: 98/130
2023-09-18 18:46:58,960 [INFO] - Training epoch stats:     Loss: 2.8688 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0217
2023-09-18 18:54:52,894 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:54:52,896 [INFO] - Epoch: 99/130
2023-09-18 18:56:34,402 [INFO] - Training epoch stats:     Loss: 2.8835 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 19:01:39,838 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:01:39,906 [INFO] - Epoch: 100/130
2023-09-18 19:04:16,595 [INFO] - Training epoch stats:     Loss: 2.8905 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0228
2023-09-18 19:10:11,490 [INFO] - Validation epoch stats:   Loss: 3.0561 - Binary-Cell-Dice: 0.7647 - Binary-Cell-Jacard: 0.6765 - bPQ-Score: 0.5753 - mPQ-Score: 0.4433 - Tissue-MC-Acc.: 0.0234
2023-09-18 19:15:59,445 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:15:59,451 [INFO] - Epoch: 101/130
2023-09-18 19:17:45,065 [INFO] - Training epoch stats:     Loss: 2.8800 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 19:23:00,960 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:23:00,963 [INFO] - Epoch: 102/130
2023-09-18 19:24:41,917 [INFO] - Training epoch stats:     Loss: 2.8793 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0209
2023-09-18 19:33:26,369 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:33:26,371 [INFO] - Epoch: 103/130
2023-09-18 19:35:06,377 [INFO] - Training epoch stats:     Loss: 2.8559 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0235
2023-09-18 19:38:39,371 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:38:39,373 [INFO] - Epoch: 104/130
2023-09-18 19:40:21,465 [INFO] - Training epoch stats:     Loss: 2.8814 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0239
2023-09-18 19:45:13,588 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-18 19:45:13,604 [INFO] - Epoch: 105/130
2023-09-18 19:46:58,445 [INFO] - Training epoch stats:     Loss: 2.8782 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0253
2023-09-18 19:52:01,806 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:52:01,809 [INFO] - Epoch: 106/130
2023-09-18 19:53:46,152 [INFO] - Training epoch stats:     Loss: 2.8691 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0184
2023-09-18 19:57:06,803 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:57:06,817 [INFO] - Epoch: 107/130
2023-09-18 19:58:54,047 [INFO] - Training epoch stats:     Loss: 2.8719 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-18 20:01:32,792 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:01:32,801 [INFO] - Epoch: 108/130
2023-09-18 20:03:15,363 [INFO] - Training epoch stats:     Loss: 2.8754 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0198
2023-09-18 20:09:30,260 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:09:30,288 [INFO] - Epoch: 109/130
2023-09-18 20:11:36,901 [INFO] - Training epoch stats:     Loss: 2.8796 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0246
2023-09-18 20:14:42,391 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:14:42,426 [INFO] - Epoch: 110/130
2023-09-18 20:16:33,231 [INFO] - Training epoch stats:     Loss: 2.8795 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0213
2023-09-18 20:21:27,165 [INFO] - Validation epoch stats:   Loss: 3.0580 - Binary-Cell-Dice: 0.7686 - Binary-Cell-Jacard: 0.6834 - bPQ-Score: 0.5820 - mPQ-Score: 0.4459 - Tissue-MC-Acc.: 0.0234
2023-09-18 20:21:27,224 [INFO] - New best model - save checkpoint
2023-09-18 20:30:38,438 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:30:38,493 [INFO] - Epoch: 111/130
2023-09-18 20:32:27,658 [INFO] - Training epoch stats:     Loss: 2.8645 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0195
2023-09-18 20:36:16,371 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:36:16,409 [INFO] - Epoch: 112/130
2023-09-18 20:38:04,488 [INFO] - Training epoch stats:     Loss: 2.8702 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0220
2023-09-18 20:41:42,435 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:41:42,449 [INFO] - Epoch: 113/130
2023-09-18 20:43:29,352 [INFO] - Training epoch stats:     Loss: 2.8832 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0217
2023-09-18 20:49:08,224 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:49:08,251 [INFO] - Epoch: 114/130
2023-09-18 20:51:23,241 [INFO] - Training epoch stats:     Loss: 2.8708 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0261
2023-09-18 20:53:38,701 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:53:38,765 [INFO] - Epoch: 115/130
