2023-09-18 06:52:02,201 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-18 06:52:02,292 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f0eb3f607c0>]
2023-09-18 06:52:02,292 [INFO] - Using GPU: cuda:0
2023-09-18 06:52:02,294 [INFO] - Using device: cuda:0
2023-09-18 06:52:02,295 [INFO] - Loss functions:
2023-09-18 06:52:02,295 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-18 06:52:41,729 [INFO] - Loaded CellViT-SAM model with backbone: SAM-H
2023-09-18 06:52:41,739 [INFO] -
Model: CellViTSAMStarDist(
  (encoder): ViTCellViTDeit(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 1280, kernel_size=(16, 16), stride=(16, 16))
    )
    (blocks): ModuleList(
      (0-31): 32 x Block(
        (norm1): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=1280, out_features=3840, bias=True)
          (proj): Linear(in_features=1280, out_features=1280, bias=True)
        )
        (norm2): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=1280, out_features=5120, bias=True)
          (lin2): Linear(in_features=5120, out_features=1280, bias=True)
          (act): GELU(approximate='none')
        )
      )
    )
    (neck): Sequential(
      (0): Conv2d(1280, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (1): LayerNorm2d()
      (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
      (3): LayerNorm2d()
    )
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (stardist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (dist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 1, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
)
2023-09-18 06:52:45,035 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViTSAMStarDist                            [1, 6, 256, 256]          --
├─ViTCellViTDeit: 1-1                         [1, 256]                  5,242,880
│    └─PatchEmbed: 2-1                        [1, 16, 16, 1280]         --
│    │    └─Conv2d: 3-1                       [1, 1280, 16, 16]         (984,320)
│    └─ModuleList: 2-2                        --                        --
│    │    └─Block: 3-2                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-3                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-4                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-5                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-6                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-7                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-8                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-9                        [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-10                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-11                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-12                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-13                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-14                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-15                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-16                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-17                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-18                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-19                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-20                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-21                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-22                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-23                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-24                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-25                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-26                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-27                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-28                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-29                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-30                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-31                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-32                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-33                       [1, 16, 16, 1280]         (19,697,760)
│    └─Sequential: 2-3                        [1, 256, 16, 16]          --
│    │    └─Conv2d: 3-34                      [1, 256, 16, 16]          (327,680)
│    │    └─LayerNorm2d: 3-35                 [1, 256, 16, 16]          (512)
│    │    └─Conv2d: 3-36                      [1, 256, 16, 16]          (589,824)
│    │    └─LayerNorm2d: 3-37                 [1, 256, 16, 16]          (512)
├─Linear: 1-2                                 [1, 19]                   4,883
├─Sequential: 1-11                            --                        (recursive)
│    └─ConvTranspose2d: 2-4                   [1, 512, 32, 32]          2,621,952
├─Sequential: 1-4                             [1, 512, 32, 32]          --
│    └─Deconv2DBlock: 2-5                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-38                  [1, 512, 32, 32]          4,982,784
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-6                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-39                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-40                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-41                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-42             [1, 256, 64, 64]          524,544
├─Sequential: 1-6                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-7                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-43                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-8                     [1, 256, 64, 64]          --
│    │    └─Sequential: 3-44                  [1, 256, 64, 64]          1,115,136
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-9                        [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-45                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-46                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-47             [1, 128, 128, 128]        131,200
├─Sequential: 1-8                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-10                    [1, 512, 32, 32]          --
│    │    └─Sequential: 3-48                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-11                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-49                  [1, 256, 64, 64]          1,115,136
│    └─Deconv2DBlock: 2-12                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-50                  [1, 128, 128, 128]        279,040
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-13                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-51                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-52                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-53             [1, 64, 256, 256]         32,832
├─Sequential: 1-10                            [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-14                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-54                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-15                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-55                  [1, 64, 256, 256]         18,624
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-16                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-56                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-57                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-58                      [1, 32, 256, 256]         2,080
├─Sequential: 1-20                            --                        (recursive)
│    └─ConvTranspose2d: 2-17                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-13                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-18                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-59                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-19                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-60                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-61                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-62                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-63             [1, 256, 64, 64]          524,544
├─Sequential: 1-15                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-64                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-21                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-65                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-22                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-66                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-67                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-68             [1, 128, 128, 128]        131,200
├─Sequential: 1-17                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-69                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-24                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-70                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-71                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-26                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-72                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-73                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-74             [1, 64, 256, 256]         32,832
├─Sequential: 1-19                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-27                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-75                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-28                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-76                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-29                       [1, 1, 256, 256]          --
│    │    └─Conv2DBlock: 3-77                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-78                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-79                      [1, 1, 256, 256]          65
├─Sequential: 1-29                            --                        (recursive)
│    └─ConvTranspose2d: 2-30                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-22                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-31                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-80                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-32                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-81                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-82                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-83                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-84             [1, 256, 64, 64]          524,544
├─Sequential: 1-24                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-85                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-34                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-86                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-35                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-87                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-88                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-89             [1, 128, 128, 128]        131,200
├─Sequential: 1-26                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-90                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-37                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-91                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-92                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-39                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-93                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-94                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-95             [1, 64, 256, 256]         32,832
├─Sequential: 1-28                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-40                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-96                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-41                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-97                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-42                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-98                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-99                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-100                     [1, 6, 256, 256]          390
===============================================================================================
Total params: 699,743,034
Trainable params: 62,716,986
Non-trainable params: 637,026,048
Total mult-adds (G): 214.33
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 3239.58
Params size (MB): 2777.19
Estimated Total Size (MB): 6017.55
===============================================================================================
2023-09-18 06:52:46,366 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-18 06:52:46,367 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-18 06:52:46,367 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-18 06:53:09,956 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-18 06:53:09,973 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.5950, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314,
        0.7691, 0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9474,
        0.9667, 0.9823, 0.9883, 0.9902, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200,
        1.0215, 1.0260, 1.0278, 1.0367, 1.0449, 1.0464, 1.0536, 1.0910, 1.0913,
        1.0922, 1.0927, 1.1088, 1.1094, 1.1143, 1.1173, 1.1216, 1.1256, 1.1283,
        1.1287, 1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1777, 1.1792, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2527, 1.2576, 1.2580, 1.2595, 1.2606, 1.2621, 1.2651, 1.2663, 1.2666,
        1.2669, 1.2678, 1.2680, 1.2829, 1.2833, 1.2835, 1.2844, 1.2850, 1.2869,
        1.2884, 1.2899, 1.2902, 1.2915, 1.2929, 1.2972, 1.3012, 1.3055, 1.3141,
        1.3156, 1.3165, 1.3219, 1.3221, 1.3276, 1.3282, 1.3294, 1.3321, 1.3380,
        1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3885, 1.3891, 1.3940,
        1.3971, 1.4028, 1.4033, 1.4099, 1.4111, 1.4229, 1.4243, 1.4275, 1.4336,
        1.4377, 1.4407, 1.4419, 1.4432, 1.4436, 1.4455, 1.4585, 1.4591, 1.4625,
        1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897, 1.4975, 1.5089, 1.5392,
        1.5449, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5612, 1.5641, 1.5647,
        1.5696, 1.5702, 1.5746, 1.5939, 1.5953, 1.6031, 1.6174, 1.6871, 1.7110,
        1.7205, 1.7216, 1.7443, 1.7695, 1.8002, 1.8627, 1.8866])
2023-09-18 06:53:09,973 [INFO] - Instantiate Trainer
2023-09-18 06:53:09,974 [INFO] - Calling Trainer Fit
2023-09-18 06:53:09,974 [INFO] - Starting training, total number of epochs: 130
2023-09-18 06:53:09,974 [INFO] - Epoch: 1/130
2023-09-18 06:54:13,950 [INFO] - Training epoch stats:     Loss: 4.4455 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-18 06:54:50,033 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-18 06:54:50,034 [INFO] - Epoch: 2/130
2023-09-18 06:55:48,832 [INFO] - Training epoch stats:     Loss: 3.7265 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0333
2023-09-18 06:56:45,676 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-18 06:56:45,676 [INFO] - Epoch: 3/130
2023-09-18 06:57:56,262 [INFO] - Training epoch stats:     Loss: 3.4906 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-18 06:58:54,665 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-18 06:58:54,666 [INFO] - Epoch: 4/130
2023-09-18 06:59:53,921 [INFO] - Training epoch stats:     Loss: 3.4274 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0353
2023-09-18 07:01:03,278 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-18 07:01:03,279 [INFO] - Epoch: 5/130
2023-09-18 07:02:04,484 [INFO] - Training epoch stats:     Loss: 3.4064 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0325
2023-09-18 07:04:12,689 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-18 07:04:12,747 [INFO] - Epoch: 6/130
2023-09-18 07:05:16,997 [INFO] - Training epoch stats:     Loss: 3.3800 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-18 07:07:09,530 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-18 07:07:09,576 [INFO] - Epoch: 7/130
2023-09-18 07:08:14,674 [INFO] - Training epoch stats:     Loss: 3.3308 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-18 07:10:43,349 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-18 07:10:43,416 [INFO] - Epoch: 8/130
2023-09-18 07:11:49,157 [INFO] - Training epoch stats:     Loss: 3.3250 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 07:13:18,040 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-18 07:13:18,041 [INFO] - Epoch: 9/130
2023-09-18 07:14:26,996 [INFO] - Training epoch stats:     Loss: 3.3057 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-18 07:16:12,794 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-18 07:16:12,898 [INFO] - Epoch: 10/130
2023-09-18 07:17:24,040 [INFO] - Training epoch stats:     Loss: 3.3250 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-18 07:22:56,439 [INFO] - Validation epoch stats:   Loss: 3.2782 - Binary-Cell-Dice: 0.7040 - Binary-Cell-Jacard: 0.5845 - bPQ-Score: 0.4395 - mPQ-Score: 0.3080 - Tissue-MC-Acc.: 0.0169
2023-09-18 07:22:56,449 [INFO] - New best model - save checkpoint
2023-09-18 07:24:45,688 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-18 07:24:45,690 [INFO] - Epoch: 11/130
2023-09-18 07:25:44,661 [INFO] - Training epoch stats:     Loss: 3.2968 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 07:26:22,021 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-18 07:26:22,021 [INFO] - Epoch: 12/130
2023-09-18 07:27:24,006 [INFO] - Training epoch stats:     Loss: 3.3005 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0277
2023-09-18 07:29:40,098 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-18 07:29:40,662 [INFO] - Epoch: 13/130
2023-09-18 07:30:44,798 [INFO] - Training epoch stats:     Loss: 3.2704 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 07:32:58,977 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-18 07:32:59,086 [INFO] - Epoch: 14/130
2023-09-18 07:34:15,650 [INFO] - Training epoch stats:     Loss: 3.2629 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0345
2023-09-18 07:35:46,596 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-18 07:35:46,596 [INFO] - Epoch: 15/130
2023-09-18 07:36:46,625 [INFO] - Training epoch stats:     Loss: 3.2531 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0337
2023-09-18 07:38:57,706 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-18 07:38:57,793 [INFO] - Epoch: 16/130
2023-09-18 07:40:05,977 [INFO] - Training epoch stats:     Loss: 3.2463 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 07:42:38,750 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-18 07:42:38,821 [INFO] - Epoch: 17/130
2023-09-18 07:43:41,549 [INFO] - Training epoch stats:     Loss: 3.2438 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0341
2023-09-18 07:45:19,790 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-18 07:45:19,824 [INFO] - Epoch: 18/130
2023-09-18 07:46:20,377 [INFO] - Training epoch stats:     Loss: 3.2372 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 07:48:09,314 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-18 07:48:09,381 [INFO] - Epoch: 19/130
2023-09-18 07:49:14,960 [INFO] - Training epoch stats:     Loss: 3.2296 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-18 07:52:01,193 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-18 07:52:01,244 [INFO] - Epoch: 20/130
2023-09-18 07:53:04,166 [INFO] - Training epoch stats:     Loss: 3.2279 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0333
2023-09-18 07:58:22,987 [INFO] - Validation epoch stats:   Loss: 3.1974 - Binary-Cell-Dice: 0.7308 - Binary-Cell-Jacard: 0.6211 - bPQ-Score: 0.5030 - mPQ-Score: 0.3698 - Tissue-MC-Acc.: 0.0169
2023-09-18 07:58:22,989 [INFO] - New best model - save checkpoint
2023-09-18 07:59:49,349 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-18 07:59:49,350 [INFO] - Epoch: 21/130
2023-09-18 08:00:52,339 [INFO] - Training epoch stats:     Loss: 3.2220 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0325
2023-09-18 08:01:29,310 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-18 08:01:29,311 [INFO] - Epoch: 22/130
2023-09-18 08:02:29,703 [INFO] - Training epoch stats:     Loss: 3.2024 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0305
2023-09-18 08:04:41,823 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-18 08:04:41,883 [INFO] - Epoch: 23/130
2023-09-18 08:06:11,780 [INFO] - Training epoch stats:     Loss: 3.1920 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-18 08:08:14,047 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-18 08:08:14,117 [INFO] - Epoch: 24/130
2023-09-18 08:09:53,358 [INFO] - Training epoch stats:     Loss: 3.1979 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0329
2023-09-18 08:12:42,089 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-18 08:12:42,211 [INFO] - Epoch: 25/130
2023-09-18 08:13:46,564 [INFO] - Training epoch stats:     Loss: 3.1951 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0281
2023-09-18 08:15:07,562 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-18 08:15:07,563 [INFO] - Epoch: 26/130
2023-09-18 08:16:45,762 [INFO] - Training epoch stats:     Loss: 3.3419 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 08:20:08,699 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-18 08:20:08,706 [INFO] - Epoch: 27/130
2023-09-18 08:21:56,251 [INFO] - Training epoch stats:     Loss: 3.2756 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0321
2023-09-18 08:23:43,546 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-18 08:23:43,549 [INFO] - Epoch: 28/130
2023-09-18 08:25:18,607 [INFO] - Training epoch stats:     Loss: 3.2289 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 08:29:52,787 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-18 08:29:52,788 [INFO] - Epoch: 29/130
2023-09-18 08:31:26,114 [INFO] - Training epoch stats:     Loss: 3.2072 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0313
2023-09-18 08:35:59,829 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-18 08:35:59,883 [INFO] - Epoch: 30/130
2023-09-18 08:38:20,904 [INFO] - Training epoch stats:     Loss: 3.1928 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0254
2023-09-18 08:45:13,871 [INFO] - Validation epoch stats:   Loss: 3.1832 - Binary-Cell-Dice: 0.7626 - Binary-Cell-Jacard: 0.6655 - bPQ-Score: 0.5418 - mPQ-Score: 0.3973 - Tissue-MC-Acc.: 0.0143
2023-09-18 08:45:13,881 [INFO] - New best model - save checkpoint
2023-09-18 08:52:06,897 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-18 08:52:06,964 [INFO] - Epoch: 31/130
2023-09-18 08:54:24,163 [INFO] - Training epoch stats:     Loss: 3.1519 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 08:57:14,059 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-18 08:57:14,060 [INFO] - Epoch: 32/130
2023-09-18 08:58:47,505 [INFO] - Training epoch stats:     Loss: 3.1470 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0273
2023-09-18 09:04:58,326 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-18 09:04:58,411 [INFO] - Epoch: 33/130
2023-09-18 09:07:08,881 [INFO] - Training epoch stats:     Loss: 3.1343 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-18 09:11:43,553 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-18 09:11:43,738 [INFO] - Epoch: 34/130
2023-09-18 09:13:16,973 [INFO] - Training epoch stats:     Loss: 3.1245 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0182
2023-09-18 09:21:48,895 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-18 09:21:48,901 [INFO] - Epoch: 35/130
2023-09-18 09:23:23,735 [INFO] - Training epoch stats:     Loss: 3.1198 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 09:31:10,636 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-18 09:31:10,644 [INFO] - Epoch: 36/130
2023-09-18 09:32:49,400 [INFO] - Training epoch stats:     Loss: 3.0864 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 09:39:57,669 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-18 09:39:57,783 [INFO] - Epoch: 37/130
2023-09-18 09:41:33,460 [INFO] - Training epoch stats:     Loss: 3.0893 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-18 09:48:43,165 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-18 09:48:43,172 [INFO] - Epoch: 38/130
2023-09-18 09:50:20,931 [INFO] - Training epoch stats:     Loss: 3.0660 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0194
2023-09-18 09:56:42,276 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-18 09:56:43,006 [INFO] - Epoch: 39/130
2023-09-18 09:58:16,157 [INFO] - Training epoch stats:     Loss: 3.0580 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 10:05:07,345 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-18 10:05:07,346 [INFO] - Epoch: 40/130
2023-09-18 10:06:42,039 [INFO] - Training epoch stats:     Loss: 3.0587 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0210
2023-09-18 10:13:52,276 [INFO] - Validation epoch stats:   Loss: 3.0990 - Binary-Cell-Dice: 0.7672 - Binary-Cell-Jacard: 0.6764 - bPQ-Score: 0.5693 - mPQ-Score: 0.4338 - Tissue-MC-Acc.: 0.0120
2023-09-18 10:13:52,279 [INFO] - New best model - save checkpoint
2023-09-18 10:21:13,512 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-18 10:21:13,547 [INFO] - Epoch: 41/130
2023-09-18 10:22:56,542 [INFO] - Training epoch stats:     Loss: 3.0551 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 10:25:36,054 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-18 10:25:36,118 [INFO] - Epoch: 42/130
2023-09-18 10:28:12,374 [INFO] - Training epoch stats:     Loss: 3.0406 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0254
2023-09-18 10:33:05,244 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-18 10:33:05,320 [INFO] - Epoch: 43/130
2023-09-18 10:35:45,700 [INFO] - Training epoch stats:     Loss: 3.0286 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-18 10:38:42,183 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-18 10:38:42,188 [INFO] - Epoch: 44/130
2023-09-18 10:40:22,670 [INFO] - Training epoch stats:     Loss: 3.0327 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-18 10:46:06,502 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-18 10:46:06,506 [INFO] - Epoch: 45/130
2023-09-18 10:47:43,610 [INFO] - Training epoch stats:     Loss: 3.0092 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0174
2023-09-18 10:55:48,263 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-18 10:55:48,269 [INFO] - Epoch: 46/130
2023-09-18 10:57:23,617 [INFO] - Training epoch stats:     Loss: 3.0056 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 11:01:23,218 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-18 11:01:23,705 [INFO] - Epoch: 47/130
2023-09-18 11:02:59,047 [INFO] - Training epoch stats:     Loss: 3.0075 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0198
2023-09-18 11:09:48,312 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-18 11:09:48,319 [INFO] - Epoch: 48/130
2023-09-18 11:11:27,644 [INFO] - Training epoch stats:     Loss: 2.9918 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-18 11:17:50,020 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-18 11:17:50,025 [INFO] - Epoch: 49/130
2023-09-18 11:19:25,819 [INFO] - Training epoch stats:     Loss: 2.9835 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0186
2023-09-18 11:25:04,528 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-18 11:25:04,643 [INFO] - Epoch: 50/130
2023-09-18 11:28:04,933 [INFO] - Training epoch stats:     Loss: 2.9762 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-18 11:33:18,050 [INFO] - Validation epoch stats:   Loss: 3.0788 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6779 - bPQ-Score: 0.5707 - mPQ-Score: 0.4366 - Tissue-MC-Acc.: 0.0162
2023-09-18 11:33:18,060 [INFO] - New best model - save checkpoint
2023-09-18 11:40:56,565 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-18 11:40:56,597 [INFO] - Epoch: 51/130
2023-09-18 11:43:23,603 [INFO] - Training epoch stats:     Loss: 2.9760 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 11:49:03,196 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-18 11:49:03,203 [INFO] - Epoch: 52/130
2023-09-18 11:50:39,131 [INFO] - Training epoch stats:     Loss: 2.9729 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-18 11:53:13,690 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-18 11:53:13,928 [INFO] - Epoch: 53/130
2023-09-18 11:54:49,285 [INFO] - Training epoch stats:     Loss: 2.9544 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 12:07:18,312 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-18 12:07:18,319 [INFO] - Epoch: 54/130
2023-09-18 12:08:53,615 [INFO] - Training epoch stats:     Loss: 2.9580 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 12:15:18,658 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-18 12:15:18,943 [INFO] - Epoch: 55/130
2023-09-18 12:16:53,429 [INFO] - Training epoch stats:     Loss: 2.9477 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 12:23:03,439 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-18 12:23:03,443 [INFO] - Epoch: 56/130
2023-09-18 12:24:43,069 [INFO] - Training epoch stats:     Loss: 2.9585 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-18 12:31:02,183 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-18 12:31:02,186 [INFO] - Epoch: 57/130
2023-09-18 12:32:37,248 [INFO] - Training epoch stats:     Loss: 2.9565 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0166
2023-09-18 12:39:19,812 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-18 12:39:19,819 [INFO] - Epoch: 58/130
2023-09-18 12:40:56,678 [INFO] - Training epoch stats:     Loss: 2.9349 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-18 12:49:31,021 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-18 12:49:31,021 [INFO] - Epoch: 59/130
2023-09-18 12:51:08,681 [INFO] - Training epoch stats:     Loss: 2.9577 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0226
2023-09-18 12:56:31,851 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-18 12:56:31,853 [INFO] - Epoch: 60/130
2023-09-18 12:58:06,235 [INFO] - Training epoch stats:     Loss: 2.9485 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-18 13:03:06,043 [INFO] - Validation epoch stats:   Loss: 3.0765 - Binary-Cell-Dice: 0.7772 - Binary-Cell-Jacard: 0.6900 - bPQ-Score: 0.5799 - mPQ-Score: 0.4446 - Tissue-MC-Acc.: 0.0136
2023-09-18 13:03:06,122 [INFO] - New best model - save checkpoint
2023-09-18 13:10:27,185 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-18 13:10:27,188 [INFO] - Epoch: 61/130
2023-09-18 13:12:00,977 [INFO] - Training epoch stats:     Loss: 2.9306 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-18 13:19:03,438 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-18 13:19:03,951 [INFO] - Epoch: 62/130
2023-09-18 13:20:45,777 [INFO] - Training epoch stats:     Loss: 2.9133 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-18 13:25:57,432 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-18 13:25:57,532 [INFO] - Epoch: 63/130
2023-09-18 13:27:38,633 [INFO] - Training epoch stats:     Loss: 2.9234 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 13:32:50,960 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-18 13:32:51,490 [INFO] - Epoch: 64/130
2023-09-18 13:34:26,622 [INFO] - Training epoch stats:     Loss: 2.9298 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-18 13:42:50,111 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-18 13:42:50,176 [INFO] - Epoch: 65/130
2023-09-18 13:44:45,776 [INFO] - Training epoch stats:     Loss: 2.9266 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 13:50:10,235 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-18 13:50:10,242 [INFO] - Epoch: 66/130
2023-09-18 13:51:44,502 [INFO] - Training epoch stats:     Loss: 2.9092 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0289
2023-09-18 14:00:44,257 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-18 14:00:44,264 [INFO] - Epoch: 67/130
2023-09-18 14:02:25,680 [INFO] - Training epoch stats:     Loss: 2.9119 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 14:07:30,762 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-18 14:07:30,764 [INFO] - Epoch: 68/130
2023-09-18 14:09:05,790 [INFO] - Training epoch stats:     Loss: 2.9004 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 14:16:27,462 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-18 14:16:27,468 [INFO] - Epoch: 69/130
2023-09-18 14:18:04,825 [INFO] - Training epoch stats:     Loss: 2.9198 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 14:25:25,706 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-18 14:25:25,709 [INFO] - Epoch: 70/130
2023-09-18 14:27:01,073 [INFO] - Training epoch stats:     Loss: 2.9118 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-18 14:32:55,258 [INFO] - Validation epoch stats:   Loss: 3.0799 - Binary-Cell-Dice: 0.7719 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.5791 - mPQ-Score: 0.4492 - Tissue-MC-Acc.: 0.0143
2023-09-18 14:37:41,786 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-18 14:37:41,862 [INFO] - Epoch: 71/130
2023-09-18 14:40:22,137 [INFO] - Training epoch stats:     Loss: 2.8954 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-18 14:47:03,087 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-18 14:47:03,156 [INFO] - Epoch: 72/130
2023-09-18 14:49:02,604 [INFO] - Training epoch stats:     Loss: 2.8862 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-18 14:54:10,574 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-18 14:54:10,577 [INFO] - Epoch: 73/130
2023-09-18 14:55:46,653 [INFO] - Training epoch stats:     Loss: 2.8999 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-18 15:00:17,321 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-18 15:00:17,324 [INFO] - Epoch: 74/130
2023-09-18 15:01:50,077 [INFO] - Training epoch stats:     Loss: 2.9038 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-18 15:08:49,617 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-18 15:08:49,625 [INFO] - Epoch: 75/130
2023-09-18 15:10:26,978 [INFO] - Training epoch stats:     Loss: 2.8946 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0246
2023-09-18 15:15:24,428 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-18 15:15:24,436 [INFO] - Epoch: 76/130
2023-09-18 15:16:58,490 [INFO] - Training epoch stats:     Loss: 2.8872 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 15:21:56,933 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-18 15:21:56,938 [INFO] - Epoch: 77/130
2023-09-18 15:23:34,602 [INFO] - Training epoch stats:     Loss: 2.8960 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0273
2023-09-18 15:34:13,449 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-18 15:34:13,453 [INFO] - Epoch: 78/130
2023-09-18 15:35:53,732 [INFO] - Training epoch stats:     Loss: 2.8858 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 15:41:33,596 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-18 15:41:33,598 [INFO] - Epoch: 79/130
2023-09-18 15:43:09,738 [INFO] - Training epoch stats:     Loss: 2.8879 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 15:53:08,100 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 15:53:08,103 [INFO] - Epoch: 80/130
2023-09-18 15:54:48,003 [INFO] - Training epoch stats:     Loss: 2.8870 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0289
2023-09-18 15:59:41,090 [INFO] - Validation epoch stats:   Loss: 3.0686 - Binary-Cell-Dice: 0.7765 - Binary-Cell-Jacard: 0.6885 - bPQ-Score: 0.5820 - mPQ-Score: 0.4533 - Tissue-MC-Acc.: 0.0147
2023-09-18 15:59:41,245 [INFO] - New best model - save checkpoint
2023-09-18 16:12:58,727 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 16:12:58,806 [INFO] - Epoch: 81/130
2023-09-18 16:15:51,597 [INFO] - Training epoch stats:     Loss: 2.8797 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 16:18:38,317 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-18 16:18:38,321 [INFO] - Epoch: 82/130
2023-09-18 16:20:12,958 [INFO] - Training epoch stats:     Loss: 2.8747 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-18 16:30:02,082 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-18 16:30:02,086 [INFO] - Epoch: 83/130
2023-09-18 16:31:36,705 [INFO] - Training epoch stats:     Loss: 2.8673 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-18 16:37:15,021 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 16:37:15,028 [INFO] - Epoch: 84/130
2023-09-18 16:38:52,594 [INFO] - Training epoch stats:     Loss: 2.8838 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-18 16:48:33,142 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 16:48:33,149 [INFO] - Epoch: 85/130
2023-09-18 16:50:12,486 [INFO] - Training epoch stats:     Loss: 2.8817 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0325
2023-09-18 16:54:30,636 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 16:54:30,638 [INFO] - Epoch: 86/130
2023-09-18 16:56:03,885 [INFO] - Training epoch stats:     Loss: 2.8719 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-18 17:04:00,249 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-18 17:04:00,257 [INFO] - Epoch: 87/130
2023-09-18 17:05:39,818 [INFO] - Training epoch stats:     Loss: 2.8823 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 17:11:14,154 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-18 17:11:14,160 [INFO] - Epoch: 88/130
2023-09-18 17:12:51,728 [INFO] - Training epoch stats:     Loss: 2.8672 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 17:17:34,303 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:17:34,310 [INFO] - Epoch: 89/130
2023-09-18 17:19:10,511 [INFO] - Training epoch stats:     Loss: 2.8703 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0222
2023-09-18 17:25:31,775 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:25:31,849 [INFO] - Epoch: 90/130
2023-09-18 17:27:38,382 [INFO] - Training epoch stats:     Loss: 2.8782 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 17:34:00,680 [INFO] - Validation epoch stats:   Loss: 3.0738 - Binary-Cell-Dice: 0.7754 - Binary-Cell-Jacard: 0.6865 - bPQ-Score: 0.5816 - mPQ-Score: 0.4468 - Tissue-MC-Acc.: 0.0147
2023-09-18 17:41:21,082 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:41:21,086 [INFO] - Epoch: 91/130
2023-09-18 17:42:55,321 [INFO] - Training epoch stats:     Loss: 2.8675 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 17:45:50,512 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:45:50,521 [INFO] - Epoch: 92/130
2023-09-18 17:47:25,210 [INFO] - Training epoch stats:     Loss: 2.8707 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 17:52:50,941 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 17:52:50,947 [INFO] - Epoch: 93/130
2023-09-18 17:54:26,434 [INFO] - Training epoch stats:     Loss: 2.8768 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0277
2023-09-18 18:03:13,363 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-18 18:03:13,372 [INFO] - Epoch: 94/130
2023-09-18 18:04:52,251 [INFO] - Training epoch stats:     Loss: 2.8755 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0270
2023-09-18 18:09:51,927 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-18 18:09:51,932 [INFO] - Epoch: 95/130
2023-09-18 18:11:30,563 [INFO] - Training epoch stats:     Loss: 2.8834 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 18:17:58,880 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:17:58,883 [INFO] - Epoch: 96/130
2023-09-18 18:19:33,084 [INFO] - Training epoch stats:     Loss: 2.8670 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-18 18:23:01,850 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:23:01,906 [INFO] - Epoch: 97/130
2023-09-18 18:24:55,220 [INFO] - Training epoch stats:     Loss: 2.8711 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-18 18:30:01,298 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:30:01,306 [INFO] - Epoch: 98/130
2023-09-18 18:31:39,051 [INFO] - Training epoch stats:     Loss: 2.8630 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0190
2023-09-18 18:37:44,898 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:37:44,900 [INFO] - Epoch: 99/130
2023-09-18 18:39:18,890 [INFO] - Training epoch stats:     Loss: 2.8627 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 18:44:36,760 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:44:36,838 [INFO] - Epoch: 100/130
2023-09-18 18:46:52,883 [INFO] - Training epoch stats:     Loss: 2.8686 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0273
2023-09-18 18:52:04,538 [INFO] - Validation epoch stats:   Loss: 3.0722 - Binary-Cell-Dice: 0.7746 - Binary-Cell-Jacard: 0.6856 - bPQ-Score: 0.5802 - mPQ-Score: 0.4496 - Tissue-MC-Acc.: 0.0147
2023-09-18 18:54:53,046 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 18:54:53,048 [INFO] - Epoch: 101/130
2023-09-18 18:56:27,389 [INFO] - Training epoch stats:     Loss: 2.8700 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-18 19:01:39,282 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:01:39,364 [INFO] - Epoch: 102/130
2023-09-18 19:04:09,389 [INFO] - Training epoch stats:     Loss: 2.8744 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-18 19:09:42,137 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:09:42,144 [INFO] - Epoch: 103/130
2023-09-18 19:11:18,719 [INFO] - Training epoch stats:     Loss: 2.8536 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 19:15:59,837 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-18 19:15:59,843 [INFO] - Epoch: 104/130
2023-09-18 19:17:38,385 [INFO] - Training epoch stats:     Loss: 2.8666 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 19:22:59,796 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-18 19:22:59,799 [INFO] - Epoch: 105/130
2023-09-18 19:24:33,213 [INFO] - Training epoch stats:     Loss: 2.8452 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 19:33:24,759 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:33:24,762 [INFO] - Epoch: 106/130
2023-09-18 19:34:58,405 [INFO] - Training epoch stats:     Loss: 2.8694 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0313
2023-09-18 19:38:39,802 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:38:39,803 [INFO] - Epoch: 107/130
2023-09-18 19:40:14,504 [INFO] - Training epoch stats:     Loss: 2.8653 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0341
2023-09-18 19:45:12,730 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:45:12,749 [INFO] - Epoch: 108/130
2023-09-18 19:46:51,871 [INFO] - Training epoch stats:     Loss: 2.8612 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-18 19:52:02,739 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:52:02,746 [INFO] - Epoch: 109/130
2023-09-18 19:53:39,106 [INFO] - Training epoch stats:     Loss: 2.8694 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0281
2023-09-18 19:56:57,142 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 19:56:57,217 [INFO] - Epoch: 110/130
2023-09-18 19:58:47,369 [INFO] - Training epoch stats:     Loss: 2.8499 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-18 20:04:39,871 [INFO] - Validation epoch stats:   Loss: 3.0718 - Binary-Cell-Dice: 0.7772 - Binary-Cell-Jacard: 0.6894 - bPQ-Score: 0.5878 - mPQ-Score: 0.4547 - Tissue-MC-Acc.: 0.0154
2023-09-18 20:04:39,979 [INFO] - New best model - save checkpoint
2023-09-18 20:11:32,588 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:11:32,629 [INFO] - Epoch: 111/130
2023-09-18 20:13:48,305 [INFO] - Training epoch stats:     Loss: 2.8630 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0321
2023-09-18 20:15:47,043 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:15:47,057 [INFO] - Epoch: 112/130
2023-09-18 20:17:25,617 [INFO] - Training epoch stats:     Loss: 2.8633 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-18 20:19:03,109 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:19:03,113 [INFO] - Epoch: 113/130
2023-09-18 20:20:41,314 [INFO] - Training epoch stats:     Loss: 2.8588 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0250
2023-09-18 20:25:50,448 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:25:50,509 [INFO] - Epoch: 114/130
2023-09-18 20:28:38,838 [INFO] - Training epoch stats:     Loss: 2.8503 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-18 20:31:17,635 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:31:17,656 [INFO] - Epoch: 115/130
2023-09-18 20:32:56,839 [INFO] - Training epoch stats:     Loss: 2.8535 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0289
2023-09-18 20:36:19,885 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:36:19,888 [INFO] - Epoch: 116/130
2023-09-18 20:37:57,231 [INFO] - Training epoch stats:     Loss: 2.8673 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-18 20:41:42,448 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:41:42,463 [INFO] - Epoch: 117/130
2023-09-18 20:43:22,469 [INFO] - Training epoch stats:     Loss: 2.8567 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-18 20:49:16,015 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-18 20:49:16,060 [INFO] - Epoch: 118/130
2023-09-18 20:51:16,341 [INFO] - Training epoch stats:     Loss: 2.8670 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
