Loading model: /homes/fhoerst/histo-projects/CellViT/results/PanNuke/Revision/CellViT/Common-Loss/ViT256/Best-Setting/Fold-2/checkpoints/latest_checkpoint.pth
<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 794
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 194
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 498
Detected cells before cleaning: 962
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 253
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 629
Detected cells before cleaning: 619
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 148
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 405
Detected cells before cleaning: 539
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 134
Iteration 1: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 354
Detected cells before cleaning: 729
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 183
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 2
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 465
Detected cells before cleaning: 433
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 102
Iteration 1: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 269
Detected cells before cleaning: 975
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 255
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 612
Detected cells before cleaning: 575
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 157
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 365
Detected cells before cleaning: 613
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 145
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 388
Detected cells before cleaning: 478
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 117
Iteration 1: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 318
Detected cells before cleaning: 413
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 107
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 257
Detected cells before cleaning: 308
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 81
Iteration 1: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 191
Detected cells before cleaning: 669
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 177
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 436
Detected cells before cleaning: 564
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 129
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 371
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.79621422290802
Binary-Cell-Jacard-Mean:  0.6616395115852356
bPQ:                      0.5876514647610535
bDQ:                      0.7722240503723246
bSQ:                      0.7602519997930219
f1_detection:             0.8323303885914157
precision_detection:      0.9217025085071138
recall_detection:         0.7602485530900939
