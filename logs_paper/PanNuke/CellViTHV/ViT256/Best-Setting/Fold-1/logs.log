2023-09-09 06:18:44,485 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-09 06:18:44,568 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f0263392220>]
2023-09-09 06:18:44,569 [INFO] - Using GPU: cuda:0
2023-09-09 06:18:44,569 [INFO] - Using device: cuda:0
2023-09-09 06:18:44,570 [INFO] - Loss functions:
2023-09-09 06:18:44,570 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': Xentropy<PERSON>oss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-09 06:19:16,964 [INFO] - Loaded CellVit256 model
2023-09-09 06:19:16,973 [INFO] -
Model: CellViT256(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
)
2023-09-09 06:19:18,613 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256                                    [1, 6, 256, 256]          --
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 2, 256, 256]          130
├─Sequential: 1-19                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 2, 256, 256]          130
├─Sequential: 1-28                            --                        (recursive)
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          479,544
├─Sequential: 1-21                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          319,744
├─Sequential: 1-23                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        131,200
├─Sequential: 1-25                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         32,832
├─Sequential: 1-27                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-76                      [1, 6, 256, 256]          390
===============================================================================================
Total params: 46,750,349
Trainable params: 25,084,685
Non-trainable params: 21,665,664
Total mult-adds (G): 132.89
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1672.49
Params size (MB): 186.70
Estimated Total Size (MB): 1859.98
===============================================================================================
2023-09-09 06:19:32,574 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-09 06:19:32,582 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-09 06:19:32,582 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-09 06:20:07,676 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-09 06:20:07,743 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314, 0.7691,
        0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9667, 0.9823,
        0.9883, 0.9902, 1.0044, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200, 1.0215,
        1.0260, 1.0278, 1.0367, 1.0449, 1.0536, 1.0910, 1.0913, 1.0922, 1.0927,
        1.1088, 1.1094, 1.1143, 1.1149, 1.1161, 1.1173, 1.1216, 1.1256, 1.1287,
        1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1480, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1660, 1.1777, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2580, 1.2595, 1.2621, 1.2651, 1.2663, 1.2666, 1.2669, 1.2678, 1.2680,
        1.2699, 1.2829, 1.2835, 1.2844, 1.2850, 1.2869, 1.2884, 1.2890, 1.2899,
        1.2902, 1.2905, 1.2915, 1.2929, 1.2972, 1.3055, 1.3072, 1.3141, 1.3156,
        1.3165, 1.3219, 1.3221, 1.3227, 1.3234, 1.3276, 1.3282, 1.3294, 1.3306,
        1.3321, 1.3380, 1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3766,
        1.3885, 1.3891, 1.3940, 1.3971, 1.4033, 1.4081, 1.4099, 1.4111, 1.4197,
        1.4275, 1.4277, 1.4332, 1.4336, 1.4362, 1.4377, 1.4407, 1.4419, 1.4436,
        1.4455, 1.4585, 1.4591, 1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897,
        1.4975, 1.5089, 1.5130, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5641,
        1.5647, 1.5696, 1.5702, 1.5712, 1.5746, 1.5953, 1.6174, 1.6260, 1.6871,
        1.6874, 1.7349, 1.7468, 1.7502, 1.7695, 1.8002, 1.8615, 1.8627, 1.8866,
        1.9432])
2023-09-09 06:20:07,744 [INFO] - Instantiate Trainer
2023-09-09 06:20:07,744 [INFO] - Calling Trainer Fit
2023-09-09 06:20:07,744 [INFO] - Starting training, total number of epochs: 130
2023-09-09 06:20:07,744 [INFO] - Epoch: 1/130
2023-09-09 06:22:55,197 [INFO] - Training epoch stats:     Loss: 8.1665 - Binary-Cell-Dice: 0.6991 - Binary-Cell-Jacard: 0.5726 - Tissue-MC-Acc.: 0.2677
2023-09-09 06:25:02,082 [INFO] - Validation epoch stats:   Loss: 6.6999 - Binary-Cell-Dice: 0.7513 - Binary-Cell-Jacard: 0.6408 - PQ-Score: 0.5004 - Tissue-MC-Acc.: 0.3738
2023-09-09 06:25:02,084 [INFO] - New best model - save checkpoint
2023-09-09 06:25:11,094 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-09 06:25:11,094 [INFO] - Epoch: 2/130
2023-09-09 06:27:30,416 [INFO] - Training epoch stats:     Loss: 6.3467 - Binary-Cell-Dice: 0.7558 - Binary-Cell-Jacard: 0.6438 - Tissue-MC-Acc.: 0.3490
2023-09-09 06:29:26,845 [INFO] - Validation epoch stats:   Loss: 5.9811 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6651 - PQ-Score: 0.5372 - Tissue-MC-Acc.: 0.4185
2023-09-09 06:29:26,847 [INFO] - New best model - save checkpoint
2023-09-09 06:29:35,447 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-09 06:29:35,447 [INFO] - Epoch: 3/130
2023-09-09 06:31:51,606 [INFO] - Training epoch stats:     Loss: 6.0409 - Binary-Cell-Dice: 0.7648 - Binary-Cell-Jacard: 0.6521 - Tissue-MC-Acc.: 0.3825
2023-09-09 06:33:41,526 [INFO] - Validation epoch stats:   Loss: 5.7491 - Binary-Cell-Dice: 0.7718 - Binary-Cell-Jacard: 0.6711 - PQ-Score: 0.5413 - Tissue-MC-Acc.: 0.4483
2023-09-09 06:33:41,529 [INFO] - New best model - save checkpoint
2023-09-09 06:33:50,700 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-09 06:33:50,701 [INFO] - Epoch: 4/130
2023-09-09 06:36:03,417 [INFO] - Training epoch stats:     Loss: 5.9414 - Binary-Cell-Dice: 0.7686 - Binary-Cell-Jacard: 0.6656 - Tissue-MC-Acc.: 0.3916
2023-09-09 06:38:02,123 [INFO] - Validation epoch stats:   Loss: 5.8028 - Binary-Cell-Dice: 0.7765 - Binary-Cell-Jacard: 0.6818 - PQ-Score: 0.5555 - Tissue-MC-Acc.: 0.4372
2023-09-09 06:38:02,126 [INFO] - New best model - save checkpoint
2023-09-09 06:38:11,247 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-09 06:38:11,248 [INFO] - Epoch: 5/130
2023-09-09 06:40:23,913 [INFO] - Training epoch stats:     Loss: 5.8466 - Binary-Cell-Dice: 0.7738 - Binary-Cell-Jacard: 0.6682 - Tissue-MC-Acc.: 0.4160
2023-09-09 06:42:35,043 [INFO] - Validation epoch stats:   Loss: 5.5488 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6870 - PQ-Score: 0.5609 - Tissue-MC-Acc.: 0.4744
2023-09-09 06:42:35,051 [INFO] - New best model - save checkpoint
2023-09-09 06:42:54,101 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-09 06:42:54,102 [INFO] - Epoch: 6/130
2023-09-09 06:45:08,800 [INFO] - Training epoch stats:     Loss: 5.8152 - Binary-Cell-Dice: 0.7788 - Binary-Cell-Jacard: 0.6722 - Tissue-MC-Acc.: 0.4330
2023-09-09 06:47:01,479 [INFO] - Validation epoch stats:   Loss: 5.6132 - Binary-Cell-Dice: 0.7781 - Binary-Cell-Jacard: 0.6802 - PQ-Score: 0.5605 - Tissue-MC-Acc.: 0.4637
2023-09-09 06:47:05,526 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-09 06:47:05,527 [INFO] - Epoch: 7/130
2023-09-09 06:49:19,358 [INFO] - Training epoch stats:     Loss: 5.7635 - Binary-Cell-Dice: 0.7828 - Binary-Cell-Jacard: 0.6755 - Tissue-MC-Acc.: 0.4270
2023-09-09 06:51:13,008 [INFO] - Validation epoch stats:   Loss: 5.5599 - Binary-Cell-Dice: 0.7799 - Binary-Cell-Jacard: 0.6886 - PQ-Score: 0.5695 - Tissue-MC-Acc.: 0.4796
2023-09-09 06:51:13,019 [INFO] - New best model - save checkpoint
2023-09-09 06:51:30,491 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-09 06:51:30,492 [INFO] - Epoch: 8/130
2023-09-09 06:53:48,429 [INFO] - Training epoch stats:     Loss: 5.7384 - Binary-Cell-Dice: 0.7812 - Binary-Cell-Jacard: 0.6784 - Tissue-MC-Acc.: 0.4401
2023-09-09 06:55:43,592 [INFO] - Validation epoch stats:   Loss: 5.5207 - Binary-Cell-Dice: 0.7807 - Binary-Cell-Jacard: 0.6934 - PQ-Score: 0.5747 - Tissue-MC-Acc.: 0.4816
2023-09-09 06:55:43,600 [INFO] - New best model - save checkpoint
2023-09-09 06:56:02,669 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-09 06:56:02,670 [INFO] - Epoch: 9/130
2023-09-09 06:58:18,278 [INFO] - Training epoch stats:     Loss: 5.6885 - Binary-Cell-Dice: 0.7790 - Binary-Cell-Jacard: 0.6782 - Tissue-MC-Acc.: 0.4665
2023-09-09 07:00:10,507 [INFO] - Validation epoch stats:   Loss: 5.5228 - Binary-Cell-Dice: 0.7773 - Binary-Cell-Jacard: 0.6868 - PQ-Score: 0.5736 - Tissue-MC-Acc.: 0.5002
2023-09-09 07:00:15,075 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-09 07:00:15,076 [INFO] - Epoch: 10/130
2023-09-09 07:02:24,416 [INFO] - Training epoch stats:     Loss: 5.6231 - Binary-Cell-Dice: 0.7876 - Binary-Cell-Jacard: 0.6885 - Tissue-MC-Acc.: 0.4763
2023-09-09 07:04:21,599 [INFO] - Validation epoch stats:   Loss: 5.5157 - Binary-Cell-Dice: 0.7800 - Binary-Cell-Jacard: 0.6912 - PQ-Score: 0.5746 - Tissue-MC-Acc.: 0.5073
2023-09-09 07:04:32,148 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-09 07:04:32,149 [INFO] - Epoch: 11/130
2023-09-09 07:06:49,145 [INFO] - Training epoch stats:     Loss: 5.6075 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.6849 - Tissue-MC-Acc.: 0.4593
2023-09-09 07:08:44,962 [INFO] - Validation epoch stats:   Loss: 5.4215 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.6991 - PQ-Score: 0.5784 - Tissue-MC-Acc.: 0.4978
2023-09-09 07:08:44,966 [INFO] - New best model - save checkpoint
2023-09-09 07:08:56,954 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-09 07:08:56,954 [INFO] - Epoch: 12/130
2023-09-09 07:11:14,683 [INFO] - Training epoch stats:     Loss: 5.5889 - Binary-Cell-Dice: 0.7840 - Binary-Cell-Jacard: 0.6863 - Tissue-MC-Acc.: 0.4669
2023-09-09 07:13:16,809 [INFO] - Validation epoch stats:   Loss: 5.4521 - Binary-Cell-Dice: 0.7843 - Binary-Cell-Jacard: 0.6991 - PQ-Score: 0.5764 - Tissue-MC-Acc.: 0.5042
2023-09-09 07:13:21,199 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-09 07:13:21,200 [INFO] - Epoch: 13/130
2023-09-09 07:15:34,666 [INFO] - Training epoch stats:     Loss: 5.5354 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.6938 - Tissue-MC-Acc.: 0.4736
2023-09-09 07:17:30,814 [INFO] - Validation epoch stats:   Loss: 5.4517 - Binary-Cell-Dice: 0.7832 - Binary-Cell-Jacard: 0.6951 - PQ-Score: 0.5794 - Tissue-MC-Acc.: 0.5105
2023-09-09 07:17:30,818 [INFO] - New best model - save checkpoint
2023-09-09 07:17:39,398 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-09 07:17:39,399 [INFO] - Epoch: 14/130
2023-09-09 07:19:53,969 [INFO] - Training epoch stats:     Loss: 5.5310 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.6980 - Tissue-MC-Acc.: 0.4864
2023-09-09 07:21:54,318 [INFO] - Validation epoch stats:   Loss: 5.3968 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6948 - PQ-Score: 0.5804 - Tissue-MC-Acc.: 0.5125
2023-09-09 07:21:54,329 [INFO] - New best model - save checkpoint
2023-09-09 07:22:13,214 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-09 07:22:13,215 [INFO] - Epoch: 15/130
2023-09-09 07:24:26,474 [INFO] - Training epoch stats:     Loss: 5.5046 - Binary-Cell-Dice: 0.7907 - Binary-Cell-Jacard: 0.6938 - Tissue-MC-Acc.: 0.4789
2023-09-09 07:26:26,826 [INFO] - Validation epoch stats:   Loss: 5.3369 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.6994 - PQ-Score: 0.5829 - Tissue-MC-Acc.: 0.5244
2023-09-09 07:26:26,836 [INFO] - New best model - save checkpoint
2023-09-09 07:26:45,018 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-09 07:26:45,019 [INFO] - Epoch: 16/130
2023-09-09 07:29:06,638 [INFO] - Training epoch stats:     Loss: 5.4821 - Binary-Cell-Dice: 0.7924 - Binary-Cell-Jacard: 0.6941 - Tissue-MC-Acc.: 0.4864
2023-09-09 07:31:03,084 [INFO] - Validation epoch stats:   Loss: 5.3865 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.6993 - PQ-Score: 0.5844 - Tissue-MC-Acc.: 0.5279
2023-09-09 07:31:03,088 [INFO] - New best model - save checkpoint
2023-09-09 07:31:11,947 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-09 07:31:11,948 [INFO] - Epoch: 17/130
2023-09-09 07:33:30,659 [INFO] - Training epoch stats:     Loss: 5.4799 - Binary-Cell-Dice: 0.7912 - Binary-Cell-Jacard: 0.6940 - Tissue-MC-Acc.: 0.4940
2023-09-09 07:35:21,976 [INFO] - Validation epoch stats:   Loss: 5.3391 - Binary-Cell-Dice: 0.7873 - Binary-Cell-Jacard: 0.7039 - PQ-Score: 0.5833 - Tissue-MC-Acc.: 0.5256
2023-09-09 07:35:31,926 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-09 07:35:31,927 [INFO] - Epoch: 18/130
2023-09-09 07:37:50,798 [INFO] - Training epoch stats:     Loss: 5.5110 - Binary-Cell-Dice: 0.7949 - Binary-Cell-Jacard: 0.6971 - Tissue-MC-Acc.: 0.4857
2023-09-09 07:39:44,998 [INFO] - Validation epoch stats:   Loss: 5.3454 - Binary-Cell-Dice: 0.7883 - Binary-Cell-Jacard: 0.7036 - PQ-Score: 0.5885 - Tissue-MC-Acc.: 0.5220
2023-09-09 07:39:45,002 [INFO] - New best model - save checkpoint
2023-09-09 07:39:53,140 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-09 07:39:53,140 [INFO] - Epoch: 19/130
2023-09-09 07:42:03,982 [INFO] - Training epoch stats:     Loss: 5.4353 - Binary-Cell-Dice: 0.7939 - Binary-Cell-Jacard: 0.6983 - Tissue-MC-Acc.: 0.4936
2023-09-09 07:43:56,498 [INFO] - Validation epoch stats:   Loss: 5.3400 - Binary-Cell-Dice: 0.7887 - Binary-Cell-Jacard: 0.7033 - PQ-Score: 0.5875 - Tissue-MC-Acc.: 0.5311
2023-09-09 07:44:07,401 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-09 07:44:07,401 [INFO] - Epoch: 20/130
2023-09-09 07:46:17,064 [INFO] - Training epoch stats:     Loss: 5.4224 - Binary-Cell-Dice: 0.7913 - Binary-Cell-Jacard: 0.6970 - Tissue-MC-Acc.: 0.4834
2023-09-09 07:48:07,007 [INFO] - Validation epoch stats:   Loss: 5.2928 - Binary-Cell-Dice: 0.7889 - Binary-Cell-Jacard: 0.7047 - PQ-Score: 0.5867 - Tissue-MC-Acc.: 0.5283
2023-09-09 07:48:11,278 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-09 07:48:11,278 [INFO] - Epoch: 21/130
2023-09-09 07:50:26,283 [INFO] - Training epoch stats:     Loss: 5.4256 - Binary-Cell-Dice: 0.7926 - Binary-Cell-Jacard: 0.6996 - Tissue-MC-Acc.: 0.4936
2023-09-09 07:52:19,239 [INFO] - Validation epoch stats:   Loss: 5.3149 - Binary-Cell-Dice: 0.7896 - Binary-Cell-Jacard: 0.7037 - PQ-Score: 0.5885 - Tissue-MC-Acc.: 0.5390
2023-09-09 07:52:19,250 [INFO] - New best model - save checkpoint
2023-09-09 07:52:37,870 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-09 07:52:37,871 [INFO] - Epoch: 22/130
2023-09-09 07:54:48,971 [INFO] - Training epoch stats:     Loss: 5.4012 - Binary-Cell-Dice: 0.7995 - Binary-Cell-Jacard: 0.7054 - Tissue-MC-Acc.: 0.4974
2023-09-09 07:56:38,973 [INFO] - Validation epoch stats:   Loss: 5.2752 - Binary-Cell-Dice: 0.7886 - Binary-Cell-Jacard: 0.7055 - PQ-Score: 0.5890 - Tissue-MC-Acc.: 0.5470
2023-09-09 07:56:38,981 [INFO] - New best model - save checkpoint
2023-09-09 07:56:57,603 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-09 07:56:57,604 [INFO] - Epoch: 23/130
2023-09-09 07:59:11,504 [INFO] - Training epoch stats:     Loss: 5.4348 - Binary-Cell-Dice: 0.8013 - Binary-Cell-Jacard: 0.7055 - Tissue-MC-Acc.: 0.4849
2023-09-09 08:01:10,638 [INFO] - Validation epoch stats:   Loss: 5.3033 - Binary-Cell-Dice: 0.7900 - Binary-Cell-Jacard: 0.7066 - PQ-Score: 0.5898 - Tissue-MC-Acc.: 0.5295
2023-09-09 08:01:10,649 [INFO] - New best model - save checkpoint
2023-09-09 08:01:30,688 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-09 08:01:30,689 [INFO] - Epoch: 24/130
2023-09-09 08:03:44,487 [INFO] - Training epoch stats:     Loss: 5.3952 - Binary-Cell-Dice: 0.8042 - Binary-Cell-Jacard: 0.7034 - Tissue-MC-Acc.: 0.5147
2023-09-09 08:05:41,493 [INFO] - Validation epoch stats:   Loss: 5.2898 - Binary-Cell-Dice: 0.7915 - Binary-Cell-Jacard: 0.7089 - PQ-Score: 0.5921 - Tissue-MC-Acc.: 0.5410
2023-09-09 08:05:41,503 [INFO] - New best model - save checkpoint
2023-09-09 08:05:55,906 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-09 08:05:55,907 [INFO] - Epoch: 25/130
2023-09-09 08:08:12,463 [INFO] - Training epoch stats:     Loss: 5.3995 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7016 - Tissue-MC-Acc.: 0.5075
2023-09-09 08:10:04,752 [INFO] - Validation epoch stats:   Loss: 5.2641 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7025 - PQ-Score: 0.5918 - Tissue-MC-Acc.: 0.5414
2023-09-09 08:10:14,863 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-09 08:10:14,864 [INFO] - Epoch: 26/130
2023-09-09 08:12:39,782 [INFO] - Training epoch stats:     Loss: 5.6306 - Binary-Cell-Dice: 0.7792 - Binary-Cell-Jacard: 0.6785 - Tissue-MC-Acc.: 0.5105
2023-09-09 08:14:33,580 [INFO] - Validation epoch stats:   Loss: 5.4095 - Binary-Cell-Dice: 0.7831 - Binary-Cell-Jacard: 0.6988 - PQ-Score: 0.5747 - Tissue-MC-Acc.: 0.6373
2023-09-09 08:14:42,489 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-09 08:14:42,489 [INFO] - Epoch: 27/130
2023-09-09 08:17:02,772 [INFO] - Training epoch stats:     Loss: 5.5204 - Binary-Cell-Dice: 0.7982 - Binary-Cell-Jacard: 0.6947 - Tissue-MC-Acc.: 0.5843
2023-09-09 08:19:04,374 [INFO] - Validation epoch stats:   Loss: 5.3712 - Binary-Cell-Dice: 0.7852 - Binary-Cell-Jacard: 0.6948 - PQ-Score: 0.5765 - Tissue-MC-Acc.: 0.6080
2023-09-09 08:19:10,559 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-09 08:19:10,559 [INFO] - Epoch: 28/130
2023-09-09 08:21:33,752 [INFO] - Training epoch stats:     Loss: 5.4318 - Binary-Cell-Dice: 0.7885 - Binary-Cell-Jacard: 0.6972 - Tissue-MC-Acc.: 0.6389
2023-09-09 08:23:25,049 [INFO] - Validation epoch stats:   Loss: 5.3359 - Binary-Cell-Dice: 0.7819 - Binary-Cell-Jacard: 0.6940 - PQ-Score: 0.5804 - Tissue-MC-Acc.: 0.6346
2023-09-09 08:23:31,591 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-09 08:23:31,592 [INFO] - Epoch: 29/130
2023-09-09 08:25:55,319 [INFO] - Training epoch stats:     Loss: 5.3540 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7003 - Tissue-MC-Acc.: 0.6864
2023-09-09 08:27:52,355 [INFO] - Validation epoch stats:   Loss: 5.2660 - Binary-Cell-Dice: 0.7872 - Binary-Cell-Jacard: 0.7017 - PQ-Score: 0.5855 - Tissue-MC-Acc.: 0.7229
2023-09-09 08:28:00,342 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-09 08:28:00,343 [INFO] - Epoch: 30/130
2023-09-09 08:30:23,844 [INFO] - Training epoch stats:     Loss: 5.3888 - Binary-Cell-Dice: 0.7959 - Binary-Cell-Jacard: 0.6994 - Tissue-MC-Acc.: 0.7319
2023-09-09 08:32:15,000 [INFO] - Validation epoch stats:   Loss: 5.2201 - Binary-Cell-Dice: 0.7887 - Binary-Cell-Jacard: 0.7052 - PQ-Score: 0.5854 - Tissue-MC-Acc.: 0.7301
2023-09-09 08:32:32,026 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-09 08:32:32,027 [INFO] - Epoch: 31/130
2023-09-09 08:34:53,480 [INFO] - Training epoch stats:     Loss: 5.2698 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7124 - Tissue-MC-Acc.: 0.7737
2023-09-09 08:36:42,395 [INFO] - Validation epoch stats:   Loss: 5.2191 - Binary-Cell-Dice: 0.7813 - Binary-Cell-Jacard: 0.6986 - PQ-Score: 0.5843 - Tissue-MC-Acc.: 0.7610
2023-09-09 08:36:49,022 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-09 08:36:49,022 [INFO] - Epoch: 32/130
2023-09-09 08:39:09,613 [INFO] - Training epoch stats:     Loss: 5.2588 - Binary-Cell-Dice: 0.7982 - Binary-Cell-Jacard: 0.7098 - Tissue-MC-Acc.: 0.8121
2023-09-09 08:41:06,090 [INFO] - Validation epoch stats:   Loss: 5.1614 - Binary-Cell-Dice: 0.7909 - Binary-Cell-Jacard: 0.7092 - PQ-Score: 0.5937 - Tissue-MC-Acc.: 0.8117
2023-09-09 08:41:06,100 [INFO] - New best model - save checkpoint
2023-09-09 08:41:36,134 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-09 08:41:36,134 [INFO] - Epoch: 33/130
2023-09-09 08:44:00,654 [INFO] - Training epoch stats:     Loss: 5.2653 - Binary-Cell-Dice: 0.8014 - Binary-Cell-Jacard: 0.7118 - Tissue-MC-Acc.: 0.8490
2023-09-09 08:45:50,286 [INFO] - Validation epoch stats:   Loss: 5.1243 - Binary-Cell-Dice: 0.7911 - Binary-Cell-Jacard: 0.7098 - PQ-Score: 0.5928 - Tissue-MC-Acc.: 0.8026
2023-09-09 08:45:56,762 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-09 08:45:56,763 [INFO] - Epoch: 34/130
2023-09-09 08:48:20,060 [INFO] - Training epoch stats:     Loss: 5.1831 - Binary-Cell-Dice: 0.8055 - Binary-Cell-Jacard: 0.7167 - Tissue-MC-Acc.: 0.8577
2023-09-09 08:50:11,651 [INFO] - Validation epoch stats:   Loss: 5.1394 - Binary-Cell-Dice: 0.7905 - Binary-Cell-Jacard: 0.7086 - PQ-Score: 0.5971 - Tissue-MC-Acc.: 0.8363
2023-09-09 08:50:11,662 [INFO] - New best model - save checkpoint
2023-09-09 08:50:34,145 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-09 08:50:34,146 [INFO] - Epoch: 35/130
2023-09-09 08:52:53,148 [INFO] - Training epoch stats:     Loss: 5.1677 - Binary-Cell-Dice: 0.8050 - Binary-Cell-Jacard: 0.7167 - Tissue-MC-Acc.: 0.8878
2023-09-09 08:54:44,189 [INFO] - Validation epoch stats:   Loss: 5.1235 - Binary-Cell-Dice: 0.7896 - Binary-Cell-Jacard: 0.7099 - PQ-Score: 0.5970 - Tissue-MC-Acc.: 0.8502
2023-09-09 08:54:50,578 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-09 08:54:50,579 [INFO] - Epoch: 36/130
2023-09-09 08:57:08,310 [INFO] - Training epoch stats:     Loss: 5.1373 - Binary-Cell-Dice: 0.8069 - Binary-Cell-Jacard: 0.7169 - Tissue-MC-Acc.: 0.8904
2023-09-09 08:59:07,158 [INFO] - Validation epoch stats:   Loss: 5.1022 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.7079 - PQ-Score: 0.5927 - Tissue-MC-Acc.: 0.8656
2023-09-09 08:59:21,620 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-09 08:59:21,620 [INFO] - Epoch: 37/130
2023-09-09 09:01:37,677 [INFO] - Training epoch stats:     Loss: 5.1076 - Binary-Cell-Dice: 0.8098 - Binary-Cell-Jacard: 0.7202 - Tissue-MC-Acc.: 0.8968
2023-09-09 09:03:30,394 [INFO] - Validation epoch stats:   Loss: 5.0860 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7129 - PQ-Score: 0.5956 - Tissue-MC-Acc.: 0.8870
2023-09-09 09:03:37,231 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-09 09:03:37,232 [INFO] - Epoch: 38/130
2023-09-09 09:05:53,688 [INFO] - Training epoch stats:     Loss: 5.1186 - Binary-Cell-Dice: 0.8064 - Binary-Cell-Jacard: 0.7203 - Tissue-MC-Acc.: 0.9187
2023-09-09 09:07:45,128 [INFO] - Validation epoch stats:   Loss: 5.0777 - Binary-Cell-Dice: 0.7947 - Binary-Cell-Jacard: 0.7162 - PQ-Score: 0.6025 - Tissue-MC-Acc.: 0.8783
2023-09-09 09:07:45,133 [INFO] - New best model - save checkpoint
2023-09-09 09:07:58,400 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-09 09:07:58,400 [INFO] - Epoch: 39/130
2023-09-09 09:10:17,249 [INFO] - Training epoch stats:     Loss: 5.0844 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7233 - Tissue-MC-Acc.: 0.9330
2023-09-09 09:12:15,247 [INFO] - Validation epoch stats:   Loss: 5.0872 - Binary-Cell-Dice: 0.7933 - Binary-Cell-Jacard: 0.7133 - PQ-Score: 0.6020 - Tissue-MC-Acc.: 0.8942
2023-09-09 09:12:29,559 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-09 09:12:29,560 [INFO] - Epoch: 40/130
2023-09-09 09:14:47,084 [INFO] - Training epoch stats:     Loss: 5.0515 - Binary-Cell-Dice: 0.8086 - Binary-Cell-Jacard: 0.7231 - Tissue-MC-Acc.: 0.9443
2023-09-09 09:16:38,983 [INFO] - Validation epoch stats:   Loss: 5.0547 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7161 - PQ-Score: 0.6060 - Tissue-MC-Acc.: 0.8847
2023-09-09 09:16:38,994 [INFO] - New best model - save checkpoint
2023-09-09 09:17:12,549 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-09 09:17:12,550 [INFO] - Epoch: 41/130
2023-09-09 09:19:29,782 [INFO] - Training epoch stats:     Loss: 4.9951 - Binary-Cell-Dice: 0.8141 - Binary-Cell-Jacard: 0.7296 - Tissue-MC-Acc.: 0.9529
2023-09-09 09:21:25,099 [INFO] - Validation epoch stats:   Loss: 5.0553 - Binary-Cell-Dice: 0.7935 - Binary-Cell-Jacard: 0.7129 - PQ-Score: 0.6042 - Tissue-MC-Acc.: 0.8989
2023-09-09 09:21:37,298 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-09 09:21:37,299 [INFO] - Epoch: 42/130
2023-09-09 09:24:04,147 [INFO] - Training epoch stats:     Loss: 5.0168 - Binary-Cell-Dice: 0.8117 - Binary-Cell-Jacard: 0.7291 - Tissue-MC-Acc.: 0.9499
2023-09-09 09:25:57,486 [INFO] - Validation epoch stats:   Loss: 5.0574 - Binary-Cell-Dice: 0.7918 - Binary-Cell-Jacard: 0.7129 - PQ-Score: 0.6040 - Tissue-MC-Acc.: 0.9053
2023-09-09 09:26:04,177 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-09 09:26:04,177 [INFO] - Epoch: 43/130
2023-09-09 09:28:27,674 [INFO] - Training epoch stats:     Loss: 5.0050 - Binary-Cell-Dice: 0.8077 - Binary-Cell-Jacard: 0.7250 - Tissue-MC-Acc.: 0.9657
2023-09-09 09:30:22,125 [INFO] - Validation epoch stats:   Loss: 5.0361 - Binary-Cell-Dice: 0.7911 - Binary-Cell-Jacard: 0.7143 - PQ-Score: 0.6021 - Tissue-MC-Acc.: 0.9112
2023-09-09 09:30:37,768 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-09 09:30:37,768 [INFO] - Epoch: 44/130
2023-09-09 09:33:01,450 [INFO] - Training epoch stats:     Loss: 4.9934 - Binary-Cell-Dice: 0.8122 - Binary-Cell-Jacard: 0.7340 - Tissue-MC-Acc.: 0.9725
2023-09-09 09:34:50,656 [INFO] - Validation epoch stats:   Loss: 5.0506 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7143 - PQ-Score: 0.6031 - Tissue-MC-Acc.: 0.9096
2023-09-09 09:35:03,470 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-09 09:35:03,471 [INFO] - Epoch: 45/130
2023-09-09 09:37:27,475 [INFO] - Training epoch stats:     Loss: 4.9573 - Binary-Cell-Dice: 0.8114 - Binary-Cell-Jacard: 0.7313 - Tissue-MC-Acc.: 0.9639
2023-09-09 09:39:20,173 [INFO] - Validation epoch stats:   Loss: 5.0335 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7162 - PQ-Score: 0.6037 - Tissue-MC-Acc.: 0.9025
2023-09-09 09:39:26,804 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-09 09:39:26,805 [INFO] - Epoch: 46/130
2023-09-09 09:41:52,033 [INFO] - Training epoch stats:     Loss: 4.9720 - Binary-Cell-Dice: 0.8089 - Binary-Cell-Jacard: 0.7283 - Tissue-MC-Acc.: 0.9725
2023-09-09 09:43:48,950 [INFO] - Validation epoch stats:   Loss: 5.0071 - Binary-Cell-Dice: 0.7954 - Binary-Cell-Jacard: 0.7185 - PQ-Score: 0.6065 - Tissue-MC-Acc.: 0.9176
2023-09-09 09:43:48,955 [INFO] - New best model - save checkpoint
2023-09-09 09:44:19,709 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-09 09:44:19,710 [INFO] - Epoch: 47/130
2023-09-09 09:46:39,209 [INFO] - Training epoch stats:     Loss: 4.9169 - Binary-Cell-Dice: 0.8118 - Binary-Cell-Jacard: 0.7363 - Tissue-MC-Acc.: 0.9706
2023-09-09 09:48:35,203 [INFO] - Validation epoch stats:   Loss: 5.0164 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7173 - PQ-Score: 0.6077 - Tissue-MC-Acc.: 0.9180
2023-09-09 09:48:35,214 [INFO] - New best model - save checkpoint
2023-09-09 09:49:07,172 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-09 09:49:07,173 [INFO] - Epoch: 48/130
2023-09-09 09:51:36,900 [INFO] - Training epoch stats:     Loss: 4.9383 - Binary-Cell-Dice: 0.8185 - Binary-Cell-Jacard: 0.7373 - Tissue-MC-Acc.: 0.9793
2023-09-09 09:53:33,512 [INFO] - Validation epoch stats:   Loss: 5.0130 - Binary-Cell-Dice: 0.7950 - Binary-Cell-Jacard: 0.7183 - PQ-Score: 0.6051 - Tissue-MC-Acc.: 0.9207
2023-09-09 09:53:39,601 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-09 09:53:39,602 [INFO] - Epoch: 49/130
2023-09-09 09:55:58,395 [INFO] - Training epoch stats:     Loss: 4.9037 - Binary-Cell-Dice: 0.8171 - Binary-Cell-Jacard: 0.7346 - Tissue-MC-Acc.: 0.9816
2023-09-09 09:57:55,355 [INFO] - Validation epoch stats:   Loss: 4.9899 - Binary-Cell-Dice: 0.7954 - Binary-Cell-Jacard: 0.7190 - PQ-Score: 0.6072 - Tissue-MC-Acc.: 0.9263
2023-09-09 09:58:10,926 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-09 09:58:10,927 [INFO] - Epoch: 50/130
2023-09-09 10:00:30,993 [INFO] - Training epoch stats:     Loss: 4.9358 - Binary-Cell-Dice: 0.8150 - Binary-Cell-Jacard: 0.7326 - Tissue-MC-Acc.: 0.9823
2023-09-09 10:02:26,851 [INFO] - Validation epoch stats:   Loss: 4.9974 - Binary-Cell-Dice: 0.7942 - Binary-Cell-Jacard: 0.7175 - PQ-Score: 0.6054 - Tissue-MC-Acc.: 0.9275
2023-09-09 10:02:45,570 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-09 10:02:45,571 [INFO] - Epoch: 51/130
2023-09-09 10:05:05,738 [INFO] - Training epoch stats:     Loss: 4.9363 - Binary-Cell-Dice: 0.8141 - Binary-Cell-Jacard: 0.7354 - Tissue-MC-Acc.: 0.9785
2023-09-09 10:06:55,791 [INFO] - Validation epoch stats:   Loss: 5.0098 - Binary-Cell-Dice: 0.7952 - Binary-Cell-Jacard: 0.7183 - PQ-Score: 0.6069 - Tissue-MC-Acc.: 0.9314
2023-09-09 10:07:08,544 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-09 10:07:08,545 [INFO] - Epoch: 52/130
2023-09-09 10:09:28,284 [INFO] - Training epoch stats:     Loss: 4.9053 - Binary-Cell-Dice: 0.8130 - Binary-Cell-Jacard: 0.7352 - Tissue-MC-Acc.: 0.9857
2023-09-09 10:11:22,465 [INFO] - Validation epoch stats:   Loss: 5.0116 - Binary-Cell-Dice: 0.7950 - Binary-Cell-Jacard: 0.7168 - PQ-Score: 0.6055 - Tissue-MC-Acc.: 0.9283
2023-09-09 10:11:37,830 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-09 10:11:37,831 [INFO] - Epoch: 53/130
2023-09-09 10:13:53,749 [INFO] - Training epoch stats:     Loss: 4.9135 - Binary-Cell-Dice: 0.8173 - Binary-Cell-Jacard: 0.7375 - Tissue-MC-Acc.: 0.9846
2023-09-09 10:15:45,346 [INFO] - Validation epoch stats:   Loss: 4.9870 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7175 - PQ-Score: 0.6066 - Tissue-MC-Acc.: 0.9271
2023-09-09 10:15:51,183 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-09 10:15:51,183 [INFO] - Epoch: 54/130
2023-09-09 10:18:17,317 [INFO] - Training epoch stats:     Loss: 4.8679 - Binary-Cell-Dice: 0.8207 - Binary-Cell-Jacard: 0.7401 - Tissue-MC-Acc.: 0.9789
2023-09-09 10:20:14,239 [INFO] - Validation epoch stats:   Loss: 4.9983 - Binary-Cell-Dice: 0.7952 - Binary-Cell-Jacard: 0.7189 - PQ-Score: 0.6082 - Tissue-MC-Acc.: 0.9227
2023-09-09 10:20:14,250 [INFO] - New best model - save checkpoint
2023-09-09 10:20:41,185 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-09 10:20:41,186 [INFO] - Epoch: 55/130
2023-09-09 10:23:04,368 [INFO] - Training epoch stats:     Loss: 4.9036 - Binary-Cell-Dice: 0.8147 - Binary-Cell-Jacard: 0.7368 - Tissue-MC-Acc.: 0.9853
2023-09-09 10:25:01,759 [INFO] - Validation epoch stats:   Loss: 4.9958 - Binary-Cell-Dice: 0.7971 - Binary-Cell-Jacard: 0.7196 - PQ-Score: 0.6079 - Tissue-MC-Acc.: 0.9259
2023-09-09 10:25:08,910 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-09 10:25:08,911 [INFO] - Epoch: 56/130
2023-09-09 10:27:25,411 [INFO] - Training epoch stats:     Loss: 4.8439 - Binary-Cell-Dice: 0.8205 - Binary-Cell-Jacard: 0.7420 - Tissue-MC-Acc.: 0.9861
2023-09-09 10:29:21,616 [INFO] - Validation epoch stats:   Loss: 4.9841 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7192 - PQ-Score: 0.6092 - Tissue-MC-Acc.: 0.9326
2023-09-09 10:29:21,624 [INFO] - New best model - save checkpoint
2023-09-09 10:29:51,160 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-09 10:29:51,161 [INFO] - Epoch: 57/130
2023-09-09 10:32:15,990 [INFO] - Training epoch stats:     Loss: 4.8503 - Binary-Cell-Dice: 0.8206 - Binary-Cell-Jacard: 0.7427 - Tissue-MC-Acc.: 0.9872
2023-09-09 10:34:12,153 [INFO] - Validation epoch stats:   Loss: 4.9843 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7206 - PQ-Score: 0.6113 - Tissue-MC-Acc.: 0.9271
2023-09-09 10:34:12,158 [INFO] - New best model - save checkpoint
2023-09-09 10:34:38,135 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-09 10:34:38,136 [INFO] - Epoch: 58/130
2023-09-09 10:36:59,736 [INFO] - Training epoch stats:     Loss: 4.8634 - Binary-Cell-Dice: 0.8239 - Binary-Cell-Jacard: 0.7425 - Tissue-MC-Acc.: 0.9898
2023-09-09 10:38:48,698 [INFO] - Validation epoch stats:   Loss: 4.9806 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7204 - PQ-Score: 0.6107 - Tissue-MC-Acc.: 0.9283
2023-09-09 10:39:01,921 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-09 10:39:01,922 [INFO] - Epoch: 59/130
2023-09-09 10:41:18,644 [INFO] - Training epoch stats:     Loss: 4.8363 - Binary-Cell-Dice: 0.8250 - Binary-Cell-Jacard: 0.7449 - Tissue-MC-Acc.: 0.9853
2023-09-09 10:43:15,449 [INFO] - Validation epoch stats:   Loss: 4.9906 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7206 - PQ-Score: 0.6108 - Tissue-MC-Acc.: 0.9291
2023-09-09 10:43:26,208 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-09 10:43:26,209 [INFO] - Epoch: 60/130
2023-09-09 10:45:47,595 [INFO] - Training epoch stats:     Loss: 4.8458 - Binary-Cell-Dice: 0.8187 - Binary-Cell-Jacard: 0.7417 - Tissue-MC-Acc.: 0.9921
2023-09-09 10:47:45,017 [INFO] - Validation epoch stats:   Loss: 4.9816 - Binary-Cell-Dice: 0.7968 - Binary-Cell-Jacard: 0.7204 - PQ-Score: 0.6099 - Tissue-MC-Acc.: 0.9294
2023-09-09 10:48:02,483 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-09 10:48:02,484 [INFO] - Epoch: 61/130
2023-09-09 10:50:21,485 [INFO] - Training epoch stats:     Loss: 4.8070 - Binary-Cell-Dice: 0.8179 - Binary-Cell-Jacard: 0.7442 - Tissue-MC-Acc.: 0.9906
2023-09-09 10:52:18,029 [INFO] - Validation epoch stats:   Loss: 4.9828 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7194 - PQ-Score: 0.6120 - Tissue-MC-Acc.: 0.9334
2023-09-09 10:52:18,039 [INFO] - New best model - save checkpoint
2023-09-09 10:52:41,830 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-09 10:52:41,831 [INFO] - Epoch: 62/130
2023-09-09 10:55:01,071 [INFO] - Training epoch stats:     Loss: 4.8283 - Binary-Cell-Dice: 0.8207 - Binary-Cell-Jacard: 0.7434 - Tissue-MC-Acc.: 0.9910
2023-09-09 10:56:50,411 [INFO] - Validation epoch stats:   Loss: 4.9685 - Binary-Cell-Dice: 0.7977 - Binary-Cell-Jacard: 0.7217 - PQ-Score: 0.6117 - Tissue-MC-Acc.: 0.9394
2023-09-09 10:57:02,456 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-09 10:57:02,457 [INFO] - Epoch: 63/130
2023-09-09 10:59:21,294 [INFO] - Training epoch stats:     Loss: 4.8426 - Binary-Cell-Dice: 0.8275 - Binary-Cell-Jacard: 0.7494 - Tissue-MC-Acc.: 0.9898
2023-09-09 11:01:19,495 [INFO] - Validation epoch stats:   Loss: 4.9878 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6107 - Tissue-MC-Acc.: 0.9346
2023-09-09 11:01:34,373 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-09 11:01:34,374 [INFO] - Epoch: 64/130
2023-09-09 11:03:52,826 [INFO] - Training epoch stats:     Loss: 4.7817 - Binary-Cell-Dice: 0.8216 - Binary-Cell-Jacard: 0.7446 - Tissue-MC-Acc.: 0.9891
2023-09-09 11:05:52,393 [INFO] - Validation epoch stats:   Loss: 4.9799 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7218 - PQ-Score: 0.6124 - Tissue-MC-Acc.: 0.9298
2023-09-09 11:05:52,403 [INFO] - New best model - save checkpoint
2023-09-09 11:06:21,836 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-09 11:06:21,837 [INFO] - Epoch: 65/130
2023-09-09 11:08:40,859 [INFO] - Training epoch stats:     Loss: 4.8189 - Binary-Cell-Dice: 0.8249 - Binary-Cell-Jacard: 0.7490 - Tissue-MC-Acc.: 0.9902
2023-09-09 11:10:31,542 [INFO] - Validation epoch stats:   Loss: 4.9834 - Binary-Cell-Dice: 0.7977 - Binary-Cell-Jacard: 0.7212 - PQ-Score: 0.6122 - Tissue-MC-Acc.: 0.9326
2023-09-09 11:10:37,565 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-09 11:10:37,566 [INFO] - Epoch: 66/130
2023-09-09 11:12:58,686 [INFO] - Training epoch stats:     Loss: 4.8154 - Binary-Cell-Dice: 0.8193 - Binary-Cell-Jacard: 0.7470 - Tissue-MC-Acc.: 0.9887
2023-09-09 11:14:54,149 [INFO] - Validation epoch stats:   Loss: 4.9711 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7209 - PQ-Score: 0.6123 - Tissue-MC-Acc.: 0.9318
2023-09-09 11:15:09,850 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-09 11:15:09,850 [INFO] - Epoch: 67/130
2023-09-09 11:17:29,890 [INFO] - Training epoch stats:     Loss: 4.7942 - Binary-Cell-Dice: 0.8231 - Binary-Cell-Jacard: 0.7503 - Tissue-MC-Acc.: 0.9925
2023-09-09 11:19:25,899 [INFO] - Validation epoch stats:   Loss: 4.9705 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7211 - PQ-Score: 0.6117 - Tissue-MC-Acc.: 0.9358
2023-09-09 11:19:31,487 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-09 11:19:31,488 [INFO] - Epoch: 68/130
2023-09-09 11:21:50,632 [INFO] - Training epoch stats:     Loss: 4.7730 - Binary-Cell-Dice: 0.8285 - Binary-Cell-Jacard: 0.7531 - Tissue-MC-Acc.: 0.9944
2023-09-09 11:23:42,700 [INFO] - Validation epoch stats:   Loss: 4.9751 - Binary-Cell-Dice: 0.7980 - Binary-Cell-Jacard: 0.7213 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9362
2023-09-09 11:23:42,705 [INFO] - New best model - save checkpoint
2023-09-09 11:23:54,641 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-09 11:23:54,641 [INFO] - Epoch: 69/130
2023-09-09 11:26:12,189 [INFO] - Training epoch stats:     Loss: 4.8408 - Binary-Cell-Dice: 0.8143 - Binary-Cell-Jacard: 0.7455 - Tissue-MC-Acc.: 0.9864
2023-09-09 11:28:08,494 [INFO] - Validation epoch stats:   Loss: 4.9703 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7209 - PQ-Score: 0.6121 - Tissue-MC-Acc.: 0.9358
2023-09-09 11:28:15,129 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-09 11:28:15,129 [INFO] - Epoch: 70/130
2023-09-09 11:30:29,122 [INFO] - Training epoch stats:     Loss: 4.7693 - Binary-Cell-Dice: 0.8159 - Binary-Cell-Jacard: 0.7434 - Tissue-MC-Acc.: 0.9936
2023-09-09 11:32:22,404 [INFO] - Validation epoch stats:   Loss: 4.9784 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7211 - PQ-Score: 0.6123 - Tissue-MC-Acc.: 0.9409
2023-09-09 11:32:27,866 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-09 11:32:27,866 [INFO] - Epoch: 71/130
2023-09-09 11:34:49,390 [INFO] - Training epoch stats:     Loss: 4.7699 - Binary-Cell-Dice: 0.8225 - Binary-Cell-Jacard: 0.7473 - Tissue-MC-Acc.: 0.9932
2023-09-09 11:36:48,205 [INFO] - Validation epoch stats:   Loss: 4.9734 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7206 - PQ-Score: 0.6125 - Tissue-MC-Acc.: 0.9413
2023-09-09 11:37:01,808 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-09 11:37:01,809 [INFO] - Epoch: 72/130
2023-09-09 11:39:21,978 [INFO] - Training epoch stats:     Loss: 4.7689 - Binary-Cell-Dice: 0.8237 - Binary-Cell-Jacard: 0.7503 - Tissue-MC-Acc.: 0.9898
2023-09-09 11:41:15,785 [INFO] - Validation epoch stats:   Loss: 4.9802 - Binary-Cell-Dice: 0.7961 - Binary-Cell-Jacard: 0.7197 - PQ-Score: 0.6120 - Tissue-MC-Acc.: 0.9409
2023-09-09 11:41:29,012 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-09 11:41:29,013 [INFO] - Epoch: 73/130
2023-09-09 11:43:51,125 [INFO] - Training epoch stats:     Loss: 4.7663 - Binary-Cell-Dice: 0.8245 - Binary-Cell-Jacard: 0.7482 - Tissue-MC-Acc.: 0.9932
2023-09-09 11:45:44,722 [INFO] - Validation epoch stats:   Loss: 4.9716 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7211 - PQ-Score: 0.6137 - Tissue-MC-Acc.: 0.9378
2023-09-09 11:45:44,733 [INFO] - New best model - save checkpoint
2023-09-09 11:46:10,653 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 11:46:10,654 [INFO] - Epoch: 74/130
2023-09-09 11:48:33,499 [INFO] - Training epoch stats:     Loss: 4.7617 - Binary-Cell-Dice: 0.8279 - Binary-Cell-Jacard: 0.7547 - Tissue-MC-Acc.: 0.9928
2023-09-09 11:50:31,701 [INFO] - Validation epoch stats:   Loss: 4.9786 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7213 - PQ-Score: 0.6119 - Tissue-MC-Acc.: 0.9405
2023-09-09 11:50:38,247 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 11:50:38,248 [INFO] - Epoch: 75/130
2023-09-09 11:52:59,077 [INFO] - Training epoch stats:     Loss: 4.7641 - Binary-Cell-Dice: 0.8238 - Binary-Cell-Jacard: 0.7527 - Tissue-MC-Acc.: 0.9928
2023-09-09 11:54:50,482 [INFO] - Validation epoch stats:   Loss: 4.9667 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7204 - PQ-Score: 0.6112 - Tissue-MC-Acc.: 0.9413
2023-09-09 11:54:56,346 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-09 11:54:56,347 [INFO] - Epoch: 76/130
2023-09-09 11:57:15,948 [INFO] - Training epoch stats:     Loss: 4.7419 - Binary-Cell-Dice: 0.8209 - Binary-Cell-Jacard: 0.7509 - Tissue-MC-Acc.: 0.9940
2023-09-09 11:59:11,316 [INFO] - Validation epoch stats:   Loss: 4.9697 - Binary-Cell-Dice: 0.7950 - Binary-Cell-Jacard: 0.7202 - PQ-Score: 0.6109 - Tissue-MC-Acc.: 0.9394
2023-09-09 11:59:27,376 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 11:59:27,376 [INFO] - Epoch: 77/130
2023-09-09 12:01:48,629 [INFO] - Training epoch stats:     Loss: 4.7427 - Binary-Cell-Dice: 0.8246 - Binary-Cell-Jacard: 0.7477 - Tissue-MC-Acc.: 0.9925
2023-09-09 12:03:40,713 [INFO] - Validation epoch stats:   Loss: 4.9620 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7221 - PQ-Score: 0.6133 - Tissue-MC-Acc.: 0.9386
2023-09-09 12:03:47,682 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 12:03:47,682 [INFO] - Epoch: 78/130
2023-09-09 12:06:06,282 [INFO] - Training epoch stats:     Loss: 4.7667 - Binary-Cell-Dice: 0.8268 - Binary-Cell-Jacard: 0.7531 - Tissue-MC-Acc.: 0.9940
2023-09-09 12:07:56,549 [INFO] - Validation epoch stats:   Loss: 4.9626 - Binary-Cell-Dice: 0.7971 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6126 - Tissue-MC-Acc.: 0.9378
2023-09-09 12:08:14,247 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-09 12:08:14,248 [INFO] - Epoch: 79/130
2023-09-09 12:10:37,361 [INFO] - Training epoch stats:     Loss: 4.7260 - Binary-Cell-Dice: 0.8216 - Binary-Cell-Jacard: 0.7516 - Tissue-MC-Acc.: 0.9947
2023-09-09 12:12:34,175 [INFO] - Validation epoch stats:   Loss: 4.9585 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6130 - Tissue-MC-Acc.: 0.9394
2023-09-09 12:12:41,051 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 12:12:41,052 [INFO] - Epoch: 80/130
2023-09-09 12:14:56,473 [INFO] - Training epoch stats:     Loss: 4.7457 - Binary-Cell-Dice: 0.8278 - Binary-Cell-Jacard: 0.7508 - Tissue-MC-Acc.: 0.9936
2023-09-09 12:16:47,954 [INFO] - Validation epoch stats:   Loss: 4.9617 - Binary-Cell-Dice: 0.7971 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9421
2023-09-09 12:16:53,679 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 12:16:53,680 [INFO] - Epoch: 81/130
2023-09-09 12:19:10,590 [INFO] - Training epoch stats:     Loss: 4.7421 - Binary-Cell-Dice: 0.8236 - Binary-Cell-Jacard: 0.7525 - Tissue-MC-Acc.: 0.9951
2023-09-09 12:21:05,775 [INFO] - Validation epoch stats:   Loss: 4.9703 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6130 - Tissue-MC-Acc.: 0.9409
2023-09-09 12:21:11,761 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 12:21:11,761 [INFO] - Epoch: 82/130
2023-09-09 12:23:30,079 [INFO] - Training epoch stats:     Loss: 4.7532 - Binary-Cell-Dice: 0.8260 - Binary-Cell-Jacard: 0.7542 - Tissue-MC-Acc.: 0.9940
2023-09-09 12:25:19,789 [INFO] - Validation epoch stats:   Loss: 4.9734 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7206 - PQ-Score: 0.6116 - Tissue-MC-Acc.: 0.9437
2023-09-09 12:25:25,577 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-09 12:25:25,578 [INFO] - Epoch: 83/130
2023-09-09 12:27:41,096 [INFO] - Training epoch stats:     Loss: 4.7260 - Binary-Cell-Dice: 0.8333 - Binary-Cell-Jacard: 0.7593 - Tissue-MC-Acc.: 0.9932
2023-09-09 12:29:37,537 [INFO] - Validation epoch stats:   Loss: 4.9661 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7218 - PQ-Score: 0.6124 - Tissue-MC-Acc.: 0.9398
2023-09-09 12:29:52,176 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 12:29:52,177 [INFO] - Epoch: 84/130
2023-09-09 12:32:11,072 [INFO] - Training epoch stats:     Loss: 4.7452 - Binary-Cell-Dice: 0.8277 - Binary-Cell-Jacard: 0.7545 - Tissue-MC-Acc.: 0.9917
2023-09-09 12:33:59,829 [INFO] - Validation epoch stats:   Loss: 4.9651 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7230 - PQ-Score: 0.6138 - Tissue-MC-Acc.: 0.9402
2023-09-09 12:33:59,833 [INFO] - New best model - save checkpoint
2023-09-09 12:34:12,839 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 12:34:12,840 [INFO] - Epoch: 85/130
2023-09-09 12:36:30,725 [INFO] - Training epoch stats:     Loss: 4.7455 - Binary-Cell-Dice: 0.8329 - Binary-Cell-Jacard: 0.7572 - Tissue-MC-Acc.: 0.9962
2023-09-09 12:38:21,560 [INFO] - Validation epoch stats:   Loss: 4.9702 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6129 - Tissue-MC-Acc.: 0.9421
2023-09-09 12:38:35,196 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 12:38:35,197 [INFO] - Epoch: 86/130
2023-09-09 12:40:58,977 [INFO] - Training epoch stats:     Loss: 4.7374 - Binary-Cell-Dice: 0.8232 - Binary-Cell-Jacard: 0.7534 - Tissue-MC-Acc.: 0.9955
2023-09-09 12:42:50,998 [INFO] - Validation epoch stats:   Loss: 4.9688 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7230 - PQ-Score: 0.6141 - Tissue-MC-Acc.: 0.9437
2023-09-09 12:42:51,106 [INFO] - New best model - save checkpoint
2023-09-09 12:43:09,422 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 12:43:09,422 [INFO] - Epoch: 87/130
2023-09-09 12:45:29,363 [INFO] - Training epoch stats:     Loss: 4.7500 - Binary-Cell-Dice: 0.8293 - Binary-Cell-Jacard: 0.7516 - Tissue-MC-Acc.: 0.9932
2023-09-09 12:47:24,601 [INFO] - Validation epoch stats:   Loss: 4.9616 - Binary-Cell-Dice: 0.7964 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9445
2023-09-09 12:47:45,045 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-09 12:47:45,045 [INFO] - Epoch: 88/130
2023-09-09 12:50:04,649 [INFO] - Training epoch stats:     Loss: 4.7371 - Binary-Cell-Dice: 0.8273 - Binary-Cell-Jacard: 0.7558 - Tissue-MC-Acc.: 0.9962
2023-09-09 12:51:54,024 [INFO] - Validation epoch stats:   Loss: 4.9638 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7231 - PQ-Score: 0.6140 - Tissue-MC-Acc.: 0.9445
2023-09-09 12:52:03,281 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 12:52:03,281 [INFO] - Epoch: 89/130
2023-09-09 12:54:22,379 [INFO] - Training epoch stats:     Loss: 4.7111 - Binary-Cell-Dice: 0.8219 - Binary-Cell-Jacard: 0.7542 - Tissue-MC-Acc.: 0.9932
2023-09-09 12:56:11,941 [INFO] - Validation epoch stats:   Loss: 4.9625 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6125 - Tissue-MC-Acc.: 0.9429
2023-09-09 12:56:26,281 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 12:56:26,281 [INFO] - Epoch: 90/130
2023-09-09 12:58:46,177 [INFO] - Training epoch stats:     Loss: 4.7437 - Binary-Cell-Dice: 0.8301 - Binary-Cell-Jacard: 0.7556 - Tissue-MC-Acc.: 0.9970
2023-09-09 13:00:40,289 [INFO] - Validation epoch stats:   Loss: 4.9647 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9453
2023-09-09 13:00:45,943 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 13:00:45,944 [INFO] - Epoch: 91/130
2023-09-09 13:03:05,395 [INFO] - Training epoch stats:     Loss: 4.7063 - Binary-Cell-Dice: 0.8354 - Binary-Cell-Jacard: 0.7598 - Tissue-MC-Acc.: 0.9925
2023-09-09 13:04:52,389 [INFO] - Validation epoch stats:   Loss: 4.9611 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6137 - Tissue-MC-Acc.: 0.9417
2023-09-09 13:05:06,466 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 13:05:06,466 [INFO] - Epoch: 92/130
2023-09-09 13:07:23,388 [INFO] - Training epoch stats:     Loss: 4.7197 - Binary-Cell-Dice: 0.8257 - Binary-Cell-Jacard: 0.7560 - Tissue-MC-Acc.: 0.9936
2023-09-09 13:09:20,785 [INFO] - Validation epoch stats:   Loss: 4.9642 - Binary-Cell-Dice: 0.7961 - Binary-Cell-Jacard: 0.7217 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9437
2023-09-09 13:09:34,979 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 13:09:34,980 [INFO] - Epoch: 93/130
2023-09-09 13:11:55,686 [INFO] - Training epoch stats:     Loss: 4.7336 - Binary-Cell-Dice: 0.8286 - Binary-Cell-Jacard: 0.7571 - Tissue-MC-Acc.: 0.9974
2023-09-09 13:13:51,133 [INFO] - Validation epoch stats:   Loss: 4.9648 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6128 - Tissue-MC-Acc.: 0.9413
2023-09-09 13:14:07,139 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 13:14:07,140 [INFO] - Epoch: 94/130
2023-09-09 13:16:29,334 [INFO] - Training epoch stats:     Loss: 4.7292 - Binary-Cell-Dice: 0.8299 - Binary-Cell-Jacard: 0.7563 - Tissue-MC-Acc.: 0.9959
2023-09-09 13:18:21,678 [INFO] - Validation epoch stats:   Loss: 4.9613 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.9433
2023-09-09 13:18:21,687 [INFO] - New best model - save checkpoint
2023-09-09 13:18:42,236 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-09 13:18:42,237 [INFO] - Epoch: 95/130
2023-09-09 13:21:05,794 [INFO] - Training epoch stats:     Loss: 4.6997 - Binary-Cell-Dice: 0.8266 - Binary-Cell-Jacard: 0.7583 - Tissue-MC-Acc.: 0.9936
2023-09-09 13:23:02,588 [INFO] - Validation epoch stats:   Loss: 4.9638 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7222 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9441
2023-09-09 13:23:15,940 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:23:15,941 [INFO] - Epoch: 96/130
2023-09-09 13:25:34,637 [INFO] - Training epoch stats:     Loss: 4.7184 - Binary-Cell-Dice: 0.8343 - Binary-Cell-Jacard: 0.7590 - Tissue-MC-Acc.: 0.9951
2023-09-09 13:27:21,775 [INFO] - Validation epoch stats:   Loss: 4.9583 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9445
2023-09-09 13:27:38,511 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:27:38,512 [INFO] - Epoch: 97/130
2023-09-09 13:30:03,030 [INFO] - Training epoch stats:     Loss: 4.7319 - Binary-Cell-Dice: 0.8335 - Binary-Cell-Jacard: 0.7556 - Tissue-MC-Acc.: 0.9947
2023-09-09 13:31:56,022 [INFO] - Validation epoch stats:   Loss: 4.9576 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6144 - Tissue-MC-Acc.: 0.9429
2023-09-09 13:32:02,773 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:32:02,774 [INFO] - Epoch: 98/130
2023-09-09 13:34:18,769 [INFO] - Training epoch stats:     Loss: 4.7100 - Binary-Cell-Dice: 0.8289 - Binary-Cell-Jacard: 0.7569 - Tissue-MC-Acc.: 0.9951
2023-09-09 13:36:09,267 [INFO] - Validation epoch stats:   Loss: 4.9654 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7220 - PQ-Score: 0.6130 - Tissue-MC-Acc.: 0.9433
2023-09-09 13:36:19,621 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:36:19,621 [INFO] - Epoch: 99/130
2023-09-09 13:38:41,976 [INFO] - Training epoch stats:     Loss: 4.7252 - Binary-Cell-Dice: 0.8290 - Binary-Cell-Jacard: 0.7561 - Tissue-MC-Acc.: 0.9955
2023-09-09 13:40:41,161 [INFO] - Validation epoch stats:   Loss: 4.9600 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7218 - PQ-Score: 0.6135 - Tissue-MC-Acc.: 0.9429
2023-09-09 13:40:46,989 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:40:46,990 [INFO] - Epoch: 100/130
2023-09-09 13:43:04,775 [INFO] - Training epoch stats:     Loss: 4.7233 - Binary-Cell-Dice: 0.8327 - Binary-Cell-Jacard: 0.7608 - Tissue-MC-Acc.: 0.9932
2023-09-09 13:44:53,062 [INFO] - Validation epoch stats:   Loss: 4.9676 - Binary-Cell-Dice: 0.7959 - Binary-Cell-Jacard: 0.7210 - PQ-Score: 0.6124 - Tissue-MC-Acc.: 0.9453
2023-09-09 13:44:59,107 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:44:59,107 [INFO] - Epoch: 101/130
2023-09-09 13:47:17,381 [INFO] - Training epoch stats:     Loss: 4.7317 - Binary-Cell-Dice: 0.8302 - Binary-Cell-Jacard: 0.7543 - Tissue-MC-Acc.: 0.9940
2023-09-09 13:49:08,130 [INFO] - Validation epoch stats:   Loss: 4.9622 - Binary-Cell-Dice: 0.7968 - Binary-Cell-Jacard: 0.7220 - PQ-Score: 0.6139 - Tissue-MC-Acc.: 0.9437
2023-09-09 13:49:13,907 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:49:13,908 [INFO] - Epoch: 102/130
2023-09-09 13:51:37,804 [INFO] - Training epoch stats:     Loss: 4.7147 - Binary-Cell-Dice: 0.8239 - Binary-Cell-Jacard: 0.7585 - Tissue-MC-Acc.: 0.9955
2023-09-09 13:53:37,700 [INFO] - Validation epoch stats:   Loss: 4.9627 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6135 - Tissue-MC-Acc.: 0.9429
2023-09-09 13:53:53,488 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:53:53,489 [INFO] - Epoch: 103/130
2023-09-09 13:56:10,350 [INFO] - Training epoch stats:     Loss: 4.7178 - Binary-Cell-Dice: 0.8301 - Binary-Cell-Jacard: 0.7575 - Tissue-MC-Acc.: 0.9959
2023-09-09 13:58:02,386 [INFO] - Validation epoch stats:   Loss: 4.9631 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6145 - Tissue-MC-Acc.: 0.9445
2023-09-09 13:58:08,588 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 13:58:08,589 [INFO] - Epoch: 104/130
2023-09-09 14:00:26,593 [INFO] - Training epoch stats:     Loss: 4.7277 - Binary-Cell-Dice: 0.8289 - Binary-Cell-Jacard: 0.7537 - Tissue-MC-Acc.: 0.9962
2023-09-09 14:02:19,348 [INFO] - Validation epoch stats:   Loss: 4.9609 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6141 - Tissue-MC-Acc.: 0.9457
2023-09-09 14:02:47,954 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-09 14:02:47,955 [INFO] - Epoch: 105/130
2023-09-09 14:05:11,042 [INFO] - Training epoch stats:     Loss: 4.7201 - Binary-Cell-Dice: 0.8270 - Binary-Cell-Jacard: 0.7575 - Tissue-MC-Acc.: 0.9966
2023-09-09 14:07:01,749 [INFO] - Validation epoch stats:   Loss: 4.9574 - Binary-Cell-Dice: 0.7971 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6133 - Tissue-MC-Acc.: 0.9461
2023-09-09 14:07:07,811 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:07:07,811 [INFO] - Epoch: 106/130
2023-09-09 14:09:32,287 [INFO] - Training epoch stats:     Loss: 4.7116 - Binary-Cell-Dice: 0.8322 - Binary-Cell-Jacard: 0.7590 - Tissue-MC-Acc.: 0.9947
2023-09-09 14:11:29,583 [INFO] - Validation epoch stats:   Loss: 4.9661 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6133 - Tissue-MC-Acc.: 0.9453
2023-09-09 14:11:45,113 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:11:45,114 [INFO] - Epoch: 107/130
2023-09-09 14:14:05,484 [INFO] - Training epoch stats:     Loss: 4.6711 - Binary-Cell-Dice: 0.8300 - Binary-Cell-Jacard: 0.7594 - Tissue-MC-Acc.: 0.9962
2023-09-09 14:16:03,144 [INFO] - Validation epoch stats:   Loss: 4.9634 - Binary-Cell-Dice: 0.7970 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6128 - Tissue-MC-Acc.: 0.9449
2023-09-09 14:16:09,757 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:16:09,757 [INFO] - Epoch: 108/130
2023-09-09 14:18:32,756 [INFO] - Training epoch stats:     Loss: 4.7025 - Binary-Cell-Dice: 0.8294 - Binary-Cell-Jacard: 0.7579 - Tissue-MC-Acc.: 0.9951
2023-09-09 14:20:23,203 [INFO] - Validation epoch stats:   Loss: 4.9602 - Binary-Cell-Dice: 0.7974 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6139 - Tissue-MC-Acc.: 0.9453
2023-09-09 14:20:40,659 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:20:40,660 [INFO] - Epoch: 109/130
2023-09-09 14:22:56,955 [INFO] - Training epoch stats:     Loss: 4.7040 - Binary-Cell-Dice: 0.8287 - Binary-Cell-Jacard: 0.7545 - Tissue-MC-Acc.: 0.9947
2023-09-09 14:24:50,617 [INFO] - Validation epoch stats:   Loss: 4.9610 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7213 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9441
2023-09-09 14:24:59,310 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:24:59,310 [INFO] - Epoch: 110/130
2023-09-09 14:27:21,602 [INFO] - Training epoch stats:     Loss: 4.7146 - Binary-Cell-Dice: 0.8309 - Binary-Cell-Jacard: 0.7537 - Tissue-MC-Acc.: 0.9966
2023-09-09 14:29:20,103 [INFO] - Validation epoch stats:   Loss: 4.9628 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9437
2023-09-09 14:29:28,729 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:29:28,729 [INFO] - Epoch: 111/130
2023-09-09 14:31:42,671 [INFO] - Training epoch stats:     Loss: 4.7072 - Binary-Cell-Dice: 0.8273 - Binary-Cell-Jacard: 0.7583 - Tissue-MC-Acc.: 0.9970
2023-09-09 14:33:39,224 [INFO] - Validation epoch stats:   Loss: 4.9649 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6127 - Tissue-MC-Acc.: 0.9433
2023-09-09 14:33:50,611 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:33:50,611 [INFO] - Epoch: 112/130
2023-09-09 14:36:09,282 [INFO] - Training epoch stats:     Loss: 4.6917 - Binary-Cell-Dice: 0.8303 - Binary-Cell-Jacard: 0.7577 - Tissue-MC-Acc.: 0.9966
2023-09-09 14:38:04,589 [INFO] - Validation epoch stats:   Loss: 4.9590 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9441
2023-09-09 14:38:20,342 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:38:20,343 [INFO] - Epoch: 113/130
2023-09-09 14:40:41,326 [INFO] - Training epoch stats:     Loss: 4.7030 - Binary-Cell-Dice: 0.8317 - Binary-Cell-Jacard: 0.7604 - Tissue-MC-Acc.: 0.9966
2023-09-09 14:42:35,515 [INFO] - Validation epoch stats:   Loss: 4.9633 - Binary-Cell-Dice: 0.7964 - Binary-Cell-Jacard: 0.7221 - PQ-Score: 0.6129 - Tissue-MC-Acc.: 0.9437
2023-09-09 14:42:51,868 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:42:51,869 [INFO] - Epoch: 114/130
2023-09-09 14:45:16,514 [INFO] - Training epoch stats:     Loss: 4.6869 - Binary-Cell-Dice: 0.8261 - Binary-Cell-Jacard: 0.7572 - Tissue-MC-Acc.: 0.9951
2023-09-09 14:47:10,870 [INFO] - Validation epoch stats:   Loss: 4.9636 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7216 - PQ-Score: 0.6139 - Tissue-MC-Acc.: 0.9437
2023-09-09 14:47:16,880 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:47:16,881 [INFO] - Epoch: 115/130
2023-09-09 14:49:31,964 [INFO] - Training epoch stats:     Loss: 4.7195 - Binary-Cell-Dice: 0.8331 - Binary-Cell-Jacard: 0.7582 - Tissue-MC-Acc.: 0.9944
2023-09-09 14:51:20,232 [INFO] - Validation epoch stats:   Loss: 4.9628 - Binary-Cell-Dice: 0.7958 - Binary-Cell-Jacard: 0.7213 - PQ-Score: 0.6132 - Tissue-MC-Acc.: 0.9449
2023-09-09 14:51:32,290 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:51:32,291 [INFO] - Epoch: 116/130
2023-09-09 14:53:56,909 [INFO] - Training epoch stats:     Loss: 4.6935 - Binary-Cell-Dice: 0.8257 - Binary-Cell-Jacard: 0.7592 - Tissue-MC-Acc.: 0.9944
2023-09-09 14:55:55,376 [INFO] - Validation epoch stats:   Loss: 4.9626 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6138 - Tissue-MC-Acc.: 0.9433
2023-09-09 14:56:24,619 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 14:56:24,620 [INFO] - Epoch: 117/130
2023-09-09 14:58:46,196 [INFO] - Training epoch stats:     Loss: 4.6966 - Binary-Cell-Dice: 0.8348 - Binary-Cell-Jacard: 0.7567 - Tissue-MC-Acc.: 0.9951
2023-09-09 15:00:43,765 [INFO] - Validation epoch stats:   Loss: 4.9618 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7217 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9421
2023-09-09 15:00:58,678 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:00:58,678 [INFO] - Epoch: 118/130
2023-09-09 15:03:22,041 [INFO] - Training epoch stats:     Loss: 4.7102 - Binary-Cell-Dice: 0.8350 - Binary-Cell-Jacard: 0.7609 - Tissue-MC-Acc.: 0.9966
2023-09-09 15:05:18,588 [INFO] - Validation epoch stats:   Loss: 4.9632 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7222 - PQ-Score: 0.6137 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:05:31,654 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:05:31,655 [INFO] - Epoch: 119/130
2023-09-09 15:07:53,091 [INFO] - Training epoch stats:     Loss: 4.7067 - Binary-Cell-Dice: 0.8254 - Binary-Cell-Jacard: 0.7581 - Tissue-MC-Acc.: 0.9947
2023-09-09 15:09:47,187 [INFO] - Validation epoch stats:   Loss: 4.9599 - Binary-Cell-Dice: 0.7966 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6140 - Tissue-MC-Acc.: 0.9433
2023-09-09 15:10:03,710 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:10:03,710 [INFO] - Epoch: 120/130
2023-09-09 15:12:23,572 [INFO] - Training epoch stats:     Loss: 4.7044 - Binary-Cell-Dice: 0.8329 - Binary-Cell-Jacard: 0.7577 - Tissue-MC-Acc.: 0.9947
2023-09-09 15:14:13,393 [INFO] - Validation epoch stats:   Loss: 4.9627 - Binary-Cell-Dice: 0.7959 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6129 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:14:19,114 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:14:19,115 [INFO] - Epoch: 121/130
2023-09-09 15:16:43,779 [INFO] - Training epoch stats:     Loss: 4.6837 - Binary-Cell-Dice: 0.8286 - Binary-Cell-Jacard: 0.7585 - Tissue-MC-Acc.: 0.9959
2023-09-09 15:18:36,088 [INFO] - Validation epoch stats:   Loss: 4.9612 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9437
2023-09-09 15:18:51,273 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:18:51,274 [INFO] - Epoch: 122/130
2023-09-09 15:21:13,491 [INFO] - Training epoch stats:     Loss: 4.6959 - Binary-Cell-Dice: 0.8289 - Binary-Cell-Jacard: 0.7593 - Tissue-MC-Acc.: 0.9962
2023-09-09 15:23:15,124 [INFO] - Validation epoch stats:   Loss: 4.9622 - Binary-Cell-Dice: 0.7970 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6128 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:23:30,152 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:23:30,152 [INFO] - Epoch: 123/130
2023-09-09 15:25:57,916 [INFO] - Training epoch stats:     Loss: 4.6893 - Binary-Cell-Dice: 0.8258 - Binary-Cell-Jacard: 0.7544 - Tissue-MC-Acc.: 0.9944
2023-09-09 15:27:47,278 [INFO] - Validation epoch stats:   Loss: 4.9619 - Binary-Cell-Dice: 0.7968 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6133 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:28:01,645 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:28:01,645 [INFO] - Epoch: 124/130
2023-09-09 15:30:23,915 [INFO] - Training epoch stats:     Loss: 4.7044 - Binary-Cell-Dice: 0.8294 - Binary-Cell-Jacard: 0.7575 - Tissue-MC-Acc.: 0.9966
2023-09-09 15:32:22,753 [INFO] - Validation epoch stats:   Loss: 4.9608 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7221 - PQ-Score: 0.6140 - Tissue-MC-Acc.: 0.9437
2023-09-09 15:32:29,390 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 15:32:29,391 [INFO] - Epoch: 125/130
2023-09-09 15:34:49,919 [INFO] - Training epoch stats:     Loss: 4.7086 - Binary-Cell-Dice: 0.8300 - Binary-Cell-Jacard: 0.7586 - Tissue-MC-Acc.: 0.9959
2023-09-09 15:36:43,513 [INFO] - Validation epoch stats:   Loss: 4.9595 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9445
2023-09-09 15:37:01,377 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-09 15:37:01,378 [INFO] - Epoch: 126/130
2023-09-09 15:39:24,759 [INFO] - Training epoch stats:     Loss: 4.6982 - Binary-Cell-Dice: 0.8275 - Binary-Cell-Jacard: 0.7587 - Tissue-MC-Acc.: 0.9940
2023-09-09 15:41:18,417 [INFO] - Validation epoch stats:   Loss: 4.9581 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:41:34,256 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 15:41:34,257 [INFO] - Epoch: 127/130
2023-09-09 15:44:03,022 [INFO] - Training epoch stats:     Loss: 4.6760 - Binary-Cell-Dice: 0.8299 - Binary-Cell-Jacard: 0.7589 - Tissue-MC-Acc.: 0.9970
2023-09-09 15:45:49,850 [INFO] - Validation epoch stats:   Loss: 4.9641 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6132 - Tissue-MC-Acc.: 0.9433
2023-09-09 15:45:56,236 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 15:45:56,236 [INFO] - Epoch: 128/130
2023-09-09 15:48:15,656 [INFO] - Training epoch stats:     Loss: 4.7256 - Binary-Cell-Dice: 0.8281 - Binary-Cell-Jacard: 0.7623 - Tissue-MC-Acc.: 0.9921
2023-09-09 15:50:11,993 [INFO] - Validation epoch stats:   Loss: 4.9624 - Binary-Cell-Dice: 0.7968 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6140 - Tissue-MC-Acc.: 0.9445
2023-09-09 15:50:26,262 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 15:50:26,263 [INFO] - Epoch: 129/130
2023-09-09 15:52:47,603 [INFO] - Training epoch stats:     Loss: 4.7219 - Binary-Cell-Dice: 0.8230 - Binary-Cell-Jacard: 0.7554 - Tissue-MC-Acc.: 0.9959
2023-09-09 15:54:44,696 [INFO] - Validation epoch stats:   Loss: 4.9622 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6141 - Tissue-MC-Acc.: 0.9441
2023-09-09 15:54:59,363 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 15:54:59,363 [INFO] - Epoch: 130/130
2023-09-09 15:57:18,405 [INFO] - Training epoch stats:     Loss: 4.7133 - Binary-Cell-Dice: 0.8284 - Binary-Cell-Jacard: 0.7590 - Tissue-MC-Acc.: 0.9962
2023-09-09 15:59:07,556 [INFO] - Validation epoch stats:   Loss: 4.9599 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9437
2023-09-09 15:59:13,600 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 15:59:13,601 [INFO] -
