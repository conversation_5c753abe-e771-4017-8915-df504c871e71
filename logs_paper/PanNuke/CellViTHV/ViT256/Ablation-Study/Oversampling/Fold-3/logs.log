2023-09-10 05:31:21,072 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-10 05:31:21,163 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7ef45450f9a0>]
2023-09-10 05:31:21,164 [INFO] - Using GPU: cuda:0
2023-09-10 05:31:21,164 [INFO] - Using device: cuda:0
2023-09-10 05:31:21,165 [INFO] - Loss functions:
2023-09-10 05:31:21,165 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-10 05:31:22,120 [INFO] - Loaded CellVit256 model
2023-09-10 05:31:22,122 [INFO] -
Model: CellViT256(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
)
2023-09-10 05:31:23,194 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256                                    [1, 6, 256, 256]          --
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 2, 256, 256]          130
├─Sequential: 1-19                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 2, 256, 256]          130
├─Sequential: 1-28                            --                        (recursive)
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          479,544
├─Sequential: 1-21                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          319,744
├─Sequential: 1-23                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        131,200
├─Sequential: 1-25                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         32,832
├─Sequential: 1-27                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-76                      [1, 6, 256, 256]          390
===============================================================================================
Total params: 46,750,349
Trainable params: 25,084,685
Non-trainable params: 21,665,664
Total mult-adds (G): 132.89
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1672.49
Params size (MB): 186.70
Estimated Total Size (MB): 1859.98
===============================================================================================
2023-09-10 05:31:24,130 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-10 05:31:24,132 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-10 05:31:24,132 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-10 05:31:33,918 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-10 05:31:33,920 [INFO] - Unique-Weights: tensor([0.5339, 0.5660, 0.5673, 0.6560, 0.6651, 0.6822, 0.6835, 0.6972, 0.6984,
        0.7156, 0.7722, 0.7871, 0.8043, 0.8055, 0.8134, 0.8146, 0.8318, 0.8467,
        0.9033, 0.9205, 0.9217, 0.9367, 0.9604, 0.9629, 0.9664, 0.9682, 0.9853,
        0.9925, 0.9938, 0.9985, 1.0003, 1.0174, 1.0316, 1.0352, 1.0529, 1.0637,
        1.0650, 1.0691, 1.0700, 1.0703, 1.0825, 1.0869, 1.0875, 1.0924, 1.0930,
        1.0954, 1.1024, 1.1024, 1.1073, 1.1087, 1.1100, 1.1147, 1.1159, 1.1165,
        1.1178, 1.1181, 1.1190, 1.1196, 1.1202, 1.1245, 1.1251, 1.1259, 1.1263,
        1.1275, 1.1336, 1.1348, 1.1421, 1.1480, 1.1499, 1.1502, 1.1514, 1.1537,
        1.1580, 1.1592, 1.1669, 1.1799, 1.1812, 1.1848, 1.1911, 1.1923, 1.1987,
        1.2012, 1.2046, 1.2065, 1.2089, 1.2133, 1.2144, 1.2174, 1.2186, 1.2186,
        1.2198, 1.2235, 1.2352, 1.2358, 1.2364, 1.2370, 1.2401, 1.2407, 1.2413,
        1.2419, 1.2425, 1.2425, 1.2437, 1.2450, 1.2519, 1.2583, 1.2598, 1.2642,
        1.2661, 1.2664, 1.2676, 1.2685, 1.2740, 1.2742, 1.2746, 1.2754, 1.2759,
        1.2771, 1.2831, 1.2997, 1.3010, 1.3044, 1.3073, 1.3075, 1.3085, 1.3251,
        1.3295, 1.3306, 1.3337, 1.3407, 1.3419, 1.3482, 1.3542, 1.3560, 1.3563,
        1.3567, 1.3585, 1.3640, 1.3641, 1.3670, 1.3681, 1.3847, 1.3854, 1.3902,
        1.3908, 1.3921, 1.3933, 1.4018, 1.4159, 1.4194, 1.4237, 1.4310, 1.4480,
        1.4527, 1.4556, 1.4569, 1.4581, 1.4631, 1.4747, 1.4753, 1.4808, 1.4820,
        1.5046, 1.5050, 1.5059, 1.5137, 1.5180, 1.5292, 1.5392, 1.5578, 1.5793,
        1.5805, 1.5805, 1.6023, 1.6044, 1.6052, 1.6126, 1.6212, 1.7288, 1.7301,
        1.7540])
2023-09-10 05:31:33,920 [INFO] - Instantiate Trainer
2023-09-10 05:31:33,920 [INFO] - Calling Trainer Fit
2023-09-10 05:31:33,921 [INFO] - Starting training, total number of epochs: 130
2023-09-10 05:31:33,921 [INFO] - Epoch: 1/130
2023-09-10 05:33:14,403 [INFO] - Training epoch stats:     Loss: 8.2219 - Binary-Cell-Dice: 0.7068 - Binary-Cell-Jacard: 0.5822 - Tissue-MC-Acc.: 0.2509
2023-09-10 05:35:31,499 [INFO] - Validation epoch stats:   Loss: 6.7144 - Binary-Cell-Dice: 0.7564 - Binary-Cell-Jacard: 0.6511 - PQ-Score: 0.4992 - Tissue-MC-Acc.: 0.3805
2023-09-10 05:35:31,501 [INFO] - New best model - save checkpoint
2023-09-10 05:35:41,034 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-10 05:35:41,035 [INFO] - Epoch: 2/130
2023-09-10 05:37:46,041 [INFO] - Training epoch stats:     Loss: 6.1466 - Binary-Cell-Dice: 0.7733 - Binary-Cell-Jacard: 0.6636 - Tissue-MC-Acc.: 0.3622
2023-09-10 05:40:38,861 [INFO] - Validation epoch stats:   Loss: 5.9759 - Binary-Cell-Dice: 0.7694 - Binary-Cell-Jacard: 0.6660 - PQ-Score: 0.5209 - Tissue-MC-Acc.: 0.4336
2023-09-10 05:40:38,900 [INFO] - New best model - save checkpoint
2023-09-10 05:41:01,305 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-10 05:41:01,306 [INFO] - Epoch: 3/130
2023-09-10 05:43:00,464 [INFO] - Training epoch stats:     Loss: 5.7226 - Binary-Cell-Dice: 0.7811 - Binary-Cell-Jacard: 0.6801 - Tissue-MC-Acc.: 0.4104
2023-09-10 05:45:06,750 [INFO] - Validation epoch stats:   Loss: 5.7456 - Binary-Cell-Dice: 0.7730 - Binary-Cell-Jacard: 0.6768 - PQ-Score: 0.5481 - Tissue-MC-Acc.: 0.4411
2023-09-10 05:45:06,754 [INFO] - New best model - save checkpoint
2023-09-10 05:45:15,756 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-10 05:45:15,756 [INFO] - Epoch: 4/130
2023-09-10 05:47:06,621 [INFO] - Training epoch stats:     Loss: 5.5376 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.6924 - Tissue-MC-Acc.: 0.4140
2023-09-10 05:49:40,928 [INFO] - Validation epoch stats:   Loss: 5.6481 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6756 - PQ-Score: 0.5472 - Tissue-MC-Acc.: 0.4566
2023-09-10 05:49:49,388 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-10 05:49:49,388 [INFO] - Epoch: 5/130
2023-09-10 05:51:51,133 [INFO] - Training epoch stats:     Loss: 5.4303 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7003 - Tissue-MC-Acc.: 0.4409
2023-09-10 05:54:12,357 [INFO] - Validation epoch stats:   Loss: 5.6324 - Binary-Cell-Dice: 0.7797 - Binary-Cell-Jacard: 0.6849 - PQ-Score: 0.5626 - Tissue-MC-Acc.: 0.4645
2023-09-10 05:54:12,361 [INFO] - New best model - save checkpoint
2023-09-10 05:54:21,862 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-10 05:54:21,862 [INFO] - Epoch: 6/130
2023-09-10 05:56:14,250 [INFO] - Training epoch stats:     Loss: 5.3057 - Binary-Cell-Dice: 0.8028 - Binary-Cell-Jacard: 0.7073 - Tissue-MC-Acc.: 0.4493
2023-09-10 05:58:33,814 [INFO] - Validation epoch stats:   Loss: 5.6070 - Binary-Cell-Dice: 0.7801 - Binary-Cell-Jacard: 0.6884 - PQ-Score: 0.5644 - Tissue-MC-Acc.: 0.4784
2023-09-10 05:58:33,817 [INFO] - New best model - save checkpoint
2023-09-10 05:58:42,930 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-10 05:58:42,931 [INFO] - Epoch: 7/130
2023-09-10 06:00:39,242 [INFO] - Training epoch stats:     Loss: 5.1960 - Binary-Cell-Dice: 0.8098 - Binary-Cell-Jacard: 0.7162 - Tissue-MC-Acc.: 0.4699
2023-09-10 06:03:45,445 [INFO] - Validation epoch stats:   Loss: 5.5611 - Binary-Cell-Dice: 0.7813 - Binary-Cell-Jacard: 0.6904 - PQ-Score: 0.5648 - Tissue-MC-Acc.: 0.4875
2023-09-10 06:03:45,447 [INFO] - New best model - save checkpoint
2023-09-10 06:03:58,570 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-10 06:03:58,571 [INFO] - Epoch: 8/130
2023-09-10 06:06:01,312 [INFO] - Training epoch stats:     Loss: 5.0485 - Binary-Cell-Dice: 0.8159 - Binary-Cell-Jacard: 0.7277 - Tissue-MC-Acc.: 0.4758
2023-09-10 06:08:10,018 [INFO] - Validation epoch stats:   Loss: 5.5561 - Binary-Cell-Dice: 0.7826 - Binary-Cell-Jacard: 0.6897 - PQ-Score: 0.5696 - Tissue-MC-Acc.: 0.4978
2023-09-10 06:08:10,046 [INFO] - New best model - save checkpoint
2023-09-10 06:08:21,852 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-10 06:08:21,853 [INFO] - Epoch: 9/130
2023-09-10 06:10:14,095 [INFO] - Training epoch stats:     Loss: 4.9847 - Binary-Cell-Dice: 0.8162 - Binary-Cell-Jacard: 0.7327 - Tissue-MC-Acc.: 0.4985
2023-09-10 06:12:17,626 [INFO] - Validation epoch stats:   Loss: 5.5919 - Binary-Cell-Dice: 0.7804 - Binary-Cell-Jacard: 0.6912 - PQ-Score: 0.5645 - Tissue-MC-Acc.: 0.4994
2023-09-10 06:12:25,788 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-10 06:12:25,788 [INFO] - Epoch: 10/130
2023-09-10 06:14:13,076 [INFO] - Training epoch stats:     Loss: 4.9127 - Binary-Cell-Dice: 0.8164 - Binary-Cell-Jacard: 0.7334 - Tissue-MC-Acc.: 0.4879
2023-09-10 06:16:28,052 [INFO] - Validation epoch stats:   Loss: 5.5725 - Binary-Cell-Dice: 0.7812 - Binary-Cell-Jacard: 0.6922 - PQ-Score: 0.5708 - Tissue-MC-Acc.: 0.4978
2023-09-10 06:16:28,054 [INFO] - New best model - save checkpoint
2023-09-10 06:16:39,463 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-10 06:16:39,464 [INFO] - Epoch: 11/130
2023-09-10 06:18:21,095 [INFO] - Training epoch stats:     Loss: 4.8206 - Binary-Cell-Dice: 0.8223 - Binary-Cell-Jacard: 0.7443 - Tissue-MC-Acc.: 0.4916
2023-09-10 06:20:27,255 [INFO] - Validation epoch stats:   Loss: 5.5843 - Binary-Cell-Dice: 0.7794 - Binary-Cell-Jacard: 0.6906 - PQ-Score: 0.5695 - Tissue-MC-Acc.: 0.5137
2023-09-10 06:22:47,458 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-10 06:22:47,459 [INFO] - Epoch: 12/130
2023-09-10 06:24:57,308 [INFO] - Training epoch stats:     Loss: 4.6983 - Binary-Cell-Dice: 0.8335 - Binary-Cell-Jacard: 0.7549 - Tissue-MC-Acc.: 0.4827
2023-09-10 06:27:45,742 [INFO] - Validation epoch stats:   Loss: 5.6436 - Binary-Cell-Dice: 0.7802 - Binary-Cell-Jacard: 0.6886 - PQ-Score: 0.5696 - Tissue-MC-Acc.: 0.5077
2023-09-10 06:27:52,471 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-10 06:27:52,472 [INFO] - Epoch: 13/130
2023-09-10 06:29:56,729 [INFO] - Training epoch stats:     Loss: 4.5902 - Binary-Cell-Dice: 0.8450 - Binary-Cell-Jacard: 0.7689 - Tissue-MC-Acc.: 0.5169
2023-09-10 06:32:18,341 [INFO] - Validation epoch stats:   Loss: 5.5880 - Binary-Cell-Dice: 0.7765 - Binary-Cell-Jacard: 0.6823 - PQ-Score: 0.5657 - Tissue-MC-Acc.: 0.5149
2023-09-10 06:32:31,163 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-10 06:32:31,164 [INFO] - Epoch: 14/130
2023-09-10 06:34:24,215 [INFO] - Training epoch stats:     Loss: 4.5303 - Binary-Cell-Dice: 0.8394 - Binary-Cell-Jacard: 0.7721 - Tissue-MC-Acc.: 0.5228
2023-09-10 06:36:35,391 [INFO] - Validation epoch stats:   Loss: 5.6888 - Binary-Cell-Dice: 0.7773 - Binary-Cell-Jacard: 0.6836 - PQ-Score: 0.5638 - Tissue-MC-Acc.: 0.5240
2023-09-10 06:36:40,110 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-10 06:36:40,111 [INFO] - Epoch: 15/130
2023-09-10 06:39:04,511 [INFO] - Training epoch stats:     Loss: 4.4752 - Binary-Cell-Dice: 0.8413 - Binary-Cell-Jacard: 0.7728 - Tissue-MC-Acc.: 0.5073
2023-09-10 06:41:17,722 [INFO] - Validation epoch stats:   Loss: 5.6911 - Binary-Cell-Dice: 0.7760 - Binary-Cell-Jacard: 0.6869 - PQ-Score: 0.5624 - Tissue-MC-Acc.: 0.5200
2023-09-10 06:41:22,088 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-10 06:41:22,089 [INFO] - Epoch: 16/130
2023-09-10 06:43:27,993 [INFO] - Training epoch stats:     Loss: 4.4076 - Binary-Cell-Dice: 0.8525 - Binary-Cell-Jacard: 0.7852 - Tissue-MC-Acc.: 0.5176
2023-09-10 06:45:31,804 [INFO] - Validation epoch stats:   Loss: 5.6746 - Binary-Cell-Dice: 0.7770 - Binary-Cell-Jacard: 0.6837 - PQ-Score: 0.5625 - Tissue-MC-Acc.: 0.5117
2023-09-10 06:45:39,936 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-10 06:45:39,936 [INFO] - Epoch: 17/130
2023-09-10 06:47:27,203 [INFO] - Training epoch stats:     Loss: 4.3024 - Binary-Cell-Dice: 0.8543 - Binary-Cell-Jacard: 0.7900 - Tissue-MC-Acc.: 0.5239
2023-09-10 06:49:40,449 [INFO] - Validation epoch stats:   Loss: 5.6396 - Binary-Cell-Dice: 0.7743 - Binary-Cell-Jacard: 0.6755 - PQ-Score: 0.5620 - Tissue-MC-Acc.: 0.5335
2023-09-10 06:49:44,941 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-10 06:49:44,942 [INFO] - Epoch: 18/130
2023-09-10 06:51:30,411 [INFO] - Training epoch stats:     Loss: 4.2487 - Binary-Cell-Dice: 0.8595 - Binary-Cell-Jacard: 0.7985 - Tissue-MC-Acc.: 0.5136
2023-09-10 06:54:36,090 [INFO] - Validation epoch stats:   Loss: 5.7023 - Binary-Cell-Dice: 0.7727 - Binary-Cell-Jacard: 0.6807 - PQ-Score: 0.5583 - Tissue-MC-Acc.: 0.5272
2023-09-10 06:54:50,077 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-10 06:54:50,077 [INFO] - Epoch: 19/130
2023-09-10 06:57:06,575 [INFO] - Training epoch stats:     Loss: 4.1922 - Binary-Cell-Dice: 0.8634 - Binary-Cell-Jacard: 0.8033 - Tissue-MC-Acc.: 0.5206
2023-09-10 06:59:18,365 [INFO] - Validation epoch stats:   Loss: 5.7094 - Binary-Cell-Dice: 0.7750 - Binary-Cell-Jacard: 0.6743 - PQ-Score: 0.5636 - Tissue-MC-Acc.: 0.5272
2023-09-10 06:59:22,988 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-10 06:59:22,989 [INFO] - Epoch: 20/130
2023-09-10 07:01:56,219 [INFO] - Training epoch stats:     Loss: 4.1238 - Binary-Cell-Dice: 0.8674 - Binary-Cell-Jacard: 0.8091 - Tissue-MC-Acc.: 0.5272
2023-09-10 07:04:38,990 [INFO] - Validation epoch stats:   Loss: 5.6927 - Binary-Cell-Dice: 0.7750 - Binary-Cell-Jacard: 0.6743 - PQ-Score: 0.5619 - Tissue-MC-Acc.: 0.5355
2023-09-10 07:04:43,074 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-10 07:04:43,075 [INFO] - Epoch: 21/130
2023-09-10 07:06:27,304 [INFO] - Training epoch stats:     Loss: 4.0915 - Binary-Cell-Dice: 0.8693 - Binary-Cell-Jacard: 0.8104 - Tissue-MC-Acc.: 0.5408
2023-09-10 07:08:33,304 [INFO] - Validation epoch stats:   Loss: 5.7396 - Binary-Cell-Dice: 0.7731 - Binary-Cell-Jacard: 0.6775 - PQ-Score: 0.5592 - Tissue-MC-Acc.: 0.5295
2023-09-10 07:08:37,895 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-10 07:08:37,896 [INFO] - Epoch: 22/130
2023-09-10 07:10:35,806 [INFO] - Training epoch stats:     Loss: 4.0375 - Binary-Cell-Dice: 0.8699 - Binary-Cell-Jacard: 0.8186 - Tissue-MC-Acc.: 0.5419
2023-09-10 07:13:04,241 [INFO] - Validation epoch stats:   Loss: 5.7651 - Binary-Cell-Dice: 0.7706 - Binary-Cell-Jacard: 0.6783 - PQ-Score: 0.5619 - Tissue-MC-Acc.: 0.5347
2023-09-10 07:13:12,220 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-10 07:13:12,221 [INFO] - Epoch: 23/130
2023-09-10 07:15:03,912 [INFO] - Training epoch stats:     Loss: 3.9797 - Binary-Cell-Dice: 0.8725 - Binary-Cell-Jacard: 0.8217 - Tissue-MC-Acc.: 0.5327
2023-09-10 07:17:28,772 [INFO] - Validation epoch stats:   Loss: 5.7882 - Binary-Cell-Dice: 0.7718 - Binary-Cell-Jacard: 0.6794 - PQ-Score: 0.5607 - Tissue-MC-Acc.: 0.5339
2023-09-10 07:17:36,687 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-10 07:17:36,688 [INFO] - Epoch: 24/130
2023-09-10 07:19:33,939 [INFO] - Training epoch stats:     Loss: 3.9552 - Binary-Cell-Dice: 0.8784 - Binary-Cell-Jacard: 0.8295 - Tissue-MC-Acc.: 0.5434
2023-09-10 07:21:47,966 [INFO] - Validation epoch stats:   Loss: 5.8164 - Binary-Cell-Dice: 0.7719 - Binary-Cell-Jacard: 0.6770 - PQ-Score: 0.5561 - Tissue-MC-Acc.: 0.5406
2023-09-10 07:21:52,081 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-10 07:21:52,082 [INFO] - Epoch: 25/130
2023-09-10 07:23:32,121 [INFO] - Training epoch stats:     Loss: 3.9065 - Binary-Cell-Dice: 0.8745 - Binary-Cell-Jacard: 0.8284 - Tissue-MC-Acc.: 0.5261
2023-09-10 07:26:22,236 [INFO] - Validation epoch stats:   Loss: 5.8114 - Binary-Cell-Dice: 0.7726 - Binary-Cell-Jacard: 0.6769 - PQ-Score: 0.5589 - Tissue-MC-Acc.: 0.5307
2023-09-10 07:26:33,136 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-10 07:26:33,136 [INFO] - Epoch: 26/130
2023-09-10 07:28:42,461 [INFO] - Training epoch stats:     Loss: 4.2337 - Binary-Cell-Dice: 0.8600 - Binary-Cell-Jacard: 0.8012 - Tissue-MC-Acc.: 0.5753
2023-09-10 07:31:06,284 [INFO] - Validation epoch stats:   Loss: 5.7645 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6712 - PQ-Score: 0.5484 - Tissue-MC-Acc.: 0.6433
2023-09-10 07:31:17,169 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-10 07:31:17,169 [INFO] - Epoch: 27/130
2023-09-10 07:33:30,598 [INFO] - Training epoch stats:     Loss: 3.9517 - Binary-Cell-Dice: 0.8685 - Binary-Cell-Jacard: 0.8222 - Tissue-MC-Acc.: 0.7076
2023-09-10 07:35:44,323 [INFO] - Validation epoch stats:   Loss: 5.7684 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6802 - PQ-Score: 0.5626 - Tissue-MC-Acc.: 0.6944
2023-09-10 07:35:50,504 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-10 07:35:50,505 [INFO] - Epoch: 28/130
2023-09-10 07:37:41,266 [INFO] - Training epoch stats:     Loss: 3.8369 - Binary-Cell-Dice: 0.8808 - Binary-Cell-Jacard: 0.8316 - Tissue-MC-Acc.: 0.7902
2023-09-10 07:40:22,105 [INFO] - Validation epoch stats:   Loss: 5.7427 - Binary-Cell-Dice: 0.7695 - Binary-Cell-Jacard: 0.6767 - PQ-Score: 0.5576 - Tissue-MC-Acc.: 0.7194
2023-09-10 07:40:33,005 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-10 07:40:33,006 [INFO] - Epoch: 29/130
2023-09-10 07:42:36,786 [INFO] - Training epoch stats:     Loss: 3.7108 - Binary-Cell-Dice: 0.8858 - Binary-Cell-Jacard: 0.8408 - Tissue-MC-Acc.: 0.8538
2023-09-10 07:44:54,528 [INFO] - Validation epoch stats:   Loss: 5.7552 - Binary-Cell-Dice: 0.7729 - Binary-Cell-Jacard: 0.6785 - PQ-Score: 0.5613 - Tissue-MC-Acc.: 0.7880
2023-09-10 07:45:00,979 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-10 07:45:00,980 [INFO] - Epoch: 30/130
2023-09-10 07:47:00,962 [INFO] - Training epoch stats:     Loss: 3.6006 - Binary-Cell-Dice: 0.8892 - Binary-Cell-Jacard: 0.8469 - Tissue-MC-Acc.: 0.9210
2023-09-10 07:48:53,529 [INFO] - Validation epoch stats:   Loss: 5.7112 - Binary-Cell-Dice: 0.7671 - Binary-Cell-Jacard: 0.6745 - PQ-Score: 0.5592 - Tissue-MC-Acc.: 0.8399
2023-09-10 07:49:09,086 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-10 07:49:09,086 [INFO] - Epoch: 31/130
2023-09-10 07:51:04,337 [INFO] - Training epoch stats:     Loss: 3.5535 - Binary-Cell-Dice: 0.8895 - Binary-Cell-Jacard: 0.8526 - Tissue-MC-Acc.: 0.9438
2023-09-10 07:53:35,563 [INFO] - Validation epoch stats:   Loss: 5.7106 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6791 - PQ-Score: 0.5534 - Tissue-MC-Acc.: 0.8644
2023-09-10 07:53:41,925 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-10 07:53:41,925 [INFO] - Epoch: 32/130
2023-09-10 07:56:05,239 [INFO] - Training epoch stats:     Loss: 3.4841 - Binary-Cell-Dice: 0.8956 - Binary-Cell-Jacard: 0.8640 - Tissue-MC-Acc.: 0.9666
2023-09-10 07:58:26,075 [INFO] - Validation epoch stats:   Loss: 5.7857 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6790 - PQ-Score: 0.5605 - Tissue-MC-Acc.: 0.8827
2023-09-10 07:58:32,669 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-10 07:58:32,670 [INFO] - Epoch: 33/130
2023-09-10 08:00:52,954 [INFO] - Training epoch stats:     Loss: 3.4235 - Binary-Cell-Dice: 0.8985 - Binary-Cell-Jacard: 0.8693 - Tissue-MC-Acc.: 0.9816
2023-09-10 08:03:15,660 [INFO] - Validation epoch stats:   Loss: 5.7330 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6766 - PQ-Score: 0.5571 - Tissue-MC-Acc.: 0.8407
2023-09-10 08:03:22,490 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-10 08:03:22,491 [INFO] - Epoch: 34/130
2023-09-10 08:05:30,088 [INFO] - Training epoch stats:     Loss: 3.3720 - Binary-Cell-Dice: 0.9002 - Binary-Cell-Jacard: 0.8734 - Tissue-MC-Acc.: 0.9879
2023-09-10 08:07:29,888 [INFO] - Validation epoch stats:   Loss: 5.8523 - Binary-Cell-Dice: 0.7667 - Binary-Cell-Jacard: 0.6710 - PQ-Score: 0.5528 - Tissue-MC-Acc.: 0.8843
2023-09-10 08:07:36,120 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-10 08:07:36,120 [INFO] - Epoch: 35/130
2023-09-10 08:09:55,588 [INFO] - Training epoch stats:     Loss: 3.3406 - Binary-Cell-Dice: 0.9043 - Binary-Cell-Jacard: 0.8814 - Tissue-MC-Acc.: 0.9893
2023-09-10 08:12:22,756 [INFO] - Validation epoch stats:   Loss: 5.7923 - Binary-Cell-Dice: 0.7709 - Binary-Cell-Jacard: 0.6795 - PQ-Score: 0.5574 - Tissue-MC-Acc.: 0.8878
2023-09-10 08:12:34,814 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-10 08:12:34,814 [INFO] - Epoch: 36/130
2023-09-10 08:15:17,080 [INFO] - Training epoch stats:     Loss: 3.3045 - Binary-Cell-Dice: 0.9027 - Binary-Cell-Jacard: 0.8786 - Tissue-MC-Acc.: 0.9882
2023-09-10 08:17:34,683 [INFO] - Validation epoch stats:   Loss: 5.7806 - Binary-Cell-Dice: 0.7718 - Binary-Cell-Jacard: 0.6763 - PQ-Score: 0.5582 - Tissue-MC-Acc.: 0.8938
2023-09-10 08:17:48,707 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-10 08:17:48,708 [INFO] - Epoch: 37/130
2023-09-10 08:20:08,818 [INFO] - Training epoch stats:     Loss: 3.2753 - Binary-Cell-Dice: 0.9109 - Binary-Cell-Jacard: 0.8869 - Tissue-MC-Acc.: 0.9923
2023-09-10 08:22:08,062 [INFO] - Validation epoch stats:   Loss: 5.8310 - Binary-Cell-Dice: 0.7671 - Binary-Cell-Jacard: 0.6770 - PQ-Score: 0.5550 - Tissue-MC-Acc.: 0.9092
2023-09-10 08:22:14,802 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-10 08:22:14,803 [INFO] - Epoch: 38/130
2023-09-10 08:24:09,989 [INFO] - Training epoch stats:     Loss: 3.2191 - Binary-Cell-Dice: 0.9097 - Binary-Cell-Jacard: 0.8885 - Tissue-MC-Acc.: 0.9963
2023-09-10 08:26:24,081 [INFO] - Validation epoch stats:   Loss: 5.8540 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6757 - PQ-Score: 0.5512 - Tissue-MC-Acc.: 0.9239
2023-09-10 08:26:35,265 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-10 08:26:35,265 [INFO] - Epoch: 39/130
2023-09-10 08:28:45,419 [INFO] - Training epoch stats:     Loss: 3.2070 - Binary-Cell-Dice: 0.9159 - Binary-Cell-Jacard: 0.8973 - Tissue-MC-Acc.: 0.9989
2023-09-10 08:30:52,659 [INFO] - Validation epoch stats:   Loss: 5.8111 - Binary-Cell-Dice: 0.7663 - Binary-Cell-Jacard: 0.6745 - PQ-Score: 0.5525 - Tissue-MC-Acc.: 0.9199
2023-09-10 08:31:03,320 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-10 08:31:03,320 [INFO] - Epoch: 40/130
2023-09-10 08:33:22,598 [INFO] - Training epoch stats:     Loss: 3.1740 - Binary-Cell-Dice: 0.9172 - Binary-Cell-Jacard: 0.9014 - Tissue-MC-Acc.: 0.9956
2023-09-10 08:36:12,908 [INFO] - Validation epoch stats:   Loss: 5.8033 - Binary-Cell-Dice: 0.7702 - Binary-Cell-Jacard: 0.6760 - PQ-Score: 0.5547 - Tissue-MC-Acc.: 0.9136
2023-09-10 08:36:33,742 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-10 08:36:33,742 [INFO] - Epoch: 41/130
2023-09-10 08:38:45,671 [INFO] - Training epoch stats:     Loss: 3.1633 - Binary-Cell-Dice: 0.9195 - Binary-Cell-Jacard: 0.9018 - Tissue-MC-Acc.: 0.9952
2023-09-10 08:41:10,220 [INFO] - Validation epoch stats:   Loss: 5.8155 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6780 - PQ-Score: 0.5551 - Tissue-MC-Acc.: 0.9203
2023-09-10 08:41:16,662 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-10 08:41:16,663 [INFO] - Epoch: 42/130
2023-09-10 08:43:37,037 [INFO] - Training epoch stats:     Loss: 3.1406 - Binary-Cell-Dice: 0.9198 - Binary-Cell-Jacard: 0.9017 - Tissue-MC-Acc.: 0.9974
2023-09-10 08:46:31,881 [INFO] - Validation epoch stats:   Loss: 5.8317 - Binary-Cell-Dice: 0.7722 - Binary-Cell-Jacard: 0.6820 - PQ-Score: 0.5590 - Tissue-MC-Acc.: 0.9203
2023-09-10 08:46:44,756 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-10 08:46:44,756 [INFO] - Epoch: 43/130
2023-09-10 08:48:54,668 [INFO] - Training epoch stats:     Loss: 3.0875 - Binary-Cell-Dice: 0.9188 - Binary-Cell-Jacard: 0.9101 - Tissue-MC-Acc.: 0.9993
2023-09-10 08:51:19,734 [INFO] - Validation epoch stats:   Loss: 5.8543 - Binary-Cell-Dice: 0.7720 - Binary-Cell-Jacard: 0.6819 - PQ-Score: 0.5584 - Tissue-MC-Acc.: 0.9184
2023-09-10 08:51:30,928 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-10 08:51:30,929 [INFO] - Epoch: 44/130
2023-09-10 08:53:32,439 [INFO] - Training epoch stats:     Loss: 3.0910 - Binary-Cell-Dice: 0.9175 - Binary-Cell-Jacard: 0.9117 - Tissue-MC-Acc.: 0.9996
2023-09-10 08:55:59,179 [INFO] - Validation epoch stats:   Loss: 5.8610 - Binary-Cell-Dice: 0.7703 - Binary-Cell-Jacard: 0.6794 - PQ-Score: 0.5554 - Tissue-MC-Acc.: 0.9326
2023-09-10 08:56:10,203 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-10 08:56:10,204 [INFO] - Epoch: 45/130
2023-09-10 08:58:23,780 [INFO] - Training epoch stats:     Loss: 3.0776 - Binary-Cell-Dice: 0.9193 - Binary-Cell-Jacard: 0.9107 - Tissue-MC-Acc.: 0.9982
2023-09-10 09:00:38,116 [INFO] - Validation epoch stats:   Loss: 5.8629 - Binary-Cell-Dice: 0.7680 - Binary-Cell-Jacard: 0.6782 - PQ-Score: 0.5548 - Tissue-MC-Acc.: 0.9112
2023-09-10 09:00:47,100 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-10 09:00:47,101 [INFO] - Epoch: 46/130
2023-09-10 09:02:39,782 [INFO] - Training epoch stats:     Loss: 3.0633 - Binary-Cell-Dice: 0.9222 - Binary-Cell-Jacard: 0.9140 - Tissue-MC-Acc.: 1.0000
2023-09-10 09:05:25,036 [INFO] - Validation epoch stats:   Loss: 5.8793 - Binary-Cell-Dice: 0.7687 - Binary-Cell-Jacard: 0.6762 - PQ-Score: 0.5524 - Tissue-MC-Acc.: 0.9322
2023-09-10 09:05:36,369 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-10 09:05:36,370 [INFO] - Epoch: 47/130
2023-09-10 09:07:40,540 [INFO] - Training epoch stats:     Loss: 3.0336 - Binary-Cell-Dice: 0.9261 - Binary-Cell-Jacard: 0.9170 - Tissue-MC-Acc.: 0.9989
2023-09-10 09:09:38,596 [INFO] - Validation epoch stats:   Loss: 5.8482 - Binary-Cell-Dice: 0.7723 - Binary-Cell-Jacard: 0.6775 - PQ-Score: 0.5582 - Tissue-MC-Acc.: 0.9283
2023-09-10 09:09:44,620 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-10 09:09:44,621 [INFO] - Epoch: 48/130
2023-09-10 09:12:18,852 [INFO] - Training epoch stats:     Loss: 3.0256 - Binary-Cell-Dice: 0.9279 - Binary-Cell-Jacard: 0.9206 - Tissue-MC-Acc.: 1.0000
2023-09-10 09:15:05,495 [INFO] - Validation epoch stats:   Loss: 5.9480 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6739 - PQ-Score: 0.5509 - Tissue-MC-Acc.: 0.9342
2023-09-10 09:15:26,974 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-10 09:15:26,975 [INFO] - Epoch: 49/130
2023-09-10 09:17:57,821 [INFO] - Training epoch stats:     Loss: 2.9939 - Binary-Cell-Dice: 0.9293 - Binary-Cell-Jacard: 0.9211 - Tissue-MC-Acc.: 0.9996
2023-09-10 09:21:01,589 [INFO] - Validation epoch stats:   Loss: 5.8886 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6784 - PQ-Score: 0.5534 - Tissue-MC-Acc.: 0.9279
2023-09-10 09:21:08,012 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-10 09:21:08,013 [INFO] - Epoch: 50/130
2023-09-10 09:23:32,960 [INFO] - Training epoch stats:     Loss: 2.9914 - Binary-Cell-Dice: 0.9295 - Binary-Cell-Jacard: 0.9249 - Tissue-MC-Acc.: 1.0000
2023-09-10 09:25:38,458 [INFO] - Validation epoch stats:   Loss: 5.8851 - Binary-Cell-Dice: 0.7699 - Binary-Cell-Jacard: 0.6773 - PQ-Score: 0.5543 - Tissue-MC-Acc.: 0.9398
2023-09-10 09:25:45,072 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-10 09:25:45,072 [INFO] - Epoch: 51/130
2023-09-10 09:27:44,279 [INFO] - Training epoch stats:     Loss: 2.9762 - Binary-Cell-Dice: 0.9293 - Binary-Cell-Jacard: 0.9250 - Tissue-MC-Acc.: 1.0000
2023-09-10 09:30:27,310 [INFO] - Validation epoch stats:   Loss: 5.9000 - Binary-Cell-Dice: 0.7698 - Binary-Cell-Jacard: 0.6774 - PQ-Score: 0.5529 - Tissue-MC-Acc.: 0.9310
2023-09-10 09:30:33,332 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-10 09:30:33,333 [INFO] - Epoch: 52/130
2023-09-10 09:32:51,683 [INFO] - Training epoch stats:     Loss: 2.9792 - Binary-Cell-Dice: 0.9292 - Binary-Cell-Jacard: 0.9250 - Tissue-MC-Acc.: 0.9993
2023-09-10 09:34:44,912 [INFO] - Validation epoch stats:   Loss: 5.9321 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6733 - PQ-Score: 0.5484 - Tissue-MC-Acc.: 0.9382
2023-09-10 09:34:51,623 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-10 09:34:51,624 [INFO] - Epoch: 53/130
2023-09-10 09:36:50,736 [INFO] - Training epoch stats:     Loss: 2.9619 - Binary-Cell-Dice: 0.9285 - Binary-Cell-Jacard: 0.9277 - Tissue-MC-Acc.: 0.9996
2023-09-10 09:39:25,967 [INFO] - Validation epoch stats:   Loss: 5.9022 - Binary-Cell-Dice: 0.7705 - Binary-Cell-Jacard: 0.6790 - PQ-Score: 0.5553 - Tissue-MC-Acc.: 0.9350
2023-09-10 09:39:32,177 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-10 09:39:32,178 [INFO] - Epoch: 54/130
2023-09-10 09:41:33,141 [INFO] - Training epoch stats:     Loss: 2.9799 - Binary-Cell-Dice: 0.9280 - Binary-Cell-Jacard: 0.9292 - Tissue-MC-Acc.: 0.9993
2023-09-10 09:44:16,008 [INFO] - Validation epoch stats:   Loss: 5.9257 - Binary-Cell-Dice: 0.7709 - Binary-Cell-Jacard: 0.6790 - PQ-Score: 0.5567 - Tissue-MC-Acc.: 0.9306
2023-09-10 09:44:21,678 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-10 09:44:21,678 [INFO] - Epoch: 55/130
2023-09-10 09:46:27,146 [INFO] - Training epoch stats:     Loss: 2.9470 - Binary-Cell-Dice: 0.9358 - Binary-Cell-Jacard: 0.9325 - Tissue-MC-Acc.: 0.9996
2023-09-10 09:49:09,707 [INFO] - Validation epoch stats:   Loss: 5.9919 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6748 - PQ-Score: 0.5541 - Tissue-MC-Acc.: 0.9334
2023-09-10 09:49:21,274 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-10 09:49:21,275 [INFO] - Epoch: 56/130
2023-09-10 09:51:29,545 [INFO] - Training epoch stats:     Loss: 2.9456 - Binary-Cell-Dice: 0.9288 - Binary-Cell-Jacard: 0.9323 - Tissue-MC-Acc.: 0.9982
2023-09-10 09:53:58,386 [INFO] - Validation epoch stats:   Loss: 5.9130 - Binary-Cell-Dice: 0.7710 - Binary-Cell-Jacard: 0.6785 - PQ-Score: 0.5549 - Tissue-MC-Acc.: 0.9342
2023-09-10 09:54:04,959 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-10 09:54:04,960 [INFO] - Epoch: 57/130
2023-09-10 09:56:33,647 [INFO] - Training epoch stats:     Loss: 2.9295 - Binary-Cell-Dice: 0.9321 - Binary-Cell-Jacard: 0.9351 - Tissue-MC-Acc.: 0.9996
2023-09-10 09:59:08,948 [INFO] - Validation epoch stats:   Loss: 5.9534 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6739 - PQ-Score: 0.5519 - Tissue-MC-Acc.: 0.9366
2023-09-10 09:59:14,818 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-10 09:59:14,819 [INFO] - Epoch: 58/130
2023-09-10 10:01:06,963 [INFO] - Training epoch stats:     Loss: 2.9340 - Binary-Cell-Dice: 0.9321 - Binary-Cell-Jacard: 0.9354 - Tissue-MC-Acc.: 0.9996
2023-09-10 10:03:14,649 [INFO] - Validation epoch stats:   Loss: 5.9344 - Binary-Cell-Dice: 0.7695 - Binary-Cell-Jacard: 0.6768 - PQ-Score: 0.5525 - Tissue-MC-Acc.: 0.9354
2023-09-10 10:03:20,297 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-10 10:03:20,298 [INFO] - Epoch: 59/130
2023-09-10 10:05:14,493 [INFO] - Training epoch stats:     Loss: 2.9082 - Binary-Cell-Dice: 0.9383 - Binary-Cell-Jacard: 0.9359 - Tissue-MC-Acc.: 0.9993
2023-09-10 10:07:52,267 [INFO] - Validation epoch stats:   Loss: 5.9864 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6760 - PQ-Score: 0.5517 - Tissue-MC-Acc.: 0.9342
2023-09-10 10:07:57,769 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-10 10:07:57,770 [INFO] - Epoch: 60/130
2023-09-10 10:09:53,962 [INFO] - Training epoch stats:     Loss: 2.9019 - Binary-Cell-Dice: 0.9300 - Binary-Cell-Jacard: 0.9392 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:12:23,999 [INFO] - Validation epoch stats:   Loss: 5.9795 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6742 - PQ-Score: 0.5521 - Tissue-MC-Acc.: 0.9370
2023-09-10 10:12:34,239 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-10 10:12:34,239 [INFO] - Epoch: 61/130
2023-09-10 10:14:28,851 [INFO] - Training epoch stats:     Loss: 2.9133 - Binary-Cell-Dice: 0.9345 - Binary-Cell-Jacard: 0.9388 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:17:02,417 [INFO] - Validation epoch stats:   Loss: 6.0215 - Binary-Cell-Dice: 0.7656 - Binary-Cell-Jacard: 0.6732 - PQ-Score: 0.5462 - Tissue-MC-Acc.: 0.9390
2023-09-10 10:17:08,794 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-10 10:17:08,795 [INFO] - Epoch: 62/130
2023-09-10 10:19:13,844 [INFO] - Training epoch stats:     Loss: 2.8951 - Binary-Cell-Dice: 0.9340 - Binary-Cell-Jacard: 0.9364 - Tissue-MC-Acc.: 0.9993
2023-09-10 10:21:28,866 [INFO] - Validation epoch stats:   Loss: 6.0117 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6726 - PQ-Score: 0.5464 - Tissue-MC-Acc.: 0.9382
2023-09-10 10:21:40,808 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-10 10:21:40,809 [INFO] - Epoch: 63/130
2023-09-10 10:24:31,689 [INFO] - Training epoch stats:     Loss: 2.8842 - Binary-Cell-Dice: 0.9339 - Binary-Cell-Jacard: 0.9387 - Tissue-MC-Acc.: 0.9996
2023-09-10 10:28:27,462 [INFO] - Validation epoch stats:   Loss: 5.9688 - Binary-Cell-Dice: 0.7686 - Binary-Cell-Jacard: 0.6747 - PQ-Score: 0.5509 - Tissue-MC-Acc.: 0.9417
2023-09-10 10:28:33,352 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-10 10:28:33,353 [INFO] - Epoch: 64/130
2023-09-10 10:30:30,519 [INFO] - Training epoch stats:     Loss: 2.9026 - Binary-Cell-Dice: 0.9348 - Binary-Cell-Jacard: 0.9403 - Tissue-MC-Acc.: 0.9993
2023-09-10 10:32:58,831 [INFO] - Validation epoch stats:   Loss: 6.0264 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6743 - PQ-Score: 0.5485 - Tissue-MC-Acc.: 0.9413
2023-09-10 10:33:06,094 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-10 10:33:06,094 [INFO] - Epoch: 65/130
2023-09-10 10:35:10,850 [INFO] - Training epoch stats:     Loss: 2.8742 - Binary-Cell-Dice: 0.9390 - Binary-Cell-Jacard: 0.9419 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:37:44,511 [INFO] - Validation epoch stats:   Loss: 6.0330 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6729 - PQ-Score: 0.5485 - Tissue-MC-Acc.: 0.9433
2023-09-10 10:37:50,368 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-10 10:37:50,369 [INFO] - Epoch: 66/130
2023-09-10 10:40:26,597 [INFO] - Training epoch stats:     Loss: 2.8690 - Binary-Cell-Dice: 0.9375 - Binary-Cell-Jacard: 0.9428 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:42:45,205 [INFO] - Validation epoch stats:   Loss: 5.9971 - Binary-Cell-Dice: 0.7699 - Binary-Cell-Jacard: 0.6758 - PQ-Score: 0.5519 - Tissue-MC-Acc.: 0.9402
2023-09-10 10:42:56,514 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-10 10:42:56,515 [INFO] - Epoch: 67/130
2023-09-10 10:45:03,459 [INFO] - Training epoch stats:     Loss: 2.8748 - Binary-Cell-Dice: 0.9398 - Binary-Cell-Jacard: 0.9430 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:47:40,163 [INFO] - Validation epoch stats:   Loss: 6.0185 - Binary-Cell-Dice: 0.7689 - Binary-Cell-Jacard: 0.6743 - PQ-Score: 0.5526 - Tissue-MC-Acc.: 0.9417
2023-09-10 10:47:56,374 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-10 10:47:56,375 [INFO] - Epoch: 68/130
2023-09-10 10:49:57,859 [INFO] - Training epoch stats:     Loss: 2.8690 - Binary-Cell-Dice: 0.9309 - Binary-Cell-Jacard: 0.9416 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:52:09,897 [INFO] - Validation epoch stats:   Loss: 6.0188 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6719 - PQ-Score: 0.5483 - Tissue-MC-Acc.: 0.9409
2023-09-10 10:52:41,762 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-10 10:52:41,763 [INFO] - Epoch: 69/130
2023-09-10 10:54:56,222 [INFO] - Training epoch stats:     Loss: 2.8741 - Binary-Cell-Dice: 0.9404 - Binary-Cell-Jacard: 0.9434 - Tissue-MC-Acc.: 1.0000
2023-09-10 10:57:10,260 [INFO] - Validation epoch stats:   Loss: 6.0385 - Binary-Cell-Dice: 0.7665 - Binary-Cell-Jacard: 0.6726 - PQ-Score: 0.5490 - Tissue-MC-Acc.: 0.9409
2023-09-10 10:57:16,666 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-10 10:57:16,666 [INFO] - Epoch: 70/130
2023-09-10 10:59:18,785 [INFO] - Training epoch stats:     Loss: 2.8553 - Binary-Cell-Dice: 0.9437 - Binary-Cell-Jacard: 0.9432 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:01:32,212 [INFO] - Validation epoch stats:   Loss: 6.0441 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6727 - PQ-Score: 0.5503 - Tissue-MC-Acc.: 0.9405
2023-09-10 11:01:48,128 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-10 11:01:48,129 [INFO] - Epoch: 71/130
2023-09-10 11:03:48,885 [INFO] - Training epoch stats:     Loss: 2.8580 - Binary-Cell-Dice: 0.9409 - Binary-Cell-Jacard: 0.9438 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:05:51,263 [INFO] - Validation epoch stats:   Loss: 6.0332 - Binary-Cell-Dice: 0.7680 - Binary-Cell-Jacard: 0.6738 - PQ-Score: 0.5506 - Tissue-MC-Acc.: 0.9433
2023-09-10 11:05:57,160 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-10 11:05:57,161 [INFO] - Epoch: 72/130
2023-09-10 11:08:21,302 [INFO] - Training epoch stats:     Loss: 2.8450 - Binary-Cell-Dice: 0.9396 - Binary-Cell-Jacard: 0.9452 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:10:51,547 [INFO] - Validation epoch stats:   Loss: 6.0541 - Binary-Cell-Dice: 0.7672 - Binary-Cell-Jacard: 0.6729 - PQ-Score: 0.5475 - Tissue-MC-Acc.: 0.9417
2023-09-10 11:10:59,957 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-10 11:10:59,957 [INFO] - Epoch: 73/130
2023-09-10 11:13:15,231 [INFO] - Training epoch stats:     Loss: 2.8442 - Binary-Cell-Dice: 0.9441 - Binary-Cell-Jacard: 0.9470 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:15:34,182 [INFO] - Validation epoch stats:   Loss: 6.0415 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6747 - PQ-Score: 0.5496 - Tissue-MC-Acc.: 0.9413
2023-09-10 11:15:40,390 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-10 11:15:40,390 [INFO] - Epoch: 74/130
2023-09-10 11:17:37,017 [INFO] - Training epoch stats:     Loss: 2.8371 - Binary-Cell-Dice: 0.9350 - Binary-Cell-Jacard: 0.9465 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:20:04,463 [INFO] - Validation epoch stats:   Loss: 6.0616 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6730 - PQ-Score: 0.5483 - Tissue-MC-Acc.: 0.9378
2023-09-10 11:20:10,286 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-10 11:20:10,287 [INFO] - Epoch: 75/130
2023-09-10 11:22:19,490 [INFO] - Training epoch stats:     Loss: 2.8367 - Binary-Cell-Dice: 0.9418 - Binary-Cell-Jacard: 0.9460 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:24:53,789 [INFO] - Validation epoch stats:   Loss: 6.0283 - Binary-Cell-Dice: 0.7683 - Binary-Cell-Jacard: 0.6754 - PQ-Score: 0.5499 - Tissue-MC-Acc.: 0.9386
2023-09-10 11:25:05,718 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-10 11:25:05,719 [INFO] - Epoch: 76/130
2023-09-10 11:27:14,804 [INFO] - Training epoch stats:     Loss: 2.8274 - Binary-Cell-Dice: 0.9428 - Binary-Cell-Jacard: 0.9492 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:29:46,274 [INFO] - Validation epoch stats:   Loss: 6.0510 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6749 - PQ-Score: 0.5491 - Tissue-MC-Acc.: 0.9405
2023-09-10 11:29:52,952 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-10 11:29:52,953 [INFO] - Epoch: 77/130
2023-09-10 11:32:05,739 [INFO] - Training epoch stats:     Loss: 2.8429 - Binary-Cell-Dice: 0.9445 - Binary-Cell-Jacard: 0.9469 - Tissue-MC-Acc.: 0.9996
2023-09-10 11:34:46,502 [INFO] - Validation epoch stats:   Loss: 6.0584 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6725 - PQ-Score: 0.5468 - Tissue-MC-Acc.: 0.9362
2023-09-10 11:34:52,323 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-10 11:34:52,324 [INFO] - Epoch: 78/130
2023-09-10 11:37:12,038 [INFO] - Training epoch stats:     Loss: 2.8199 - Binary-Cell-Dice: 0.9402 - Binary-Cell-Jacard: 0.9501 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:39:51,233 [INFO] - Validation epoch stats:   Loss: 6.0676 - Binary-Cell-Dice: 0.7673 - Binary-Cell-Jacard: 0.6737 - PQ-Score: 0.5487 - Tissue-MC-Acc.: 0.9394
2023-09-10 11:40:02,612 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-10 11:40:02,613 [INFO] - Epoch: 79/130
2023-09-10 11:42:26,710 [INFO] - Training epoch stats:     Loss: 2.8232 - Binary-Cell-Dice: 0.9407 - Binary-Cell-Jacard: 0.9477 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:44:54,135 [INFO] - Validation epoch stats:   Loss: 6.0647 - Binary-Cell-Dice: 0.7682 - Binary-Cell-Jacard: 0.6747 - PQ-Score: 0.5502 - Tissue-MC-Acc.: 0.9417
2023-09-10 11:45:00,730 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 11:45:00,730 [INFO] - Epoch: 80/130
2023-09-10 11:46:54,171 [INFO] - Training epoch stats:     Loss: 2.8140 - Binary-Cell-Dice: 0.9422 - Binary-Cell-Jacard: 0.9490 - Tissue-MC-Acc.: 0.9993
2023-09-10 11:49:44,938 [INFO] - Validation epoch stats:   Loss: 6.0725 - Binary-Cell-Dice: 0.7674 - Binary-Cell-Jacard: 0.6742 - PQ-Score: 0.5486 - Tissue-MC-Acc.: 0.9413
2023-09-10 11:49:51,542 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 11:49:51,542 [INFO] - Epoch: 81/130
2023-09-10 11:52:14,340 [INFO] - Training epoch stats:     Loss: 2.8229 - Binary-Cell-Dice: 0.9456 - Binary-Cell-Jacard: 0.9491 - Tissue-MC-Acc.: 1.0000
2023-09-10 11:54:29,134 [INFO] - Validation epoch stats:   Loss: 6.1006 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6718 - PQ-Score: 0.5501 - Tissue-MC-Acc.: 0.9405
2023-09-10 11:54:35,023 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 11:54:35,024 [INFO] - Epoch: 82/130
2023-09-10 11:56:34,199 [INFO] - Training epoch stats:     Loss: 2.8099 - Binary-Cell-Dice: 0.9430 - Binary-Cell-Jacard: 0.9488 - Tissue-MC-Acc.: 0.9996
2023-09-10 11:58:50,828 [INFO] - Validation epoch stats:   Loss: 6.0474 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6737 - PQ-Score: 0.5496 - Tissue-MC-Acc.: 0.9433
2023-09-10 11:59:00,600 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-10 11:59:00,601 [INFO] - Epoch: 83/130
2023-09-10 12:01:19,961 [INFO] - Training epoch stats:     Loss: 2.8085 - Binary-Cell-Dice: 0.9371 - Binary-Cell-Jacard: 0.9503 - Tissue-MC-Acc.: 0.9993
2023-09-10 12:03:30,785 [INFO] - Validation epoch stats:   Loss: 6.0538 - Binary-Cell-Dice: 0.7669 - Binary-Cell-Jacard: 0.6750 - PQ-Score: 0.5514 - Tissue-MC-Acc.: 0.9421
2023-09-10 12:03:37,263 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 12:03:37,264 [INFO] - Epoch: 84/130
2023-09-10 12:06:01,213 [INFO] - Training epoch stats:     Loss: 2.7950 - Binary-Cell-Dice: 0.9366 - Binary-Cell-Jacard: 0.9496 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:08:05,332 [INFO] - Validation epoch stats:   Loss: 6.0785 - Binary-Cell-Dice: 0.7669 - Binary-Cell-Jacard: 0.6728 - PQ-Score: 0.5474 - Tissue-MC-Acc.: 0.9425
2023-09-10 12:08:11,484 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 12:08:11,485 [INFO] - Epoch: 85/130
2023-09-10 12:09:59,010 [INFO] - Training epoch stats:     Loss: 2.7958 - Binary-Cell-Dice: 0.9410 - Binary-Cell-Jacard: 0.9496 - Tissue-MC-Acc.: 0.9996
2023-09-10 12:12:23,017 [INFO] - Validation epoch stats:   Loss: 6.0956 - Binary-Cell-Dice: 0.7664 - Binary-Cell-Jacard: 0.6725 - PQ-Score: 0.5475 - Tissue-MC-Acc.: 0.9421
2023-09-10 12:12:35,617 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 12:12:35,618 [INFO] - Epoch: 86/130
2023-09-10 12:15:03,205 [INFO] - Training epoch stats:     Loss: 2.8077 - Binary-Cell-Dice: 0.9459 - Binary-Cell-Jacard: 0.9520 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:17:06,455 [INFO] - Validation epoch stats:   Loss: 6.0592 - Binary-Cell-Dice: 0.7680 - Binary-Cell-Jacard: 0.6745 - PQ-Score: 0.5507 - Tissue-MC-Acc.: 0.9413
2023-09-10 12:17:18,120 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 12:17:18,121 [INFO] - Epoch: 87/130
2023-09-10 12:19:26,462 [INFO] - Training epoch stats:     Loss: 2.7922 - Binary-Cell-Dice: 0.9461 - Binary-Cell-Jacard: 0.9510 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:22:50,283 [INFO] - Validation epoch stats:   Loss: 6.0552 - Binary-Cell-Dice: 0.7686 - Binary-Cell-Jacard: 0.6755 - PQ-Score: 0.5491 - Tissue-MC-Acc.: 0.9409
2023-09-10 12:22:56,577 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-10 12:22:56,577 [INFO] - Epoch: 88/130
2023-09-10 12:24:53,094 [INFO] - Training epoch stats:     Loss: 2.7921 - Binary-Cell-Dice: 0.9476 - Binary-Cell-Jacard: 0.9501 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:26:55,928 [INFO] - Validation epoch stats:   Loss: 6.0682 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6735 - PQ-Score: 0.5496 - Tissue-MC-Acc.: 0.9405
2023-09-10 12:27:02,018 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:27:02,019 [INFO] - Epoch: 89/130
2023-09-10 12:29:20,990 [INFO] - Training epoch stats:     Loss: 2.7970 - Binary-Cell-Dice: 0.9399 - Binary-Cell-Jacard: 0.9505 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:31:51,934 [INFO] - Validation epoch stats:   Loss: 6.0818 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6731 - PQ-Score: 0.5483 - Tissue-MC-Acc.: 0.9433
2023-09-10 12:31:57,874 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:31:57,875 [INFO] - Epoch: 90/130
2023-09-10 12:34:01,942 [INFO] - Training epoch stats:     Loss: 2.7916 - Binary-Cell-Dice: 0.9450 - Binary-Cell-Jacard: 0.9516 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:36:35,724 [INFO] - Validation epoch stats:   Loss: 6.0732 - Binary-Cell-Dice: 0.7689 - Binary-Cell-Jacard: 0.6751 - PQ-Score: 0.5498 - Tissue-MC-Acc.: 0.9445
2023-09-10 12:36:42,180 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:36:42,180 [INFO] - Epoch: 91/130
2023-09-10 12:38:41,382 [INFO] - Training epoch stats:     Loss: 2.7773 - Binary-Cell-Dice: 0.9391 - Binary-Cell-Jacard: 0.9515 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:41:12,964 [INFO] - Validation epoch stats:   Loss: 6.1051 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6724 - PQ-Score: 0.5480 - Tissue-MC-Acc.: 0.9441
2023-09-10 12:41:23,790 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:41:23,791 [INFO] - Epoch: 92/130
2023-09-10 12:43:29,240 [INFO] - Training epoch stats:     Loss: 2.7967 - Binary-Cell-Dice: 0.9422 - Binary-Cell-Jacard: 0.9519 - Tissue-MC-Acc.: 0.9996
2023-09-10 12:45:41,027 [INFO] - Validation epoch stats:   Loss: 6.0876 - Binary-Cell-Dice: 0.7663 - Binary-Cell-Jacard: 0.6721 - PQ-Score: 0.5479 - Tissue-MC-Acc.: 0.9417
2023-09-10 12:45:48,323 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:45:48,324 [INFO] - Epoch: 93/130
2023-09-10 12:48:38,392 [INFO] - Training epoch stats:     Loss: 2.7858 - Binary-Cell-Dice: 0.9451 - Binary-Cell-Jacard: 0.9521 - Tissue-MC-Acc.: 0.9996
2023-09-10 12:51:38,040 [INFO] - Validation epoch stats:   Loss: 6.0702 - Binary-Cell-Dice: 0.7673 - Binary-Cell-Jacard: 0.6748 - PQ-Score: 0.5500 - Tissue-MC-Acc.: 0.9425
2023-09-10 12:51:53,899 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 12:51:53,899 [INFO] - Epoch: 94/130
2023-09-10 12:53:54,618 [INFO] - Training epoch stats:     Loss: 2.7821 - Binary-Cell-Dice: 0.9417 - Binary-Cell-Jacard: 0.9531 - Tissue-MC-Acc.: 1.0000
2023-09-10 12:55:52,493 [INFO] - Validation epoch stats:   Loss: 6.0856 - Binary-Cell-Dice: 0.7683 - Binary-Cell-Jacard: 0.6739 - PQ-Score: 0.5489 - Tissue-MC-Acc.: 0.9421
2023-09-10 12:55:58,249 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-10 12:55:58,249 [INFO] - Epoch: 95/130
2023-09-10 12:57:53,085 [INFO] - Training epoch stats:     Loss: 2.7869 - Binary-Cell-Dice: 0.9432 - Binary-Cell-Jacard: 0.9540 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:00:04,460 [INFO] - Validation epoch stats:   Loss: 6.0775 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6747 - PQ-Score: 0.5503 - Tissue-MC-Acc.: 0.9413
2023-09-10 13:00:20,842 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:00:20,843 [INFO] - Epoch: 96/130
2023-09-10 13:02:33,986 [INFO] - Training epoch stats:     Loss: 2.7831 - Binary-Cell-Dice: 0.9414 - Binary-Cell-Jacard: 0.9521 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:04:49,949 [INFO] - Validation epoch stats:   Loss: 6.0911 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6734 - PQ-Score: 0.5481 - Tissue-MC-Acc.: 0.9413
2023-09-10 13:04:56,479 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:04:56,480 [INFO] - Epoch: 97/130
2023-09-10 13:06:42,881 [INFO] - Training epoch stats:     Loss: 2.7846 - Binary-Cell-Dice: 0.9465 - Binary-Cell-Jacard: 0.9526 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:09:05,742 [INFO] - Validation epoch stats:   Loss: 6.0967 - Binary-Cell-Dice: 0.7681 - Binary-Cell-Jacard: 0.6739 - PQ-Score: 0.5485 - Tissue-MC-Acc.: 0.9425
2023-09-10 13:09:11,368 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:09:11,369 [INFO] - Epoch: 98/130
2023-09-10 13:11:11,809 [INFO] - Training epoch stats:     Loss: 2.7737 - Binary-Cell-Dice: 0.9436 - Binary-Cell-Jacard: 0.9536 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:13:33,633 [INFO] - Validation epoch stats:   Loss: 6.0867 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6726 - PQ-Score: 0.5494 - Tissue-MC-Acc.: 0.9429
2023-09-10 13:13:40,221 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:13:40,222 [INFO] - Epoch: 99/130
2023-09-10 13:15:55,885 [INFO] - Training epoch stats:     Loss: 2.7793 - Binary-Cell-Dice: 0.9432 - Binary-Cell-Jacard: 0.9517 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:18:08,859 [INFO] - Validation epoch stats:   Loss: 6.0660 - Binary-Cell-Dice: 0.7683 - Binary-Cell-Jacard: 0.6747 - PQ-Score: 0.5510 - Tissue-MC-Acc.: 0.9421
2023-09-10 13:18:14,858 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:18:14,859 [INFO] - Epoch: 100/130
2023-09-10 13:20:13,696 [INFO] - Training epoch stats:     Loss: 2.7877 - Binary-Cell-Dice: 0.9498 - Binary-Cell-Jacard: 0.9526 - Tissue-MC-Acc.: 0.9996
2023-09-10 13:22:06,281 [INFO] - Validation epoch stats:   Loss: 6.1111 - Binary-Cell-Dice: 0.7669 - Binary-Cell-Jacard: 0.6717 - PQ-Score: 0.5479 - Tissue-MC-Acc.: 0.9409
2023-09-10 13:22:22,659 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:22:22,660 [INFO] - Epoch: 101/130
2023-09-10 13:25:27,774 [INFO] - Training epoch stats:     Loss: 2.7823 - Binary-Cell-Dice: 0.9434 - Binary-Cell-Jacard: 0.9529 - Tissue-MC-Acc.: 0.9996
2023-09-10 13:28:08,372 [INFO] - Validation epoch stats:   Loss: 6.0926 - Binary-Cell-Dice: 0.7673 - Binary-Cell-Jacard: 0.6722 - PQ-Score: 0.5483 - Tissue-MC-Acc.: 0.9425
2023-09-10 13:28:14,684 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:28:14,685 [INFO] - Epoch: 102/130
2023-09-10 13:30:11,676 [INFO] - Training epoch stats:     Loss: 2.7816 - Binary-Cell-Dice: 0.9430 - Binary-Cell-Jacard: 0.9546 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:32:53,112 [INFO] - Validation epoch stats:   Loss: 6.1077 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6726 - PQ-Score: 0.5489 - Tissue-MC-Acc.: 0.9417
2023-09-10 13:33:00,274 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:33:00,275 [INFO] - Epoch: 103/130
2023-09-10 13:34:47,943 [INFO] - Training epoch stats:     Loss: 2.7658 - Binary-Cell-Dice: 0.9395 - Binary-Cell-Jacard: 0.9538 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:36:56,068 [INFO] - Validation epoch stats:   Loss: 6.1319 - Binary-Cell-Dice: 0.7666 - Binary-Cell-Jacard: 0.6710 - PQ-Score: 0.5473 - Tissue-MC-Acc.: 0.9417
2023-09-10 13:37:08,391 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 13:37:08,391 [INFO] - Epoch: 104/130
2023-09-10 13:39:05,906 [INFO] - Training epoch stats:     Loss: 2.7736 - Binary-Cell-Dice: 0.9393 - Binary-Cell-Jacard: 0.9550 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:41:27,007 [INFO] - Validation epoch stats:   Loss: 6.1072 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6727 - PQ-Score: 0.5470 - Tissue-MC-Acc.: 0.9417
2023-09-10 13:41:42,932 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-10 13:41:42,933 [INFO] - Epoch: 105/130
2023-09-10 13:43:58,535 [INFO] - Training epoch stats:     Loss: 2.7819 - Binary-Cell-Dice: 0.9357 - Binary-Cell-Jacard: 0.9521 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:46:03,568 [INFO] - Validation epoch stats:   Loss: 6.1488 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6714 - PQ-Score: 0.5513 - Tissue-MC-Acc.: 0.9421
2023-09-10 13:46:08,980 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 13:46:08,981 [INFO] - Epoch: 106/130
2023-09-10 13:48:00,665 [INFO] - Training epoch stats:     Loss: 2.7729 - Binary-Cell-Dice: 0.9348 - Binary-Cell-Jacard: 0.9535 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:50:07,373 [INFO] - Validation epoch stats:   Loss: 6.0890 - Binary-Cell-Dice: 0.7674 - Binary-Cell-Jacard: 0.6736 - PQ-Score: 0.5482 - Tissue-MC-Acc.: 0.9425
2023-09-10 13:50:18,818 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 13:50:18,819 [INFO] - Epoch: 107/130
2023-09-10 13:53:00,691 [INFO] - Training epoch stats:     Loss: 2.7761 - Binary-Cell-Dice: 0.9439 - Binary-Cell-Jacard: 0.9518 - Tissue-MC-Acc.: 1.0000
2023-09-10 13:55:26,957 [INFO] - Validation epoch stats:   Loss: 6.0721 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6744 - PQ-Score: 0.5491 - Tissue-MC-Acc.: 0.9417
2023-09-10 13:55:32,655 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 13:55:32,656 [INFO] - Epoch: 108/130
2023-09-10 13:57:32,602 [INFO] - Training epoch stats:     Loss: 2.7638 - Binary-Cell-Dice: 0.9484 - Binary-Cell-Jacard: 0.9541 - Tissue-MC-Acc.: 0.9996
2023-09-10 13:59:41,742 [INFO] - Validation epoch stats:   Loss: 6.1053 - Binary-Cell-Dice: 0.7665 - Binary-Cell-Jacard: 0.6718 - PQ-Score: 0.5473 - Tissue-MC-Acc.: 0.9409
2023-09-10 14:00:11,979 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:00:11,979 [INFO] - Epoch: 109/130
2023-09-10 14:02:34,572 [INFO] - Training epoch stats:     Loss: 2.7656 - Binary-Cell-Dice: 0.9476 - Binary-Cell-Jacard: 0.9561 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:05:18,717 [INFO] - Validation epoch stats:   Loss: 6.1006 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6730 - PQ-Score: 0.5498 - Tissue-MC-Acc.: 0.9417
2023-09-10 14:05:25,321 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:05:25,321 [INFO] - Epoch: 110/130
2023-09-10 14:07:16,631 [INFO] - Training epoch stats:     Loss: 2.7730 - Binary-Cell-Dice: 0.9485 - Binary-Cell-Jacard: 0.9551 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:09:17,724 [INFO] - Validation epoch stats:   Loss: 6.0637 - Binary-Cell-Dice: 0.7681 - Binary-Cell-Jacard: 0.6753 - PQ-Score: 0.5512 - Tissue-MC-Acc.: 0.9405
2023-09-10 14:09:24,206 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:09:24,206 [INFO] - Epoch: 111/130
2023-09-10 14:11:38,161 [INFO] - Training epoch stats:     Loss: 2.7695 - Binary-Cell-Dice: 0.9451 - Binary-Cell-Jacard: 0.9540 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:14:04,921 [INFO] - Validation epoch stats:   Loss: 6.1125 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6717 - PQ-Score: 0.5475 - Tissue-MC-Acc.: 0.9421
2023-09-10 14:14:20,531 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:14:20,531 [INFO] - Epoch: 112/130
2023-09-10 14:16:25,405 [INFO] - Training epoch stats:     Loss: 2.7709 - Binary-Cell-Dice: 0.9417 - Binary-Cell-Jacard: 0.9547 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:18:34,568 [INFO] - Validation epoch stats:   Loss: 6.1325 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6722 - PQ-Score: 0.5494 - Tissue-MC-Acc.: 0.9429
2023-09-10 14:18:40,875 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:18:40,876 [INFO] - Epoch: 113/130
2023-09-10 14:20:41,105 [INFO] - Training epoch stats:     Loss: 2.7849 - Binary-Cell-Dice: 0.9411 - Binary-Cell-Jacard: 0.9539 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:23:24,953 [INFO] - Validation epoch stats:   Loss: 6.1346 - Binary-Cell-Dice: 0.7673 - Binary-Cell-Jacard: 0.6717 - PQ-Score: 0.5490 - Tissue-MC-Acc.: 0.9413
2023-09-10 14:23:40,537 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:23:40,538 [INFO] - Epoch: 114/130
2023-09-10 14:25:55,304 [INFO] - Training epoch stats:     Loss: 2.7706 - Binary-Cell-Dice: 0.9401 - Binary-Cell-Jacard: 0.9544 - Tissue-MC-Acc.: 0.9993
2023-09-10 14:28:04,326 [INFO] - Validation epoch stats:   Loss: 6.0966 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6722 - PQ-Score: 0.5488 - Tissue-MC-Acc.: 0.9421
2023-09-10 14:28:10,942 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:28:10,942 [INFO] - Epoch: 115/130
2023-09-10 14:30:20,611 [INFO] - Training epoch stats:     Loss: 2.7637 - Binary-Cell-Dice: 0.9434 - Binary-Cell-Jacard: 0.9538 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:33:14,966 [INFO] - Validation epoch stats:   Loss: 6.0952 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6726 - PQ-Score: 0.5503 - Tissue-MC-Acc.: 0.9405
2023-09-10 14:33:20,742 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:33:20,742 [INFO] - Epoch: 116/130
2023-09-10 14:35:18,967 [INFO] - Training epoch stats:     Loss: 2.7653 - Binary-Cell-Dice: 0.9478 - Binary-Cell-Jacard: 0.9565 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:37:35,627 [INFO] - Validation epoch stats:   Loss: 6.0844 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6742 - PQ-Score: 0.5490 - Tissue-MC-Acc.: 0.9413
2023-09-10 14:37:46,649 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:37:46,650 [INFO] - Epoch: 117/130
2023-09-10 14:39:48,627 [INFO] - Training epoch stats:     Loss: 2.7606 - Binary-Cell-Dice: 0.9429 - Binary-Cell-Jacard: 0.9550 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:41:47,900 [INFO] - Validation epoch stats:   Loss: 6.0856 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6739 - PQ-Score: 0.5482 - Tissue-MC-Acc.: 0.9405
2023-09-10 14:42:03,688 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:42:03,689 [INFO] - Epoch: 118/130
2023-09-10 14:44:21,470 [INFO] - Training epoch stats:     Loss: 2.7654 - Binary-Cell-Dice: 0.9430 - Binary-Cell-Jacard: 0.9522 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:46:48,112 [INFO] - Validation epoch stats:   Loss: 6.1147 - Binary-Cell-Dice: 0.7672 - Binary-Cell-Jacard: 0.6721 - PQ-Score: 0.5483 - Tissue-MC-Acc.: 0.9421
2023-09-10 14:46:54,296 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:46:54,296 [INFO] - Epoch: 119/130
2023-09-10 14:48:59,283 [INFO] - Training epoch stats:     Loss: 2.7604 - Binary-Cell-Dice: 0.9408 - Binary-Cell-Jacard: 0.9544 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:51:18,269 [INFO] - Validation epoch stats:   Loss: 6.1282 - Binary-Cell-Dice: 0.7667 - Binary-Cell-Jacard: 0.6717 - PQ-Score: 0.5474 - Tissue-MC-Acc.: 0.9413
2023-09-10 14:51:24,544 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:51:24,545 [INFO] - Epoch: 120/130
2023-09-10 14:53:34,520 [INFO] - Training epoch stats:     Loss: 2.7641 - Binary-Cell-Dice: 0.9367 - Binary-Cell-Jacard: 0.9538 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:55:56,198 [INFO] - Validation epoch stats:   Loss: 6.1133 - Binary-Cell-Dice: 0.7670 - Binary-Cell-Jacard: 0.6730 - PQ-Score: 0.5474 - Tissue-MC-Acc.: 0.9425
2023-09-10 14:56:03,530 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 14:56:03,530 [INFO] - Epoch: 121/130
2023-09-10 14:57:54,244 [INFO] - Training epoch stats:     Loss: 2.7661 - Binary-Cell-Dice: 0.9443 - Binary-Cell-Jacard: 0.9551 - Tissue-MC-Acc.: 1.0000
2023-09-10 14:59:53,788 [INFO] - Validation epoch stats:   Loss: 6.1043 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6737 - PQ-Score: 0.5493 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:00:09,471 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 15:00:09,472 [INFO] - Epoch: 122/130
2023-09-10 15:02:11,715 [INFO] - Training epoch stats:     Loss: 2.7634 - Binary-Cell-Dice: 0.9418 - Binary-Cell-Jacard: 0.9539 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:04:29,295 [INFO] - Validation epoch stats:   Loss: 6.1163 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6722 - PQ-Score: 0.5477 - Tissue-MC-Acc.: 0.9409
2023-09-10 15:04:40,322 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 15:04:40,323 [INFO] - Epoch: 123/130
2023-09-10 15:06:46,914 [INFO] - Training epoch stats:     Loss: 2.7721 - Binary-Cell-Dice: 0.9450 - Binary-Cell-Jacard: 0.9537 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:09:09,286 [INFO] - Validation epoch stats:   Loss: 6.1290 - Binary-Cell-Dice: 0.7677 - Binary-Cell-Jacard: 0.6714 - PQ-Score: 0.5497 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:09:15,204 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 15:09:15,205 [INFO] - Epoch: 124/130
2023-09-10 15:11:19,314 [INFO] - Training epoch stats:     Loss: 2.7736 - Binary-Cell-Dice: 0.9453 - Binary-Cell-Jacard: 0.9530 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:13:34,242 [INFO] - Validation epoch stats:   Loss: 6.0920 - Binary-Cell-Dice: 0.7681 - Binary-Cell-Jacard: 0.6728 - PQ-Score: 0.5500 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:13:46,242 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 15:13:46,243 [INFO] - Epoch: 125/130
2023-09-10 15:15:58,503 [INFO] - Training epoch stats:     Loss: 2.7600 - Binary-Cell-Dice: 0.9491 - Binary-Cell-Jacard: 0.9547 - Tissue-MC-Acc.: 0.9996
2023-09-10 15:18:19,624 [INFO] - Validation epoch stats:   Loss: 6.0895 - Binary-Cell-Dice: 0.7673 - Binary-Cell-Jacard: 0.6729 - PQ-Score: 0.5481 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:18:33,746 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-10 15:18:33,746 [INFO] - Epoch: 126/130
2023-09-10 15:20:31,710 [INFO] - Training epoch stats:     Loss: 2.7784 - Binary-Cell-Dice: 0.9423 - Binary-Cell-Jacard: 0.9538 - Tissue-MC-Acc.: 0.9996
2023-09-10 15:22:39,505 [INFO] - Validation epoch stats:   Loss: 6.1055 - Binary-Cell-Dice: 0.7676 - Binary-Cell-Jacard: 0.6728 - PQ-Score: 0.5496 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:22:57,404 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 15:22:57,404 [INFO] - Epoch: 127/130
2023-09-10 15:24:51,886 [INFO] - Training epoch stats:     Loss: 2.7620 - Binary-Cell-Dice: 0.9531 - Binary-Cell-Jacard: 0.9560 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:27:00,791 [INFO] - Validation epoch stats:   Loss: 6.1251 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6718 - PQ-Score: 0.5473 - Tissue-MC-Acc.: 0.9409
2023-09-10 15:27:16,837 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 15:27:16,838 [INFO] - Epoch: 128/130
2023-09-10 15:29:19,093 [INFO] - Training epoch stats:     Loss: 2.7702 - Binary-Cell-Dice: 0.9370 - Binary-Cell-Jacard: 0.9555 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:31:49,707 [INFO] - Validation epoch stats:   Loss: 6.1026 - Binary-Cell-Dice: 0.7678 - Binary-Cell-Jacard: 0.6729 - PQ-Score: 0.5486 - Tissue-MC-Acc.: 0.9417
2023-09-10 15:31:55,324 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 15:31:55,325 [INFO] - Epoch: 129/130
2023-09-10 15:34:11,221 [INFO] - Training epoch stats:     Loss: 2.7552 - Binary-Cell-Dice: 0.9447 - Binary-Cell-Jacard: 0.9544 - Tissue-MC-Acc.: 1.0000
2023-09-10 15:36:29,998 [INFO] - Validation epoch stats:   Loss: 6.1226 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6721 - PQ-Score: 0.5481 - Tissue-MC-Acc.: 0.9413
2023-09-10 15:36:41,983 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 15:36:41,984 [INFO] - Epoch: 130/130
2023-09-10 15:38:49,126 [INFO] - Training epoch stats:     Loss: 2.7553 - Binary-Cell-Dice: 0.9462 - Binary-Cell-Jacard: 0.9549 - Tissue-MC-Acc.: 0.9996
2023-09-10 15:41:10,538 [INFO] - Validation epoch stats:   Loss: 6.1283 - Binary-Cell-Dice: 0.7668 - Binary-Cell-Jacard: 0.6714 - PQ-Score: 0.5473 - Tissue-MC-Acc.: 0.9417
2023-09-10 15:41:17,070 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 15:41:17,071 [INFO] -
