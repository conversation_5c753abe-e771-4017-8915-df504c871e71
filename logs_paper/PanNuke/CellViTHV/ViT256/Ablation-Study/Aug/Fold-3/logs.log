2023-09-09 18:17:37,095 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-09 18:17:37,145 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7eedb4793460>]
2023-09-09 18:17:37,145 [INFO] - Using GPU: cuda:0
2023-09-09 18:17:37,145 [INFO] - Using device: cuda:0
2023-09-09 18:17:37,145 [INFO] - Loss functions:
2023-09-09 18:17:37,146 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-09 18:17:40,848 [INFO] - Loaded CellVit256 model
2023-09-09 18:17:40,850 [INFO] -
Model: CellViT256(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
)
2023-09-09 18:17:47,531 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256                                    [1, 6, 256, 256]          --
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 2, 256, 256]          130
├─Sequential: 1-19                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-19                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 2, 256, 256]          130
├─Sequential: 1-28                            --                        (recursive)
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          479,544
├─Sequential: 1-21                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          319,744
├─Sequential: 1-23                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        131,200
├─Sequential: 1-25                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         32,832
├─Sequential: 1-27                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-28                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-76                      [1, 6, 256, 256]          390
===============================================================================================
Total params: 46,750,349
Trainable params: 25,084,685
Non-trainable params: 21,665,664
Total mult-adds (G): 132.89
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1672.49
Params size (MB): 186.70
Estimated Total Size (MB): 1859.98
===============================================================================================
2023-09-09 18:17:48,270 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-09 18:17:48,271 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-09 18:17:48,271 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-09 18:18:20,355 [INFO] - Using RandomSampler
2023-09-09 18:18:20,356 [INFO] - Instantiate Trainer
2023-09-09 18:18:20,356 [INFO] - Calling Trainer Fit
2023-09-09 18:18:20,357 [INFO] - Starting training, total number of epochs: 130
2023-09-09 18:18:20,357 [INFO] - Epoch: 1/130
2023-09-09 18:20:33,677 [INFO] - Training epoch stats:     Loss: 8.2249 - Binary-Cell-Dice: 0.6836 - Binary-Cell-Jacard: 0.5588 - Tissue-MC-Acc.: 0.2994
2023-09-09 18:22:54,571 [INFO] - Validation epoch stats:   Loss: 6.6666 - Binary-Cell-Dice: 0.7100 - Binary-Cell-Jacard: 0.5881 - PQ-Score: 0.4287 - Tissue-MC-Acc.: 0.3813
2023-09-09 18:22:54,581 [INFO] - New best model - save checkpoint
2023-09-09 18:23:16,407 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-09 18:23:16,407 [INFO] - Epoch: 2/130
2023-09-09 18:25:46,482 [INFO] - Training epoch stats:     Loss: 6.4350 - Binary-Cell-Dice: 0.7424 - Binary-Cell-Jacard: 0.6311 - Tissue-MC-Acc.: 0.3916
2023-09-09 18:27:56,631 [INFO] - Validation epoch stats:   Loss: 6.0480 - Binary-Cell-Dice: 0.7555 - Binary-Cell-Jacard: 0.6550 - PQ-Score: 0.5204 - Tissue-MC-Acc.: 0.4138
2023-09-09 18:27:56,635 [INFO] - New best model - save checkpoint
2023-09-09 18:28:05,243 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-09 18:28:05,243 [INFO] - Epoch: 3/130
2023-09-09 18:30:31,082 [INFO] - Training epoch stats:     Loss: 6.0997 - Binary-Cell-Dice: 0.7485 - Binary-Cell-Jacard: 0.6414 - Tissue-MC-Acc.: 0.4309
2023-09-09 18:32:31,165 [INFO] - Validation epoch stats:   Loss: 5.7670 - Binary-Cell-Dice: 0.7632 - Binary-Cell-Jacard: 0.6702 - PQ-Score: 0.5361 - Tissue-MC-Acc.: 0.4499
2023-09-09 18:32:31,177 [INFO] - New best model - save checkpoint
2023-09-09 18:32:46,603 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-09 18:32:46,603 [INFO] - Epoch: 4/130
2023-09-09 18:35:08,111 [INFO] - Training epoch stats:     Loss: 5.9827 - Binary-Cell-Dice: 0.7549 - Binary-Cell-Jacard: 0.6495 - Tissue-MC-Acc.: 0.4423
2023-09-09 18:37:04,327 [INFO] - Validation epoch stats:   Loss: 5.7259 - Binary-Cell-Dice: 0.7703 - Binary-Cell-Jacard: 0.6795 - PQ-Score: 0.5505 - Tissue-MC-Acc.: 0.4558
2023-09-09 18:37:04,331 [INFO] - New best model - save checkpoint
2023-09-09 18:37:12,802 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-09 18:37:12,803 [INFO] - Epoch: 5/130
2023-09-09 18:39:40,731 [INFO] - Training epoch stats:     Loss: 5.9005 - Binary-Cell-Dice: 0.7597 - Binary-Cell-Jacard: 0.6579 - Tissue-MC-Acc.: 0.4544
2023-09-09 18:41:38,063 [INFO] - Validation epoch stats:   Loss: 5.6649 - Binary-Cell-Dice: 0.7736 - Binary-Cell-Jacard: 0.6802 - PQ-Score: 0.5524 - Tissue-MC-Acc.: 0.4756
2023-09-09 18:41:38,072 [INFO] - New best model - save checkpoint
2023-09-09 18:41:57,888 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-09 18:41:57,889 [INFO] - Epoch: 6/130
2023-09-09 18:44:25,786 [INFO] - Training epoch stats:     Loss: 5.8355 - Binary-Cell-Dice: 0.7609 - Binary-Cell-Jacard: 0.6611 - Tissue-MC-Acc.: 0.4651
2023-09-09 18:46:28,344 [INFO] - Validation epoch stats:   Loss: 5.6133 - Binary-Cell-Dice: 0.7799 - Binary-Cell-Jacard: 0.6875 - PQ-Score: 0.5630 - Tissue-MC-Acc.: 0.4899
2023-09-09 18:46:28,354 [INFO] - New best model - save checkpoint
2023-09-09 18:46:49,225 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-09 18:46:49,225 [INFO] - Epoch: 7/130
2023-09-09 18:49:16,844 [INFO] - Training epoch stats:     Loss: 5.7980 - Binary-Cell-Dice: 0.7624 - Binary-Cell-Jacard: 0.6636 - Tissue-MC-Acc.: 0.4702
2023-09-09 18:51:18,091 [INFO] - Validation epoch stats:   Loss: 5.5769 - Binary-Cell-Dice: 0.7819 - Binary-Cell-Jacard: 0.6902 - PQ-Score: 0.5635 - Tissue-MC-Acc.: 0.4812
2023-09-09 18:51:18,100 [INFO] - New best model - save checkpoint
2023-09-09 18:51:42,112 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-09 18:51:42,113 [INFO] - Epoch: 8/130
2023-09-09 18:54:12,182 [INFO] - Training epoch stats:     Loss: 5.7696 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6682 - Tissue-MC-Acc.: 0.4846
2023-09-09 18:56:10,920 [INFO] - Validation epoch stats:   Loss: 5.5201 - Binary-Cell-Dice: 0.7815 - Binary-Cell-Jacard: 0.6963 - PQ-Score: 0.5665 - Tissue-MC-Acc.: 0.4966
2023-09-09 18:56:10,928 [INFO] - New best model - save checkpoint
2023-09-09 18:56:31,093 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-09 18:56:31,094 [INFO] - Epoch: 9/130
2023-09-09 18:58:56,612 [INFO] - Training epoch stats:     Loss: 5.7420 - Binary-Cell-Dice: 0.7669 - Binary-Cell-Jacard: 0.6684 - Tissue-MC-Acc.: 0.4960
2023-09-09 19:00:56,934 [INFO] - Validation epoch stats:   Loss: 5.5673 - Binary-Cell-Dice: 0.7786 - Binary-Cell-Jacard: 0.6920 - PQ-Score: 0.5708 - Tissue-MC-Acc.: 0.5046
2023-09-09 19:00:56,936 [INFO] - New best model - save checkpoint
2023-09-09 19:01:10,602 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-09 19:01:10,603 [INFO] - Epoch: 10/130
2023-09-09 19:03:35,287 [INFO] - Training epoch stats:     Loss: 5.6970 - Binary-Cell-Dice: 0.7702 - Binary-Cell-Jacard: 0.6737 - Tissue-MC-Acc.: 0.4927
2023-09-09 19:05:22,512 [INFO] - Validation epoch stats:   Loss: 5.4775 - Binary-Cell-Dice: 0.7787 - Binary-Cell-Jacard: 0.6927 - PQ-Score: 0.5726 - Tissue-MC-Acc.: 0.5057
2023-09-09 19:05:22,591 [INFO] - New best model - save checkpoint
2023-09-09 19:05:52,641 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-09 19:05:52,642 [INFO] - Epoch: 11/130
2023-09-09 19:08:27,877 [INFO] - Training epoch stats:     Loss: 5.6579 - Binary-Cell-Dice: 0.7711 - Binary-Cell-Jacard: 0.6755 - Tissue-MC-Acc.: 0.5055
2023-09-09 19:10:26,464 [INFO] - Validation epoch stats:   Loss: 5.4573 - Binary-Cell-Dice: 0.7853 - Binary-Cell-Jacard: 0.7001 - PQ-Score: 0.5773 - Tissue-MC-Acc.: 0.5093
2023-09-09 19:10:26,475 [INFO] - New best model - save checkpoint
2023-09-09 19:10:45,654 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-09 19:10:45,655 [INFO] - Epoch: 12/130
2023-09-09 19:13:07,617 [INFO] - Training epoch stats:     Loss: 5.6549 - Binary-Cell-Dice: 0.7711 - Binary-Cell-Jacard: 0.6772 - Tissue-MC-Acc.: 0.4989
2023-09-09 19:15:05,567 [INFO] - Validation epoch stats:   Loss: 5.4221 - Binary-Cell-Dice: 0.7851 - Binary-Cell-Jacard: 0.6951 - PQ-Score: 0.5789 - Tissue-MC-Acc.: 0.5125
2023-09-09 19:15:05,577 [INFO] - New best model - save checkpoint
2023-09-09 19:15:24,088 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-09 19:15:24,088 [INFO] - Epoch: 13/130
2023-09-09 19:17:50,965 [INFO] - Training epoch stats:     Loss: 5.6144 - Binary-Cell-Dice: 0.7722 - Binary-Cell-Jacard: 0.6785 - Tissue-MC-Acc.: 0.5073
2023-09-09 19:20:01,971 [INFO] - Validation epoch stats:   Loss: 5.4287 - Binary-Cell-Dice: 0.7854 - Binary-Cell-Jacard: 0.7004 - PQ-Score: 0.5832 - Tissue-MC-Acc.: 0.5184
2023-09-09 19:20:01,980 [INFO] - New best model - save checkpoint
2023-09-09 19:20:22,524 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-09 19:20:22,525 [INFO] - Epoch: 14/130
2023-09-09 19:22:58,131 [INFO] - Training epoch stats:     Loss: 5.6017 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6817 - Tissue-MC-Acc.: 0.5107
2023-09-09 19:24:54,241 [INFO] - Validation epoch stats:   Loss: 5.3827 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6982 - PQ-Score: 0.5850 - Tissue-MC-Acc.: 0.5145
2023-09-09 19:24:54,251 [INFO] - New best model - save checkpoint
2023-09-09 19:25:03,465 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-09 19:25:03,466 [INFO] - Epoch: 15/130
2023-09-09 19:27:26,338 [INFO] - Training epoch stats:     Loss: 5.5735 - Binary-Cell-Dice: 0.7785 - Binary-Cell-Jacard: 0.6884 - Tissue-MC-Acc.: 0.5154
2023-09-09 19:29:24,119 [INFO] - Validation epoch stats:   Loss: 5.4230 - Binary-Cell-Dice: 0.7868 - Binary-Cell-Jacard: 0.6998 - PQ-Score: 0.5850 - Tissue-MC-Acc.: 0.5244
2023-09-09 19:29:24,128 [INFO] - New best model - save checkpoint
2023-09-09 19:29:43,080 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-09 19:29:43,081 [INFO] - Epoch: 16/130
2023-09-09 19:32:18,945 [INFO] - Training epoch stats:     Loss: 5.5636 - Binary-Cell-Dice: 0.7762 - Binary-Cell-Jacard: 0.6859 - Tissue-MC-Acc.: 0.5107
2023-09-09 19:34:15,573 [INFO] - Validation epoch stats:   Loss: 5.4075 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.7016 - PQ-Score: 0.5868 - Tissue-MC-Acc.: 0.5216
2023-09-09 19:34:15,577 [INFO] - New best model - save checkpoint
2023-09-09 19:34:24,528 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-09 19:34:24,529 [INFO] - Epoch: 17/130
2023-09-09 19:36:53,008 [INFO] - Training epoch stats:     Loss: 5.5429 - Binary-Cell-Dice: 0.7789 - Binary-Cell-Jacard: 0.6891 - Tissue-MC-Acc.: 0.5261
2023-09-09 19:39:05,202 [INFO] - Validation epoch stats:   Loss: 5.3485 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.7052 - PQ-Score: 0.5831 - Tissue-MC-Acc.: 0.5275
2023-09-09 19:39:18,394 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-09 19:39:18,395 [INFO] - Epoch: 18/130
2023-09-09 19:41:48,981 [INFO] - Training epoch stats:     Loss: 5.5293 - Binary-Cell-Dice: 0.7764 - Binary-Cell-Jacard: 0.6880 - Tissue-MC-Acc.: 0.5298
2023-09-09 19:43:46,715 [INFO] - Validation epoch stats:   Loss: 5.3796 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.7047 - PQ-Score: 0.5855 - Tissue-MC-Acc.: 0.5307
2023-09-09 19:43:56,337 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-09 19:43:56,338 [INFO] - Epoch: 19/130
2023-09-09 19:46:17,988 [INFO] - Training epoch stats:     Loss: 5.5198 - Binary-Cell-Dice: 0.7788 - Binary-Cell-Jacard: 0.6891 - Tissue-MC-Acc.: 0.5301
2023-09-09 19:48:14,486 [INFO] - Validation epoch stats:   Loss: 5.3357 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6996 - PQ-Score: 0.5815 - Tissue-MC-Acc.: 0.5382
2023-09-09 19:48:18,694 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-09 19:48:18,695 [INFO] - Epoch: 20/130
2023-09-09 19:50:38,700 [INFO] - Training epoch stats:     Loss: 5.4853 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6888 - Tissue-MC-Acc.: 0.5268
2023-09-09 19:53:00,449 [INFO] - Validation epoch stats:   Loss: 5.3099 - Binary-Cell-Dice: 0.7893 - Binary-Cell-Jacard: 0.7069 - PQ-Score: 0.5899 - Tissue-MC-Acc.: 0.5434
2023-09-09 19:53:00,459 [INFO] - New best model - save checkpoint
2023-09-09 19:53:20,016 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-09 19:53:20,017 [INFO] - Epoch: 21/130
2023-09-09 19:55:47,144 [INFO] - Training epoch stats:     Loss: 5.4895 - Binary-Cell-Dice: 0.7803 - Binary-Cell-Jacard: 0.6916 - Tissue-MC-Acc.: 0.5287
2023-09-09 19:57:46,526 [INFO] - Validation epoch stats:   Loss: 5.2945 - Binary-Cell-Dice: 0.7906 - Binary-Cell-Jacard: 0.7012 - PQ-Score: 0.5861 - Tissue-MC-Acc.: 0.5394
2023-09-09 19:57:50,837 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-09 19:57:50,837 [INFO] - Epoch: 22/130
2023-09-09 20:00:10,884 [INFO] - Training epoch stats:     Loss: 5.4692 - Binary-Cell-Dice: 0.7809 - Binary-Cell-Jacard: 0.6905 - Tissue-MC-Acc.: 0.5345
2023-09-09 20:02:07,260 [INFO] - Validation epoch stats:   Loss: 5.3067 - Binary-Cell-Dice: 0.7873 - Binary-Cell-Jacard: 0.7039 - PQ-Score: 0.5908 - Tissue-MC-Acc.: 0.5426
2023-09-09 20:02:07,269 [INFO] - New best model - save checkpoint
2023-09-09 20:02:28,058 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-09 20:02:28,059 [INFO] - Epoch: 23/130
2023-09-09 20:04:52,698 [INFO] - Training epoch stats:     Loss: 5.4520 - Binary-Cell-Dice: 0.7799 - Binary-Cell-Jacard: 0.6919 - Tissue-MC-Acc.: 0.5345
2023-09-09 20:07:05,146 [INFO] - Validation epoch stats:   Loss: 5.3434 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.7015 - PQ-Score: 0.5881 - Tissue-MC-Acc.: 0.5426
2023-09-09 20:07:10,284 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-09 20:07:10,284 [INFO] - Epoch: 24/130
2023-09-09 20:09:34,549 [INFO] - Training epoch stats:     Loss: 5.4599 - Binary-Cell-Dice: 0.7810 - Binary-Cell-Jacard: 0.6916 - Tissue-MC-Acc.: 0.5375
2023-09-09 20:11:33,950 [INFO] - Validation epoch stats:   Loss: 5.3033 - Binary-Cell-Dice: 0.7899 - Binary-Cell-Jacard: 0.7047 - PQ-Score: 0.5908 - Tissue-MC-Acc.: 0.5422
2023-09-09 20:11:33,958 [INFO] - New best model - save checkpoint
2023-09-09 20:11:55,599 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-09 20:11:55,599 [INFO] - Epoch: 25/130
2023-09-09 20:14:30,286 [INFO] - Training epoch stats:     Loss: 5.4396 - Binary-Cell-Dice: 0.7838 - Binary-Cell-Jacard: 0.6958 - Tissue-MC-Acc.: 0.5371
2023-09-09 20:16:30,437 [INFO] - Validation epoch stats:   Loss: 5.2911 - Binary-Cell-Dice: 0.7867 - Binary-Cell-Jacard: 0.7055 - PQ-Score: 0.5908 - Tissue-MC-Acc.: 0.5402
2023-09-09 20:16:35,687 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-09 20:16:35,688 [INFO] - Epoch: 26/130
2023-09-09 20:19:14,100 [INFO] - Training epoch stats:     Loss: 5.7259 - Binary-Cell-Dice: 0.7638 - Binary-Cell-Jacard: 0.6655 - Tissue-MC-Acc.: 0.5125
2023-09-09 20:21:14,989 [INFO] - Validation epoch stats:   Loss: 5.5697 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6866 - PQ-Score: 0.5688 - Tissue-MC-Acc.: 0.5359
2023-09-09 20:21:21,248 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-09 20:21:21,248 [INFO] - Epoch: 27/130
2023-09-09 20:24:00,402 [INFO] - Training epoch stats:     Loss: 5.5728 - Binary-Cell-Dice: 0.7734 - Binary-Cell-Jacard: 0.6794 - Tissue-MC-Acc.: 0.6176
2023-09-09 20:25:56,529 [INFO] - Validation epoch stats:   Loss: 5.3631 - Binary-Cell-Dice: 0.7814 - Binary-Cell-Jacard: 0.6964 - PQ-Score: 0.5798 - Tissue-MC-Acc.: 0.6250
2023-09-09 20:26:11,953 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-09 20:26:11,953 [INFO] - Epoch: 28/130
2023-09-09 20:28:51,811 [INFO] - Training epoch stats:     Loss: 5.5043 - Binary-Cell-Dice: 0.7738 - Binary-Cell-Jacard: 0.6824 - Tissue-MC-Acc.: 0.6580
2023-09-09 20:30:44,665 [INFO] - Validation epoch stats:   Loss: 5.5121 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6754 - PQ-Score: 0.5583 - Tissue-MC-Acc.: 0.6397
2023-09-09 20:31:01,265 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-09 20:31:01,266 [INFO] - Epoch: 29/130
2023-09-09 20:33:39,969 [INFO] - Training epoch stats:     Loss: 5.4553 - Binary-Cell-Dice: 0.7792 - Binary-Cell-Jacard: 0.6911 - Tissue-MC-Acc.: 0.6892
2023-09-09 20:35:40,101 [INFO] - Validation epoch stats:   Loss: 5.3666 - Binary-Cell-Dice: 0.7776 - Binary-Cell-Jacard: 0.6899 - PQ-Score: 0.5577 - Tissue-MC-Acc.: 0.6968
2023-09-09 20:36:06,656 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-09 20:36:06,656 [INFO] - Epoch: 30/130
2023-09-09 20:38:46,887 [INFO] - Training epoch stats:     Loss: 5.3819 - Binary-Cell-Dice: 0.7813 - Binary-Cell-Jacard: 0.6953 - Tissue-MC-Acc.: 0.7296
2023-09-09 20:40:40,684 [INFO] - Validation epoch stats:   Loss: 5.2897 - Binary-Cell-Dice: 0.7865 - Binary-Cell-Jacard: 0.7031 - PQ-Score: 0.5896 - Tissue-MC-Acc.: 0.7210
2023-09-09 20:40:47,392 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-09 20:40:47,392 [INFO] - Epoch: 31/130
2023-09-09 20:43:13,752 [INFO] - Training epoch stats:     Loss: 5.3727 - Binary-Cell-Dice: 0.7832 - Binary-Cell-Jacard: 0.6970 - Tissue-MC-Acc.: 0.7432
2023-09-09 20:45:08,118 [INFO] - Validation epoch stats:   Loss: 5.2281 - Binary-Cell-Dice: 0.7873 - Binary-Cell-Jacard: 0.7042 - PQ-Score: 0.5825 - Tissue-MC-Acc.: 0.7305
2023-09-09 20:45:19,942 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-09 20:45:19,942 [INFO] - Epoch: 32/130
2023-09-09 20:47:51,620 [INFO] - Training epoch stats:     Loss: 5.3238 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.6995 - Tissue-MC-Acc.: 0.7785
2023-09-09 20:49:50,316 [INFO] - Validation epoch stats:   Loss: 5.2400 - Binary-Cell-Dice: 0.7888 - Binary-Cell-Jacard: 0.7048 - PQ-Score: 0.5875 - Tissue-MC-Acc.: 0.7673
2023-09-09 20:49:57,127 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-09 20:49:57,127 [INFO] - Epoch: 33/130
2023-09-09 20:52:28,380 [INFO] - Training epoch stats:     Loss: 5.3254 - Binary-Cell-Dice: 0.7864 - Binary-Cell-Jacard: 0.7021 - Tissue-MC-Acc.: 0.8049
2023-09-09 20:54:35,570 [INFO] - Validation epoch stats:   Loss: 5.2004 - Binary-Cell-Dice: 0.7844 - Binary-Cell-Jacard: 0.7048 - PQ-Score: 0.5874 - Tissue-MC-Acc.: 0.7594
2023-09-09 20:54:48,012 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-09 20:54:48,013 [INFO] - Epoch: 34/130
2023-09-09 20:57:27,145 [INFO] - Training epoch stats:     Loss: 5.2614 - Binary-Cell-Dice: 0.7876 - Binary-Cell-Jacard: 0.7047 - Tissue-MC-Acc.: 0.8255
2023-09-09 20:59:23,619 [INFO] - Validation epoch stats:   Loss: 5.1227 - Binary-Cell-Dice: 0.7916 - Binary-Cell-Jacard: 0.7098 - PQ-Score: 0.5940 - Tissue-MC-Acc.: 0.8113
2023-09-09 20:59:23,622 [INFO] - New best model - save checkpoint
2023-09-09 20:59:35,967 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-09 20:59:35,968 [INFO] - Epoch: 35/130
2023-09-09 21:02:07,972 [INFO] - Training epoch stats:     Loss: 5.2498 - Binary-Cell-Dice: 0.7882 - Binary-Cell-Jacard: 0.7057 - Tissue-MC-Acc.: 0.8391
2023-09-09 21:04:33,375 [INFO] - Validation epoch stats:   Loss: 5.1320 - Binary-Cell-Dice: 0.7899 - Binary-Cell-Jacard: 0.7057 - PQ-Score: 0.5955 - Tissue-MC-Acc.: 0.8062
2023-09-09 21:04:33,377 [INFO] - New best model - save checkpoint
2023-09-09 21:04:57,102 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-09 21:04:57,103 [INFO] - Epoch: 36/130
2023-09-09 21:07:31,461 [INFO] - Training epoch stats:     Loss: 5.2207 - Binary-Cell-Dice: 0.7909 - Binary-Cell-Jacard: 0.7088 - Tissue-MC-Acc.: 0.8780
2023-09-09 21:09:26,367 [INFO] - Validation epoch stats:   Loss: 5.0934 - Binary-Cell-Dice: 0.7914 - Binary-Cell-Jacard: 0.7134 - PQ-Score: 0.5973 - Tissue-MC-Acc.: 0.8446
2023-09-09 21:09:26,377 [INFO] - New best model - save checkpoint
2023-09-09 21:09:54,304 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-09 21:09:54,304 [INFO] - Epoch: 37/130
2023-09-09 21:12:23,506 [INFO] - Training epoch stats:     Loss: 5.2008 - Binary-Cell-Dice: 0.7897 - Binary-Cell-Jacard: 0.7089 - Tissue-MC-Acc.: 0.8784
2023-09-09 21:14:15,496 [INFO] - Validation epoch stats:   Loss: 5.1248 - Binary-Cell-Dice: 0.7877 - Binary-Cell-Jacard: 0.7088 - PQ-Score: 0.5943 - Tissue-MC-Acc.: 0.8292
2023-09-09 21:14:21,898 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-09 21:14:21,899 [INFO] - Epoch: 38/130
2023-09-09 21:16:59,800 [INFO] - Training epoch stats:     Loss: 5.1651 - Binary-Cell-Dice: 0.7912 - Binary-Cell-Jacard: 0.7110 - Tissue-MC-Acc.: 0.8997
2023-09-09 21:18:58,434 [INFO] - Validation epoch stats:   Loss: 5.1060 - Binary-Cell-Dice: 0.7945 - Binary-Cell-Jacard: 0.7137 - PQ-Score: 0.6017 - Tissue-MC-Acc.: 0.8700
2023-09-09 21:18:58,443 [INFO] - New best model - save checkpoint
2023-09-09 21:19:24,975 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-09 21:19:24,976 [INFO] - Epoch: 39/130
2023-09-09 21:21:51,254 [INFO] - Training epoch stats:     Loss: 5.1540 - Binary-Cell-Dice: 0.7921 - Binary-Cell-Jacard: 0.7140 - Tissue-MC-Acc.: 0.9096
2023-09-09 21:23:44,563 [INFO] - Validation epoch stats:   Loss: 5.1005 - Binary-Cell-Dice: 0.7931 - Binary-Cell-Jacard: 0.7119 - PQ-Score: 0.5969 - Tissue-MC-Acc.: 0.8565
2023-09-09 21:23:50,331 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-09 21:23:50,332 [INFO] - Epoch: 40/130
2023-09-09 21:26:17,193 [INFO] - Training epoch stats:     Loss: 5.1231 - Binary-Cell-Dice: 0.7948 - Binary-Cell-Jacard: 0.7149 - Tissue-MC-Acc.: 0.9140
2023-09-09 21:28:10,400 [INFO] - Validation epoch stats:   Loss: 5.0649 - Binary-Cell-Dice: 0.7940 - Binary-Cell-Jacard: 0.7153 - PQ-Score: 0.6014 - Tissue-MC-Acc.: 0.8700
2023-09-09 21:28:21,875 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-09 21:28:21,876 [INFO] - Epoch: 41/130
2023-09-09 21:30:53,140 [INFO] - Training epoch stats:     Loss: 5.0943 - Binary-Cell-Dice: 0.7961 - Binary-Cell-Jacard: 0.7190 - Tissue-MC-Acc.: 0.9273
2023-09-09 21:32:48,515 [INFO] - Validation epoch stats:   Loss: 5.0554 - Binary-Cell-Dice: 0.7936 - Binary-Cell-Jacard: 0.7130 - PQ-Score: 0.5993 - Tissue-MC-Acc.: 0.8930
2023-09-09 21:33:02,325 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-09 21:33:02,325 [INFO] - Epoch: 42/130
2023-09-09 21:35:25,960 [INFO] - Training epoch stats:     Loss: 5.0954 - Binary-Cell-Dice: 0.7958 - Binary-Cell-Jacard: 0.7172 - Tissue-MC-Acc.: 0.9350
2023-09-09 21:37:21,462 [INFO] - Validation epoch stats:   Loss: 5.0499 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7158 - PQ-Score: 0.6024 - Tissue-MC-Acc.: 0.8966
2023-09-09 21:37:21,464 [INFO] - New best model - save checkpoint
2023-09-09 21:37:33,929 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-09 21:37:33,930 [INFO] - Epoch: 43/130
2023-09-09 21:40:00,589 [INFO] - Training epoch stats:     Loss: 5.0743 - Binary-Cell-Dice: 0.7954 - Binary-Cell-Jacard: 0.7185 - Tissue-MC-Acc.: 0.9467
2023-09-09 21:41:52,814 [INFO] - Validation epoch stats:   Loss: 5.0279 - Binary-Cell-Dice: 0.7936 - Binary-Cell-Jacard: 0.7159 - PQ-Score: 0.6007 - Tissue-MC-Acc.: 0.9029
2023-09-09 21:42:14,625 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-09 21:42:14,626 [INFO] - Epoch: 44/130
2023-09-09 21:44:42,758 [INFO] - Training epoch stats:     Loss: 5.0519 - Binary-Cell-Dice: 0.7984 - Binary-Cell-Jacard: 0.7226 - Tissue-MC-Acc.: 0.9515
2023-09-09 21:46:34,768 [INFO] - Validation epoch stats:   Loss: 5.0246 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7170 - PQ-Score: 0.6015 - Tissue-MC-Acc.: 0.9049
2023-09-09 21:46:52,267 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-09 21:46:52,268 [INFO] - Epoch: 45/130
2023-09-09 21:49:15,909 [INFO] - Training epoch stats:     Loss: 5.0461 - Binary-Cell-Dice: 0.7987 - Binary-Cell-Jacard: 0.7236 - Tissue-MC-Acc.: 0.9578
2023-09-09 21:51:07,949 [INFO] - Validation epoch stats:   Loss: 5.0317 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7178 - PQ-Score: 0.6064 - Tissue-MC-Acc.: 0.8966
2023-09-09 21:51:07,959 [INFO] - New best model - save checkpoint
2023-09-09 21:51:34,882 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-09 21:51:34,883 [INFO] - Epoch: 46/130
2023-09-09 21:54:01,879 [INFO] - Training epoch stats:     Loss: 5.0202 - Binary-Cell-Dice: 0.8001 - Binary-Cell-Jacard: 0.7244 - Tissue-MC-Acc.: 0.9600
2023-09-09 21:56:01,999 [INFO] - Validation epoch stats:   Loss: 5.0046 - Binary-Cell-Dice: 0.7943 - Binary-Cell-Jacard: 0.7177 - PQ-Score: 0.6023 - Tissue-MC-Acc.: 0.9164
2023-09-09 21:56:16,569 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-09 21:56:16,570 [INFO] - Epoch: 47/130
2023-09-09 21:58:42,825 [INFO] - Training epoch stats:     Loss: 5.0070 - Binary-Cell-Dice: 0.7996 - Binary-Cell-Jacard: 0.7231 - Tissue-MC-Acc.: 0.9614
2023-09-09 22:00:39,040 [INFO] - Validation epoch stats:   Loss: 5.0131 - Binary-Cell-Dice: 0.7935 - Binary-Cell-Jacard: 0.7172 - PQ-Score: 0.6011 - Tissue-MC-Acc.: 0.9156
2023-09-09 22:00:45,337 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-09 22:00:45,337 [INFO] - Epoch: 48/130
2023-09-09 22:03:07,158 [INFO] - Training epoch stats:     Loss: 4.9968 - Binary-Cell-Dice: 0.7996 - Binary-Cell-Jacard: 0.7241 - Tissue-MC-Acc.: 0.9702
2023-09-09 22:05:07,945 [INFO] - Validation epoch stats:   Loss: 5.0152 - Binary-Cell-Dice: 0.7968 - Binary-Cell-Jacard: 0.7191 - PQ-Score: 0.6092 - Tissue-MC-Acc.: 0.9306
2023-09-09 22:05:07,954 [INFO] - New best model - save checkpoint
2023-09-09 22:05:35,139 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-09 22:05:35,139 [INFO] - Epoch: 49/130
2023-09-09 22:08:03,149 [INFO] - Training epoch stats:     Loss: 4.9952 - Binary-Cell-Dice: 0.8018 - Binary-Cell-Jacard: 0.7250 - Tissue-MC-Acc.: 0.9680
2023-09-09 22:10:00,787 [INFO] - Validation epoch stats:   Loss: 4.9908 - Binary-Cell-Dice: 0.7952 - Binary-Cell-Jacard: 0.7182 - PQ-Score: 0.6052 - Tissue-MC-Acc.: 0.9231
2023-09-09 22:10:11,256 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-09 22:10:11,256 [INFO] - Epoch: 50/130
2023-09-09 22:12:38,687 [INFO] - Training epoch stats:     Loss: 4.9979 - Binary-Cell-Dice: 0.8021 - Binary-Cell-Jacard: 0.7279 - Tissue-MC-Acc.: 0.9695
2023-09-09 22:14:37,730 [INFO] - Validation epoch stats:   Loss: 4.9782 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7209 - PQ-Score: 0.6067 - Tissue-MC-Acc.: 0.9354
2023-09-09 22:14:53,944 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-09 22:14:53,945 [INFO] - Epoch: 51/130
2023-09-09 22:17:25,092 [INFO] - Training epoch stats:     Loss: 4.9768 - Binary-Cell-Dice: 0.8019 - Binary-Cell-Jacard: 0.7286 - Tissue-MC-Acc.: 0.9754
2023-09-09 22:19:23,939 [INFO] - Validation epoch stats:   Loss: 4.9763 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7199 - PQ-Score: 0.6079 - Tissue-MC-Acc.: 0.9342
2023-09-09 22:19:34,091 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-09 22:19:34,092 [INFO] - Epoch: 52/130
2023-09-09 22:21:58,570 [INFO] - Training epoch stats:     Loss: 4.9557 - Binary-Cell-Dice: 0.8028 - Binary-Cell-Jacard: 0.7315 - Tissue-MC-Acc.: 0.9750
2023-09-09 22:23:50,799 [INFO] - Validation epoch stats:   Loss: 4.9748 - Binary-Cell-Dice: 0.7952 - Binary-Cell-Jacard: 0.7196 - PQ-Score: 0.6079 - Tissue-MC-Acc.: 0.9318
2023-09-09 22:24:05,225 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-09 22:24:05,226 [INFO] - Epoch: 53/130
2023-09-09 22:26:40,212 [INFO] - Training epoch stats:     Loss: 4.9570 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7283 - Tissue-MC-Acc.: 0.9769
2023-09-09 22:28:41,359 [INFO] - Validation epoch stats:   Loss: 4.9806 - Binary-Cell-Dice: 0.7945 - Binary-Cell-Jacard: 0.7180 - PQ-Score: 0.6077 - Tissue-MC-Acc.: 0.9287
2023-09-09 22:28:56,566 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-09 22:28:56,567 [INFO] - Epoch: 54/130
2023-09-09 22:31:27,245 [INFO] - Training epoch stats:     Loss: 4.9431 - Binary-Cell-Dice: 0.8056 - Binary-Cell-Jacard: 0.7340 - Tissue-MC-Acc.: 0.9780
2023-09-09 22:33:24,117 [INFO] - Validation epoch stats:   Loss: 4.9830 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7207 - PQ-Score: 0.6079 - Tissue-MC-Acc.: 0.9386
2023-09-09 22:33:30,776 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-09 22:33:30,777 [INFO] - Epoch: 55/130
2023-09-09 22:35:55,500 [INFO] - Training epoch stats:     Loss: 4.9520 - Binary-Cell-Dice: 0.8043 - Binary-Cell-Jacard: 0.7301 - Tissue-MC-Acc.: 0.9772
2023-09-09 22:38:03,111 [INFO] - Validation epoch stats:   Loss: 4.9785 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7199 - PQ-Score: 0.6096 - Tissue-MC-Acc.: 0.9433
2023-09-09 22:38:03,122 [INFO] - New best model - save checkpoint
2023-09-09 22:38:31,019 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-09 22:38:31,020 [INFO] - Epoch: 56/130
2023-09-09 22:41:00,677 [INFO] - Training epoch stats:     Loss: 4.9399 - Binary-Cell-Dice: 0.8037 - Binary-Cell-Jacard: 0.7328 - Tissue-MC-Acc.: 0.9827
2023-09-09 22:42:54,433 [INFO] - Validation epoch stats:   Loss: 4.9725 - Binary-Cell-Dice: 0.7939 - Binary-Cell-Jacard: 0.7177 - PQ-Score: 0.6089 - Tissue-MC-Acc.: 0.9453
2023-09-09 22:43:01,288 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-09 22:43:01,288 [INFO] - Epoch: 57/130
2023-09-09 22:45:29,367 [INFO] - Training epoch stats:     Loss: 4.9280 - Binary-Cell-Dice: 0.8069 - Binary-Cell-Jacard: 0.7342 - Tissue-MC-Acc.: 0.9835
2023-09-09 22:47:30,935 [INFO] - Validation epoch stats:   Loss: 4.9696 - Binary-Cell-Dice: 0.7984 - Binary-Cell-Jacard: 0.7220 - PQ-Score: 0.6086 - Tissue-MC-Acc.: 0.9366
2023-09-09 22:47:43,135 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-09 22:47:43,136 [INFO] - Epoch: 58/130
2023-09-09 22:50:08,193 [INFO] - Training epoch stats:     Loss: 4.9181 - Binary-Cell-Dice: 0.8060 - Binary-Cell-Jacard: 0.7349 - Tissue-MC-Acc.: 0.9849
2023-09-09 22:52:05,723 [INFO] - Validation epoch stats:   Loss: 4.9627 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7195 - PQ-Score: 0.6079 - Tissue-MC-Acc.: 0.9417
2023-09-09 22:52:12,108 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-09 22:52:12,108 [INFO] - Epoch: 59/130
2023-09-09 22:54:34,565 [INFO] - Training epoch stats:     Loss: 4.9175 - Binary-Cell-Dice: 0.8073 - Binary-Cell-Jacard: 0.7361 - Tissue-MC-Acc.: 0.9860
2023-09-09 22:56:34,108 [INFO] - Validation epoch stats:   Loss: 4.9674 - Binary-Cell-Dice: 0.7967 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6067 - Tissue-MC-Acc.: 0.9528
2023-09-09 22:56:46,352 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-09 22:56:46,353 [INFO] - Epoch: 60/130
2023-09-09 22:59:24,802 [INFO] - Training epoch stats:     Loss: 4.9074 - Binary-Cell-Dice: 0.8072 - Binary-Cell-Jacard: 0.7357 - Tissue-MC-Acc.: 0.9893
2023-09-09 23:01:21,071 [INFO] - Validation epoch stats:   Loss: 4.9539 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7217 - PQ-Score: 0.6120 - Tissue-MC-Acc.: 0.9461
2023-09-09 23:01:21,074 [INFO] - New best model - save checkpoint
2023-09-09 23:01:35,193 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-09 23:01:35,194 [INFO] - Epoch: 61/130
2023-09-09 23:04:09,686 [INFO] - Training epoch stats:     Loss: 4.8880 - Binary-Cell-Dice: 0.8095 - Binary-Cell-Jacard: 0.7364 - Tissue-MC-Acc.: 0.9882
2023-09-09 23:06:07,683 [INFO] - Validation epoch stats:   Loss: 4.9619 - Binary-Cell-Dice: 0.7970 - Binary-Cell-Jacard: 0.7208 - PQ-Score: 0.6109 - Tissue-MC-Acc.: 0.9524
2023-09-09 23:06:13,402 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-09 23:06:13,402 [INFO] - Epoch: 62/130
2023-09-09 23:08:45,362 [INFO] - Training epoch stats:     Loss: 4.8940 - Binary-Cell-Dice: 0.8060 - Binary-Cell-Jacard: 0.7359 - Tissue-MC-Acc.: 0.9849
2023-09-09 23:10:43,030 [INFO] - Validation epoch stats:   Loss: 4.9547 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7221 - PQ-Score: 0.6101 - Tissue-MC-Acc.: 0.9512
2023-09-09 23:10:57,970 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-09 23:10:57,970 [INFO] - Epoch: 63/130
2023-09-09 23:13:33,332 [INFO] - Training epoch stats:     Loss: 4.8714 - Binary-Cell-Dice: 0.8101 - Binary-Cell-Jacard: 0.7378 - Tissue-MC-Acc.: 0.9871
2023-09-09 23:15:30,040 [INFO] - Validation epoch stats:   Loss: 4.9689 - Binary-Cell-Dice: 0.7971 - Binary-Cell-Jacard: 0.7218 - PQ-Score: 0.6107 - Tissue-MC-Acc.: 0.9501
2023-09-09 23:15:40,180 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-09 23:15:40,180 [INFO] - Epoch: 64/130
2023-09-09 23:18:07,643 [INFO] - Training epoch stats:     Loss: 4.8841 - Binary-Cell-Dice: 0.8103 - Binary-Cell-Jacard: 0.7380 - Tissue-MC-Acc.: 0.9901
2023-09-09 23:20:09,958 [INFO] - Validation epoch stats:   Loss: 4.9521 - Binary-Cell-Dice: 0.7987 - Binary-Cell-Jacard: 0.7241 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9477
2023-09-09 23:20:09,966 [INFO] - New best model - save checkpoint
2023-09-09 23:20:39,764 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-09 23:20:39,765 [INFO] - Epoch: 65/130
2023-09-09 23:23:11,038 [INFO] - Training epoch stats:     Loss: 4.8717 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7388 - Tissue-MC-Acc.: 0.9868
2023-09-09 23:25:10,542 [INFO] - Validation epoch stats:   Loss: 4.9569 - Binary-Cell-Dice: 0.7956 - Binary-Cell-Jacard: 0.7215 - PQ-Score: 0.6103 - Tissue-MC-Acc.: 0.9461
2023-09-09 23:25:16,516 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-09 23:25:16,516 [INFO] - Epoch: 66/130
2023-09-09 23:27:38,142 [INFO] - Training epoch stats:     Loss: 4.8661 - Binary-Cell-Dice: 0.8084 - Binary-Cell-Jacard: 0.7391 - Tissue-MC-Acc.: 0.9897
2023-09-09 23:29:32,964 [INFO] - Validation epoch stats:   Loss: 4.9452 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7190 - PQ-Score: 0.6102 - Tissue-MC-Acc.: 0.9516
2023-09-09 23:29:47,759 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-09 23:29:47,759 [INFO] - Epoch: 67/130
2023-09-09 23:32:14,278 [INFO] - Training epoch stats:     Loss: 4.8467 - Binary-Cell-Dice: 0.8106 - Binary-Cell-Jacard: 0.7394 - Tissue-MC-Acc.: 0.9893
2023-09-09 23:34:10,840 [INFO] - Validation epoch stats:   Loss: 4.9485 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6126 - Tissue-MC-Acc.: 0.9509
2023-09-09 23:34:24,210 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-09 23:34:24,211 [INFO] - Epoch: 68/130
2023-09-09 23:36:48,949 [INFO] - Training epoch stats:     Loss: 4.8598 - Binary-Cell-Dice: 0.8122 - Binary-Cell-Jacard: 0.7432 - Tissue-MC-Acc.: 0.9882
2023-09-09 23:38:39,919 [INFO] - Validation epoch stats:   Loss: 4.9529 - Binary-Cell-Dice: 0.7961 - Binary-Cell-Jacard: 0.7201 - PQ-Score: 0.6101 - Tissue-MC-Acc.: 0.9556
2023-09-09 23:38:57,856 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-09 23:38:57,856 [INFO] - Epoch: 69/130
2023-09-09 23:41:22,576 [INFO] - Training epoch stats:     Loss: 4.8680 - Binary-Cell-Dice: 0.8080 - Binary-Cell-Jacard: 0.7370 - Tissue-MC-Acc.: 0.9893
2023-09-09 23:43:23,031 [INFO] - Validation epoch stats:   Loss: 4.9501 - Binary-Cell-Dice: 0.7979 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6115 - Tissue-MC-Acc.: 0.9576
2023-09-09 23:43:40,330 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-09 23:43:40,331 [INFO] - Epoch: 70/130
2023-09-09 23:46:07,147 [INFO] - Training epoch stats:     Loss: 4.8478 - Binary-Cell-Dice: 0.8105 - Binary-Cell-Jacard: 0.7404 - Tissue-MC-Acc.: 0.9916
2023-09-09 23:48:05,333 [INFO] - Validation epoch stats:   Loss: 4.9328 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7232 - PQ-Score: 0.6142 - Tissue-MC-Acc.: 0.9552
2023-09-09 23:48:05,337 [INFO] - New best model - save checkpoint
2023-09-09 23:48:18,397 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-09 23:48:18,398 [INFO] - Epoch: 71/130
2023-09-09 23:50:41,407 [INFO] - Training epoch stats:     Loss: 4.8340 - Binary-Cell-Dice: 0.8104 - Binary-Cell-Jacard: 0.7405 - Tissue-MC-Acc.: 0.9890
2023-09-09 23:52:43,294 [INFO] - Validation epoch stats:   Loss: 4.9336 - Binary-Cell-Dice: 0.7982 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6152 - Tissue-MC-Acc.: 0.9556
2023-09-09 23:52:43,303 [INFO] - New best model - save checkpoint
2023-09-09 23:53:14,207 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-09 23:53:14,208 [INFO] - Epoch: 72/130
2023-09-09 23:55:40,162 [INFO] - Training epoch stats:     Loss: 4.8403 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7396 - Tissue-MC-Acc.: 0.9860
2023-09-09 23:57:37,338 [INFO] - Validation epoch stats:   Loss: 4.9455 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6139 - Tissue-MC-Acc.: 0.9524
2023-09-09 23:57:43,290 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-09 23:57:43,291 [INFO] - Epoch: 73/130
2023-09-10 00:00:11,880 [INFO] - Training epoch stats:     Loss: 4.8610 - Binary-Cell-Dice: 0.8126 - Binary-Cell-Jacard: 0.7439 - Tissue-MC-Acc.: 0.9930
2023-09-10 00:02:05,641 [INFO] - Validation epoch stats:   Loss: 4.9471 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6125 - Tissue-MC-Acc.: 0.9564
2023-09-10 00:02:18,438 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-10 00:02:18,438 [INFO] - Epoch: 74/130
2023-09-10 00:04:51,341 [INFO] - Training epoch stats:     Loss: 4.8409 - Binary-Cell-Dice: 0.8123 - Binary-Cell-Jacard: 0.7427 - Tissue-MC-Acc.: 0.9949
2023-09-10 00:06:46,870 [INFO] - Validation epoch stats:   Loss: 4.9480 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7210 - PQ-Score: 0.6138 - Tissue-MC-Acc.: 0.9576
2023-09-10 00:07:00,590 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-10 00:07:00,591 [INFO] - Epoch: 75/130
2023-09-10 00:09:36,047 [INFO] - Training epoch stats:     Loss: 4.8444 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7415 - Tissue-MC-Acc.: 0.9923
2023-09-10 00:11:35,031 [INFO] - Validation epoch stats:   Loss: 4.9485 - Binary-Cell-Dice: 0.7990 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6145 - Tissue-MC-Acc.: 0.9548
2023-09-10 00:11:46,040 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-10 00:11:46,040 [INFO] - Epoch: 76/130
2023-09-10 00:14:13,239 [INFO] - Training epoch stats:     Loss: 4.8298 - Binary-Cell-Dice: 0.8120 - Binary-Cell-Jacard: 0.7445 - Tissue-MC-Acc.: 0.9912
2023-09-10 00:16:12,613 [INFO] - Validation epoch stats:   Loss: 4.9561 - Binary-Cell-Dice: 0.7963 - Binary-Cell-Jacard: 0.7208 - PQ-Score: 0.6113 - Tissue-MC-Acc.: 0.9588
2023-09-10 00:16:39,919 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-10 00:16:39,920 [INFO] - Epoch: 77/130
2023-09-10 00:19:04,261 [INFO] - Training epoch stats:     Loss: 4.8179 - Binary-Cell-Dice: 0.8126 - Binary-Cell-Jacard: 0.7443 - Tissue-MC-Acc.: 0.9919
2023-09-10 00:21:03,630 [INFO] - Validation epoch stats:   Loss: 4.9411 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7231 - PQ-Score: 0.6141 - Tissue-MC-Acc.: 0.9552
2023-09-10 00:21:10,357 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-10 00:21:10,357 [INFO] - Epoch: 78/130
2023-09-10 00:23:29,433 [INFO] - Training epoch stats:     Loss: 4.8103 - Binary-Cell-Dice: 0.8133 - Binary-Cell-Jacard: 0.7457 - Tissue-MC-Acc.: 0.9945
2023-09-10 00:25:23,246 [INFO] - Validation epoch stats:   Loss: 4.9370 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7231 - PQ-Score: 0.6140 - Tissue-MC-Acc.: 0.9592
2023-09-10 00:25:38,368 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-10 00:25:38,369 [INFO] - Epoch: 79/130
2023-09-10 00:28:07,454 [INFO] - Training epoch stats:     Loss: 4.8149 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7432 - Tissue-MC-Acc.: 0.9908
2023-09-10 00:30:11,786 [INFO] - Validation epoch stats:   Loss: 4.9387 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6154 - Tissue-MC-Acc.: 0.9576
2023-09-10 00:30:11,796 [INFO] - New best model - save checkpoint
2023-09-10 00:30:37,967 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 00:30:37,968 [INFO] - Epoch: 80/130
2023-09-10 00:33:05,106 [INFO] - Training epoch stats:     Loss: 4.8289 - Binary-Cell-Dice: 0.8135 - Binary-Cell-Jacard: 0.7443 - Tissue-MC-Acc.: 0.9908
2023-09-10 00:34:57,124 [INFO] - Validation epoch stats:   Loss: 4.9530 - Binary-Cell-Dice: 0.7961 - Binary-Cell-Jacard: 0.7208 - PQ-Score: 0.6131 - Tissue-MC-Acc.: 0.9560
2023-09-10 00:35:11,994 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 00:35:11,995 [INFO] - Epoch: 81/130
2023-09-10 00:37:31,362 [INFO] - Training epoch stats:     Loss: 4.8151 - Binary-Cell-Dice: 0.8140 - Binary-Cell-Jacard: 0.7439 - Tissue-MC-Acc.: 0.9919
2023-09-10 00:39:51,876 [INFO] - Validation epoch stats:   Loss: 4.9411 - Binary-Cell-Dice: 0.7978 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6142 - Tissue-MC-Acc.: 0.9576
2023-09-10 00:40:07,938 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-10 00:40:07,939 [INFO] - Epoch: 82/130
2023-09-10 00:42:36,275 [INFO] - Training epoch stats:     Loss: 4.8138 - Binary-Cell-Dice: 0.8146 - Binary-Cell-Jacard: 0.7465 - Tissue-MC-Acc.: 0.9941
2023-09-10 00:44:28,424 [INFO] - Validation epoch stats:   Loss: 4.9417 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.9584
2023-09-10 00:44:37,340 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-10 00:44:37,340 [INFO] - Epoch: 83/130
2023-09-10 00:47:01,839 [INFO] - Training epoch stats:     Loss: 4.7918 - Binary-Cell-Dice: 0.8140 - Binary-Cell-Jacard: 0.7468 - Tissue-MC-Acc.: 0.9923
2023-09-10 00:48:55,307 [INFO] - Validation epoch stats:   Loss: 4.9479 - Binary-Cell-Dice: 0.7974 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9576
2023-09-10 00:49:08,786 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 00:49:08,787 [INFO] - Epoch: 84/130
2023-09-10 00:51:45,861 [INFO] - Training epoch stats:     Loss: 4.7996 - Binary-Cell-Dice: 0.8137 - Binary-Cell-Jacard: 0.7465 - Tissue-MC-Acc.: 0.9938
2023-09-10 00:53:45,796 [INFO] - Validation epoch stats:   Loss: 4.9388 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7220 - PQ-Score: 0.6138 - Tissue-MC-Acc.: 0.9572
2023-09-10 00:53:56,859 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 00:53:56,859 [INFO] - Epoch: 85/130
2023-09-10 00:56:14,710 [INFO] - Training epoch stats:     Loss: 4.8089 - Binary-Cell-Dice: 0.8149 - Binary-Cell-Jacard: 0.7465 - Tissue-MC-Acc.: 0.9945
2023-09-10 00:58:07,501 [INFO] - Validation epoch stats:   Loss: 4.9447 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9580
2023-09-10 00:58:21,387 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 00:58:21,388 [INFO] - Epoch: 86/130
2023-09-10 01:00:45,509 [INFO] - Training epoch stats:     Loss: 4.7996 - Binary-Cell-Dice: 0.8148 - Binary-Cell-Jacard: 0.7484 - Tissue-MC-Acc.: 0.9923
2023-09-10 01:02:47,487 [INFO] - Validation epoch stats:   Loss: 4.9390 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7236 - PQ-Score: 0.6152 - Tissue-MC-Acc.: 0.9580
2023-09-10 01:03:01,794 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-10 01:03:01,794 [INFO] - Epoch: 87/130
2023-09-10 01:05:28,098 [INFO] - Training epoch stats:     Loss: 4.8066 - Binary-Cell-Dice: 0.8136 - Binary-Cell-Jacard: 0.7444 - Tissue-MC-Acc.: 0.9945
2023-09-10 01:07:20,516 [INFO] - Validation epoch stats:   Loss: 4.9466 - Binary-Cell-Dice: 0.7989 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6154 - Tissue-MC-Acc.: 0.9568
2023-09-10 01:07:26,894 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-10 01:07:26,895 [INFO] - Epoch: 88/130
2023-09-10 01:09:58,689 [INFO] - Training epoch stats:     Loss: 4.8009 - Binary-Cell-Dice: 0.8154 - Binary-Cell-Jacard: 0.7483 - Tissue-MC-Acc.: 0.9882
2023-09-10 01:11:56,089 [INFO] - Validation epoch stats:   Loss: 4.9471 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9568
2023-09-10 01:12:10,500 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:12:10,501 [INFO] - Epoch: 89/130
2023-09-10 01:14:31,879 [INFO] - Training epoch stats:     Loss: 4.7967 - Binary-Cell-Dice: 0.8136 - Binary-Cell-Jacard: 0.7474 - Tissue-MC-Acc.: 0.9934
2023-09-10 01:16:24,139 [INFO] - Validation epoch stats:   Loss: 4.9429 - Binary-Cell-Dice: 0.7979 - Binary-Cell-Jacard: 0.7232 - PQ-Score: 0.6141 - Tissue-MC-Acc.: 0.9596
2023-09-10 01:16:40,718 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:16:40,719 [INFO] - Epoch: 90/130
2023-09-10 01:19:07,161 [INFO] - Training epoch stats:     Loss: 4.7791 - Binary-Cell-Dice: 0.8135 - Binary-Cell-Jacard: 0.7462 - Tissue-MC-Acc.: 0.9956
2023-09-10 01:20:59,477 [INFO] - Validation epoch stats:   Loss: 4.9431 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9600
2023-09-10 01:21:10,023 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:21:10,023 [INFO] - Epoch: 91/130
2023-09-10 01:23:32,561 [INFO] - Training epoch stats:     Loss: 4.7828 - Binary-Cell-Dice: 0.8136 - Binary-Cell-Jacard: 0.7480 - Tissue-MC-Acc.: 0.9912
2023-09-10 01:25:32,850 [INFO] - Validation epoch stats:   Loss: 4.9390 - Binary-Cell-Dice: 0.7989 - Binary-Cell-Jacard: 0.7230 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9600
2023-09-10 01:25:44,555 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:25:44,556 [INFO] - Epoch: 92/130
2023-09-10 01:28:13,707 [INFO] - Training epoch stats:     Loss: 4.7852 - Binary-Cell-Dice: 0.8152 - Binary-Cell-Jacard: 0.7484 - Tissue-MC-Acc.: 0.9904
2023-09-10 01:30:17,793 [INFO] - Validation epoch stats:   Loss: 4.9419 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6138 - Tissue-MC-Acc.: 0.9604
2023-09-10 01:30:32,482 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:30:32,483 [INFO] - Epoch: 93/130
2023-09-10 01:32:52,253 [INFO] - Training epoch stats:     Loss: 4.7849 - Binary-Cell-Dice: 0.8133 - Binary-Cell-Jacard: 0.7467 - Tissue-MC-Acc.: 0.9945
2023-09-10 01:34:57,597 [INFO] - Validation epoch stats:   Loss: 4.9424 - Binary-Cell-Dice: 0.7977 - Binary-Cell-Jacard: 0.7225 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9580
2023-09-10 01:35:14,960 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-10 01:35:14,960 [INFO] - Epoch: 94/130
2023-09-10 01:37:52,372 [INFO] - Training epoch stats:     Loss: 4.7866 - Binary-Cell-Dice: 0.8152 - Binary-Cell-Jacard: 0.7490 - Tissue-MC-Acc.: 0.9945
2023-09-10 01:39:45,708 [INFO] - Validation epoch stats:   Loss: 4.9404 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6133 - Tissue-MC-Acc.: 0.9584
2023-09-10 01:39:51,784 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-10 01:39:51,785 [INFO] - Epoch: 95/130
2023-09-10 01:42:10,929 [INFO] - Training epoch stats:     Loss: 4.7961 - Binary-Cell-Dice: 0.8152 - Binary-Cell-Jacard: 0.7468 - Tissue-MC-Acc.: 0.9960
2023-09-10 01:44:11,947 [INFO] - Validation epoch stats:   Loss: 4.9398 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7230 - PQ-Score: 0.6123 - Tissue-MC-Acc.: 0.9580
2023-09-10 01:44:27,442 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 01:44:27,443 [INFO] - Epoch: 96/130
2023-09-10 01:46:51,904 [INFO] - Training epoch stats:     Loss: 4.7961 - Binary-Cell-Dice: 0.8150 - Binary-Cell-Jacard: 0.7469 - Tissue-MC-Acc.: 0.9934
2023-09-10 01:48:50,172 [INFO] - Validation epoch stats:   Loss: 4.9361 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6139 - Tissue-MC-Acc.: 0.9580
2023-09-10 01:48:56,256 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 01:48:56,257 [INFO] - Epoch: 97/130
2023-09-10 01:51:16,980 [INFO] - Training epoch stats:     Loss: 4.7648 - Binary-Cell-Dice: 0.8167 - Binary-Cell-Jacard: 0.7497 - Tissue-MC-Acc.: 0.9923
2023-09-10 01:53:11,996 [INFO] - Validation epoch stats:   Loss: 4.9416 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9568
2023-09-10 01:53:30,067 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 01:53:30,067 [INFO] - Epoch: 98/130
2023-09-10 01:56:08,332 [INFO] - Training epoch stats:     Loss: 4.7572 - Binary-Cell-Dice: 0.8160 - Binary-Cell-Jacard: 0.7492 - Tissue-MC-Acc.: 0.9919
2023-09-10 01:58:09,845 [INFO] - Validation epoch stats:   Loss: 4.9447 - Binary-Cell-Dice: 0.7958 - Binary-Cell-Jacard: 0.7212 - PQ-Score: 0.6120 - Tissue-MC-Acc.: 0.9580
2023-09-10 01:58:16,063 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 01:58:16,063 [INFO] - Epoch: 99/130
2023-09-10 02:00:34,527 [INFO] - Training epoch stats:     Loss: 4.7797 - Binary-Cell-Dice: 0.8156 - Binary-Cell-Jacard: 0.7471 - Tissue-MC-Acc.: 0.9945
2023-09-10 02:02:23,251 [INFO] - Validation epoch stats:   Loss: 4.9393 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7232 - PQ-Score: 0.6147 - Tissue-MC-Acc.: 0.9592
2023-09-10 02:02:30,951 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 02:02:30,952 [INFO] - Epoch: 100/130
2023-09-10 02:04:55,324 [INFO] - Training epoch stats:     Loss: 4.7960 - Binary-Cell-Dice: 0.8141 - Binary-Cell-Jacard: 0.7478 - Tissue-MC-Acc.: 0.9934
2023-09-10 02:06:53,955 [INFO] - Validation epoch stats:   Loss: 4.9375 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7220 - PQ-Score: 0.6135 - Tissue-MC-Acc.: 0.9572
2023-09-10 02:07:14,048 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 02:07:14,048 [INFO] - Epoch: 101/130
2023-09-10 02:09:47,984 [INFO] - Training epoch stats:     Loss: 4.7757 - Binary-Cell-Dice: 0.8149 - Binary-Cell-Jacard: 0.7468 - Tissue-MC-Acc.: 0.9919
2023-09-10 02:11:41,003 [INFO] - Validation epoch stats:   Loss: 4.9432 - Binary-Cell-Dice: 0.7980 - Binary-Cell-Jacard: 0.7224 - PQ-Score: 0.6149 - Tissue-MC-Acc.: 0.9588
2023-09-10 02:11:47,456 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 02:11:47,457 [INFO] - Epoch: 102/130
2023-09-10 02:14:20,313 [INFO] - Training epoch stats:     Loss: 4.7745 - Binary-Cell-Dice: 0.8148 - Binary-Cell-Jacard: 0.7480 - Tissue-MC-Acc.: 0.9949
2023-09-10 02:16:17,587 [INFO] - Validation epoch stats:   Loss: 4.9398 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7231 - PQ-Score: 0.6145 - Tissue-MC-Acc.: 0.9592
2023-09-10 02:16:33,378 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 02:16:33,379 [INFO] - Epoch: 103/130
2023-09-10 02:19:03,665 [INFO] - Training epoch stats:     Loss: 4.8038 - Binary-Cell-Dice: 0.8149 - Binary-Cell-Jacard: 0.7470 - Tissue-MC-Acc.: 0.9938
2023-09-10 02:21:04,019 [INFO] - Validation epoch stats:   Loss: 4.9430 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6160 - Tissue-MC-Acc.: 0.9588
2023-09-10 02:21:04,022 [INFO] - New best model - save checkpoint
2023-09-10 02:21:19,818 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-10 02:21:19,819 [INFO] - Epoch: 104/130
2023-09-10 02:23:53,680 [INFO] - Training epoch stats:     Loss: 4.7803 - Binary-Cell-Dice: 0.8171 - Binary-Cell-Jacard: 0.7511 - Tissue-MC-Acc.: 0.9938
2023-09-10 02:25:52,614 [INFO] - Validation epoch stats:   Loss: 4.9396 - Binary-Cell-Dice: 0.7984 - Binary-Cell-Jacard: 0.7230 - PQ-Score: 0.6153 - Tissue-MC-Acc.: 0.9580
2023-09-10 02:26:04,632 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-10 02:26:04,632 [INFO] - Epoch: 105/130
2023-09-10 02:28:34,998 [INFO] - Training epoch stats:     Loss: 4.7571 - Binary-Cell-Dice: 0.8159 - Binary-Cell-Jacard: 0.7487 - Tissue-MC-Acc.: 0.9938
2023-09-10 02:30:35,067 [INFO] - Validation epoch stats:   Loss: 4.9368 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7241 - PQ-Score: 0.6137 - Tissue-MC-Acc.: 0.9584
2023-09-10 02:30:49,402 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:30:49,402 [INFO] - Epoch: 106/130
2023-09-10 02:33:17,339 [INFO] - Training epoch stats:     Loss: 4.7830 - Binary-Cell-Dice: 0.8150 - Binary-Cell-Jacard: 0.7482 - Tissue-MC-Acc.: 0.9923
2023-09-10 02:35:07,976 [INFO] - Validation epoch stats:   Loss: 4.9365 - Binary-Cell-Dice: 0.7988 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6146 - Tissue-MC-Acc.: 0.9592
2023-09-10 02:35:23,549 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:35:23,549 [INFO] - Epoch: 107/130
2023-09-10 02:37:50,548 [INFO] - Training epoch stats:     Loss: 4.7720 - Binary-Cell-Dice: 0.8157 - Binary-Cell-Jacard: 0.7480 - Tissue-MC-Acc.: 0.9930
2023-09-10 02:39:48,215 [INFO] - Validation epoch stats:   Loss: 4.9410 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7233 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.9588
2023-09-10 02:40:02,867 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:40:02,867 [INFO] - Epoch: 108/130
2023-09-10 02:42:33,605 [INFO] - Training epoch stats:     Loss: 4.7708 - Binary-Cell-Dice: 0.8157 - Binary-Cell-Jacard: 0.7495 - Tissue-MC-Acc.: 0.9938
2023-09-10 02:44:33,093 [INFO] - Validation epoch stats:   Loss: 4.9415 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7229 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9592
2023-09-10 02:44:46,701 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:44:46,702 [INFO] - Epoch: 109/130
2023-09-10 02:47:16,363 [INFO] - Training epoch stats:     Loss: 4.7683 - Binary-Cell-Dice: 0.8176 - Binary-Cell-Jacard: 0.7516 - Tissue-MC-Acc.: 0.9923
2023-09-10 02:49:06,534 [INFO] - Validation epoch stats:   Loss: 4.9375 - Binary-Cell-Dice: 0.7979 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6149 - Tissue-MC-Acc.: 0.9604
2023-09-10 02:49:28,673 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:49:28,674 [INFO] - Epoch: 110/130
2023-09-10 02:51:57,851 [INFO] - Training epoch stats:     Loss: 4.7651 - Binary-Cell-Dice: 0.8156 - Binary-Cell-Jacard: 0.7507 - Tissue-MC-Acc.: 0.9934
2023-09-10 02:53:54,662 [INFO] - Validation epoch stats:   Loss: 4.9419 - Binary-Cell-Dice: 0.7985 - Binary-Cell-Jacard: 0.7233 - PQ-Score: 0.6145 - Tissue-MC-Acc.: 0.9584
2023-09-10 02:54:14,727 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:54:14,727 [INFO] - Epoch: 111/130
2023-09-10 02:56:49,817 [INFO] - Training epoch stats:     Loss: 4.7764 - Binary-Cell-Dice: 0.8161 - Binary-Cell-Jacard: 0.7491 - Tissue-MC-Acc.: 0.9960
2023-09-10 02:58:49,007 [INFO] - Validation epoch stats:   Loss: 4.9410 - Binary-Cell-Dice: 0.7974 - Binary-Cell-Jacard: 0.7239 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9604
2023-09-10 02:59:01,224 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 02:59:01,225 [INFO] - Epoch: 112/130
2023-09-10 03:01:27,455 [INFO] - Training epoch stats:     Loss: 4.7604 - Binary-Cell-Dice: 0.8154 - Binary-Cell-Jacard: 0.7492 - Tissue-MC-Acc.: 0.9967
2023-09-10 03:03:22,368 [INFO] - Validation epoch stats:   Loss: 4.9372 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7233 - PQ-Score: 0.6142 - Tissue-MC-Acc.: 0.9600
2023-09-10 03:03:37,360 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:03:37,360 [INFO] - Epoch: 113/130
2023-09-10 03:06:02,675 [INFO] - Training epoch stats:     Loss: 4.7688 - Binary-Cell-Dice: 0.8166 - Binary-Cell-Jacard: 0.7496 - Tissue-MC-Acc.: 0.9963
2023-09-10 03:07:58,220 [INFO] - Validation epoch stats:   Loss: 4.9408 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7239 - PQ-Score: 0.6145 - Tissue-MC-Acc.: 0.9600
2023-09-10 03:08:12,982 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:08:12,983 [INFO] - Epoch: 114/130
2023-09-10 03:10:48,232 [INFO] - Training epoch stats:     Loss: 4.7643 - Binary-Cell-Dice: 0.8171 - Binary-Cell-Jacard: 0.7520 - Tissue-MC-Acc.: 0.9949
2023-09-10 03:12:37,788 [INFO] - Validation epoch stats:   Loss: 4.9416 - Binary-Cell-Dice: 0.7976 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.9584
2023-09-10 03:12:56,668 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:12:56,669 [INFO] - Epoch: 115/130
2023-09-10 03:15:14,849 [INFO] - Training epoch stats:     Loss: 4.7912 - Binary-Cell-Dice: 0.8172 - Binary-Cell-Jacard: 0.7500 - Tissue-MC-Acc.: 0.9945
2023-09-10 03:17:12,099 [INFO] - Validation epoch stats:   Loss: 4.9349 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6160 - Tissue-MC-Acc.: 0.9596
2023-09-10 03:17:12,109 [INFO] - New best model - save checkpoint
2023-09-10 03:17:39,152 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:17:39,153 [INFO] - Epoch: 116/130
2023-09-10 03:20:12,658 [INFO] - Training epoch stats:     Loss: 4.7908 - Binary-Cell-Dice: 0.8181 - Binary-Cell-Jacard: 0.7509 - Tissue-MC-Acc.: 0.9941
2023-09-10 03:22:09,575 [INFO] - Validation epoch stats:   Loss: 4.9429 - Binary-Cell-Dice: 0.7980 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6143 - Tissue-MC-Acc.: 0.9588
2023-09-10 03:22:22,956 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:22:22,956 [INFO] - Epoch: 117/130
2023-09-10 03:24:43,087 [INFO] - Training epoch stats:     Loss: 4.7427 - Binary-Cell-Dice: 0.8175 - Binary-Cell-Jacard: 0.7499 - Tissue-MC-Acc.: 0.9945
2023-09-10 03:26:43,692 [INFO] - Validation epoch stats:   Loss: 4.9386 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7242 - PQ-Score: 0.6156 - Tissue-MC-Acc.: 0.9592
2023-09-10 03:26:58,953 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:26:58,953 [INFO] - Epoch: 118/130
2023-09-10 03:29:23,072 [INFO] - Training epoch stats:     Loss: 4.7669 - Binary-Cell-Dice: 0.8178 - Binary-Cell-Jacard: 0.7514 - Tissue-MC-Acc.: 0.9923
2023-09-10 03:31:18,612 [INFO] - Validation epoch stats:   Loss: 4.9454 - Binary-Cell-Dice: 0.7977 - Binary-Cell-Jacard: 0.7228 - PQ-Score: 0.6132 - Tissue-MC-Acc.: 0.9588
2023-09-10 03:31:36,849 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:31:36,849 [INFO] - Epoch: 119/130
2023-09-10 03:34:12,175 [INFO] - Training epoch stats:     Loss: 4.7773 - Binary-Cell-Dice: 0.8159 - Binary-Cell-Jacard: 0.7494 - Tissue-MC-Acc.: 0.9956
2023-09-10 03:36:10,993 [INFO] - Validation epoch stats:   Loss: 4.9376 - Binary-Cell-Dice: 0.7979 - Binary-Cell-Jacard: 0.7231 - PQ-Score: 0.6150 - Tissue-MC-Acc.: 0.9592
2023-09-10 03:36:26,192 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:36:26,193 [INFO] - Epoch: 120/130
2023-09-10 03:38:55,740 [INFO] - Training epoch stats:     Loss: 4.7643 - Binary-Cell-Dice: 0.8170 - Binary-Cell-Jacard: 0.7507 - Tissue-MC-Acc.: 0.9967
2023-09-10 03:40:53,913 [INFO] - Validation epoch stats:   Loss: 4.9428 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7233 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.9592
2023-09-10 03:41:21,897 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:41:21,898 [INFO] - Epoch: 121/130
2023-09-10 03:43:52,219 [INFO] - Training epoch stats:     Loss: 4.7526 - Binary-Cell-Dice: 0.8172 - Binary-Cell-Jacard: 0.7505 - Tissue-MC-Acc.: 0.9923
2023-09-10 03:45:50,144 [INFO] - Validation epoch stats:   Loss: 4.9439 - Binary-Cell-Dice: 0.7978 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6144 - Tissue-MC-Acc.: 0.9588
2023-09-10 03:46:17,193 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:46:17,194 [INFO] - Epoch: 122/130
2023-09-10 03:48:44,705 [INFO] - Training epoch stats:     Loss: 4.7592 - Binary-Cell-Dice: 0.8160 - Binary-Cell-Jacard: 0.7485 - Tissue-MC-Acc.: 0.9938
2023-09-10 03:50:45,741 [INFO] - Validation epoch stats:   Loss: 4.9437 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7236 - PQ-Score: 0.6155 - Tissue-MC-Acc.: 0.9596
2023-09-10 03:51:01,090 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:51:01,090 [INFO] - Epoch: 123/130
2023-09-10 03:53:28,136 [INFO] - Training epoch stats:     Loss: 4.7860 - Binary-Cell-Dice: 0.8175 - Binary-Cell-Jacard: 0.7513 - Tissue-MC-Acc.: 0.9952
2023-09-10 03:55:26,826 [INFO] - Validation epoch stats:   Loss: 4.9461 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7240 - PQ-Score: 0.6156 - Tissue-MC-Acc.: 0.9608
2023-09-10 03:55:33,455 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 03:55:33,455 [INFO] - Epoch: 124/130
2023-09-10 03:57:57,907 [INFO] - Training epoch stats:     Loss: 4.7549 - Binary-Cell-Dice: 0.8159 - Binary-Cell-Jacard: 0.7486 - Tissue-MC-Acc.: 0.9949
2023-09-10 03:59:58,978 [INFO] - Validation epoch stats:   Loss: 4.9385 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.9596
2023-09-10 04:00:14,538 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-10 04:00:14,538 [INFO] - Epoch: 125/130
2023-09-10 04:02:42,741 [INFO] - Training epoch stats:     Loss: 4.7546 - Binary-Cell-Dice: 0.8169 - Binary-Cell-Jacard: 0.7514 - Tissue-MC-Acc.: 0.9934
2023-09-10 04:04:39,633 [INFO] - Validation epoch stats:   Loss: 4.9377 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7233 - PQ-Score: 0.6136 - Tissue-MC-Acc.: 0.9592
2023-09-10 04:04:57,459 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-10 04:04:57,460 [INFO] - Epoch: 126/130
2023-09-10 04:07:15,844 [INFO] - Training epoch stats:     Loss: 4.7601 - Binary-Cell-Dice: 0.8170 - Binary-Cell-Jacard: 0.7509 - Tissue-MC-Acc.: 0.9956
2023-09-10 04:09:07,516 [INFO] - Validation epoch stats:   Loss: 4.9378 - Binary-Cell-Dice: 0.7978 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6154 - Tissue-MC-Acc.: 0.9592
2023-09-10 04:09:26,749 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 04:09:26,750 [INFO] - Epoch: 127/130
2023-09-10 04:12:02,265 [INFO] - Training epoch stats:     Loss: 4.7673 - Binary-Cell-Dice: 0.8161 - Binary-Cell-Jacard: 0.7493 - Tissue-MC-Acc.: 0.9945
2023-09-10 04:13:59,787 [INFO] - Validation epoch stats:   Loss: 4.9402 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7223 - PQ-Score: 0.6157 - Tissue-MC-Acc.: 0.9600
2023-09-10 04:14:14,599 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 04:14:14,600 [INFO] - Epoch: 128/130
2023-09-10 04:16:48,301 [INFO] - Training epoch stats:     Loss: 4.7815 - Binary-Cell-Dice: 0.8145 - Binary-Cell-Jacard: 0.7495 - Tissue-MC-Acc.: 0.9945
2023-09-10 04:18:40,503 [INFO] - Validation epoch stats:   Loss: 4.9469 - Binary-Cell-Dice: 0.7975 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6153 - Tissue-MC-Acc.: 0.9592
2023-09-10 04:18:46,271 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 04:18:46,272 [INFO] - Epoch: 129/130
2023-09-10 04:21:11,718 [INFO] - Training epoch stats:     Loss: 4.7622 - Binary-Cell-Dice: 0.8157 - Binary-Cell-Jacard: 0.7485 - Tissue-MC-Acc.: 0.9956
2023-09-10 04:23:06,113 [INFO] - Validation epoch stats:   Loss: 4.9391 - Binary-Cell-Dice: 0.7984 - Binary-Cell-Jacard: 0.7236 - PQ-Score: 0.6155 - Tissue-MC-Acc.: 0.9596
2023-09-10 04:23:22,166 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 04:23:22,167 [INFO] - Epoch: 130/130
2023-09-10 04:25:51,968 [INFO] - Training epoch stats:     Loss: 4.7759 - Binary-Cell-Dice: 0.8144 - Binary-Cell-Jacard: 0.7491 - Tissue-MC-Acc.: 0.9967
2023-09-10 04:27:49,033 [INFO] - Validation epoch stats:   Loss: 4.9430 - Binary-Cell-Dice: 0.7969 - Binary-Cell-Jacard: 0.7227 - PQ-Score: 0.6144 - Tissue-MC-Acc.: 0.9588
2023-09-10 04:28:02,793 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-10 04:28:02,794 [INFO] -
