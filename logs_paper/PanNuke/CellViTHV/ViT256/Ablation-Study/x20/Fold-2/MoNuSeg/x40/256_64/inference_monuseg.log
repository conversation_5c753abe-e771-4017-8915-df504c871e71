<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 1344
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 281
Iteration 1: Found overlap of # cells: 8
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 791
Detected cells before cleaning: 1528
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 340
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 906
Detected cells before cleaning: 1027
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 220
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 618
Detected cells before cleaning: 835
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 186
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 499
Detected cells before cleaning: 990
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 208
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 628
Detected cells before cleaning: 402
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 78
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 265
Detected cells before cleaning: 1462
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 361
Iteration 1: Found overlap of # cells: 8
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 865
Detected cells before cleaning: 965
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 220
Iteration 1: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 564
Detected cells before cleaning: 1030
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 185
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 625
Detected cells before cleaning: 706
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 133
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 419
Detected cells before cleaning: 670
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 132
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 409
Detected cells before cleaning: 562
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 136
Iteration 1: Found overlap of # cells: 9
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 323
Detected cells before cleaning: 1122
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 231
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 673
Detected cells before cleaning: 975
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 201
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 588
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.76141357421875
Binary-Cell-Jacard-Mean:  0.6267400979995728
bPQ:                      0.****************
bDQ:                      0.7096218995266808
bSQ:                      0.7309546064928053
f1_detection:             0.8203180972120192
precision_detection:      0.7491882650409399
recall_detection:         0.9143180910340786
