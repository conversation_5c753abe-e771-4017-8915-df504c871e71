<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 1330
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 278
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 791
Detected cells before cleaning: 1577
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 351
Iteration 1: Found overlap of # cells: 8
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 939
Detected cells before cleaning: 1057
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 212
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 655
Detected cells before cleaning: 951
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 206
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 581
Detected cells before cleaning: 1151
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 246
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 717
Detected cells before cleaning: 933
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 172
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 586
Detected cells before cleaning: 1593
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 371
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 965
Detected cells before cleaning: 1011
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 219
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 597
Detected cells before cleaning: 1032
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 202
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 621
Detected cells before cleaning: 757
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 140
Iteration 1: Found overlap of # cells: 9
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 450
Detected cells before cleaning: 683
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 131
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 416
Detected cells before cleaning: 543
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 124
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 317
Detected cells before cleaning: 1134
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 229
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 693
Detected cells before cleaning: 933
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 189
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 564
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.7832499742507935
Binary-Cell-Jacard-Mean:  0.6445698738098145
bPQ:                      0.****************
bDQ:                      0.7206949902195339
bSQ:                      0.7167773573462108
f1_detection:             0.8352828168364095
precision_detection:      0.7340795856234072
recall_detection:         0.9729061600194199
