<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 1320
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 269
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 783
Detected cells before cleaning: 1561
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 341
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 937
Detected cells before cleaning: 1040
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 205
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 635
Detected cells before cleaning: 934
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 196
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 565
Detected cells before cleaning: 1108
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 225
Iteration 1: Found overlap of # cells: 11
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 692
Detected cells before cleaning: 830
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 153
Iteration 1: Found overlap of # cells: 9
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 519
Detected cells before cleaning: 1531
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 350
Iteration 1: Found overlap of # cells: 11
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 923
Detected cells before cleaning: 1007
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 211
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 596
Detected cells before cleaning: 988
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 168
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 601
Detected cells before cleaning: 747
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 129
Iteration 1: Found overlap of # cells: 7
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 436
Detected cells before cleaning: 715
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 136
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 427
Detected cells before cleaning: 542
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 121
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 306
Detected cells before cleaning: 1119
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 220
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 676
Detected cells before cleaning: 897
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 178
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 536
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.791022777557373
Binary-Cell-Jacard-Mean:  0.****************
bPQ:                      0.****************
bDQ:                      0.7585320878818013
bSQ:                      0.7205731329381914
f1_detection:             0.8490513059271676
precision_detection:      0.7552634040421452
recall_detection:         0.97223333141349
