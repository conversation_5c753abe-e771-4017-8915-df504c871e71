<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 920
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 240
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 597
Detected cells before cleaning: 1123
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 306
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 743
Detected cells before cleaning: 725
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 182
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 476
Detected cells before cleaning: 612
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 159
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 400
Detected cells before cleaning: 787
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 222
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 506
Detected cells before cleaning: 617
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 159
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 374
Detected cells before cleaning: 1036
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 298
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 665
Detected cells before cleaning: 686
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 185
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 435
Detected cells before cleaning: 692
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 171
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 438
Detected cells before cleaning: 554
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 146
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 361
Detected cells before cleaning: 496
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 134
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 316
Detected cells before cleaning: 370
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 103
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 235
Detected cells before cleaning: 807
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 225
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 522
Detected cells before cleaning: 686
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 163
Iteration 1: Found overlap of # cells: 1
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 449
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.8188766837120056
Binary-Cell-Jacard-Mean:  0.6936725378036499
bPQ:                      0.648623089022795
bDQ:                      0.8436158533378298
bSQ:                      0.7684057075673545
f1_detection:             0.8194439017038425
precision_detection:      0.8309627844271714
recall_detection:         0.8093055293064113
