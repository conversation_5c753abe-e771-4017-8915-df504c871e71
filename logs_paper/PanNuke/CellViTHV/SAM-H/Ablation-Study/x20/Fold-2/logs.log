2023-09-25 06:59:01,640 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-25 06:59:01,722 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f50a0733ca0>]
2023-09-25 06:59:01,722 [INFO] - Using GPU: cuda:0
2023-09-25 06:59:01,722 [INFO] - Using device: cuda:0
2023-09-25 06:59:01,723 [INFO] - Loss functions:
2023-09-25 06:59:01,723 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON>ce<PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-25 06:59:58,505 [INFO] - Loaded CellViT-SAM model with backbone: SAM-H
2023-09-25 06:59:58,515 [INFO] -
Model: CellViTSAM(
  (encoder): ViTCellViTDeit(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 1280, kernel_size=(16, 16), stride=(16, 16))
    )
    (blocks): ModuleList(
      (0-31): 32 x Block(
        (norm1): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=1280, out_features=3840, bias=True)
          (proj): Linear(in_features=1280, out_features=1280, bias=True)
        )
        (norm2): LayerNorm((1280,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=1280, out_features=5120, bias=True)
          (lin2): Linear(in_features=5120, out_features=1280, bias=True)
          (act): GELU(approximate='none')
        )
      )
    )
    (neck): Sequential(
      (0): Conv2d(1280, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (1): LayerNorm2d()
      (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
      (3): LayerNorm2d()
    )
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(1280, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
)
2023-09-25 07:00:01,419 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViTSAM                                    [1, 6, 256, 256]          --
├─ViTCellViTDeit: 1-1                         [1, 256]                  5,242,880
│    └─PatchEmbed: 2-1                        [1, 16, 16, 1280]         --
│    │    └─Conv2d: 3-1                       [1, 1280, 16, 16]         (984,320)
│    └─ModuleList: 2-2                        --                        --
│    │    └─Block: 3-2                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-3                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-4                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-5                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-6                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-7                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-8                        [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-9                        [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-10                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-11                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-12                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-13                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-14                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-15                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-16                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-17                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-18                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-19                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-20                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-21                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-22                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-23                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-24                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-25                       [1, 16, 16, 1280]         (19,697,760)
│    │    └─Block: 3-26                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-27                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-28                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-29                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-30                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-31                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-32                       [1, 16, 16, 1280]         (19,681,760)
│    │    └─Block: 3-33                       [1, 16, 16, 1280]         (19,697,760)
│    └─Sequential: 2-3                        [1, 256, 16, 16]          --
│    │    └─Conv2d: 3-34                      [1, 256, 16, 16]          (327,680)
│    │    └─LayerNorm2d: 3-35                 [1, 256, 16, 16]          (512)
│    │    └─Conv2d: 3-36                      [1, 256, 16, 16]          (589,824)
│    │    └─LayerNorm2d: 3-37                 [1, 256, 16, 16]          (512)
├─Linear: 1-2                                 [1, 19]                   4,883
├─Sequential: 1-11                            --                        (recursive)
│    └─ConvTranspose2d: 2-4                   [1, 512, 32, 32]          2,621,952
├─Sequential: 1-4                             [1, 512, 32, 32]          --
│    └─Deconv2DBlock: 2-5                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-38                  [1, 512, 32, 32]          4,982,784
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-6                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-39                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-40                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-41                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-42             [1, 256, 64, 64]          524,544
├─Sequential: 1-6                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-7                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-43                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-8                     [1, 256, 64, 64]          --
│    │    └─Sequential: 3-44                  [1, 256, 64, 64]          1,115,136
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-9                        [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-45                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-46                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-47             [1, 128, 128, 128]        131,200
├─Sequential: 1-8                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-10                    [1, 512, 32, 32]          --
│    │    └─Sequential: 3-48                  [1, 512, 32, 32]          4,982,784
│    └─Deconv2DBlock: 2-11                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-49                  [1, 256, 64, 64]          1,115,136
│    └─Deconv2DBlock: 2-12                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-50                  [1, 128, 128, 128]        279,040
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-13                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-51                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-52                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-53             [1, 64, 256, 256]         32,832
├─Sequential: 1-10                            [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-14                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-54                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-15                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-55                  [1, 64, 256, 256]         18,624
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-16                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-56                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-57                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-58                      [1, 2, 256, 256]          130
├─Sequential: 1-20                            --                        (recursive)
│    └─ConvTranspose2d: 2-17                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-13                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-18                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-59                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-19                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-60                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-61                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-62                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-63             [1, 256, 64, 64]          524,544
├─Sequential: 1-15                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-64                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-21                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-65                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-22                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-66                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-67                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-68             [1, 128, 128, 128]        131,200
├─Sequential: 1-17                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-69                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-24                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-70                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-71                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-26                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-72                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-73                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-74             [1, 64, 256, 256]         32,832
├─Sequential: 1-19                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-27                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-75                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-28                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-76                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-29                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-77                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-78                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-79                      [1, 2, 256, 256]          130
├─Sequential: 1-29                            --                        (recursive)
│    └─ConvTranspose2d: 2-30                  [1, 512, 32, 32]          2,621,952
├─Sequential: 1-22                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-31                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-80                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-32                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-81                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-82                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-83                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-84             [1, 256, 64, 64]          524,544
├─Sequential: 1-24                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-85                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-34                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-86                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-35                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-87                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-88                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-89             [1, 128, 128, 128]        131,200
├─Sequential: 1-26                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-90                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-37                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-91                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-92                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-39                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-93                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-94                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-95             [1, 64, 256, 256]         32,832
├─Sequential: 1-28                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-40                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-96                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-41                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-97                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-42                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-98                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-99                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-100                     [1, 6, 256, 256]          390
===============================================================================================
Total params: 699,741,149
Trainable params: 62,715,101
Non-trainable params: 637,026,048
Total mult-adds (G): 214.20
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 3224.37
Params size (MB): 2777.18
Estimated Total Size (MB): 6002.34
===============================================================================================
2023-09-25 07:00:03,879 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-25 07:00:03,879 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-25 07:00:03,880 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-25 07:00:05,807 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-25 07:00:05,810 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.5950, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314,
        0.7691, 0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9474,
        0.9667, 0.9823, 0.9883, 0.9902, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200,
        1.0215, 1.0260, 1.0278, 1.0367, 1.0449, 1.0464, 1.0536, 1.0910, 1.0913,
        1.0922, 1.0927, 1.1088, 1.1094, 1.1143, 1.1173, 1.1216, 1.1256, 1.1283,
        1.1287, 1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1777, 1.1792, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2527, 1.2576, 1.2580, 1.2595, 1.2606, 1.2621, 1.2651, 1.2663, 1.2666,
        1.2669, 1.2678, 1.2680, 1.2829, 1.2833, 1.2835, 1.2844, 1.2850, 1.2869,
        1.2884, 1.2899, 1.2902, 1.2915, 1.2929, 1.2972, 1.3012, 1.3055, 1.3141,
        1.3156, 1.3165, 1.3219, 1.3221, 1.3276, 1.3282, 1.3294, 1.3321, 1.3380,
        1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3885, 1.3891, 1.3940,
        1.3971, 1.4028, 1.4033, 1.4099, 1.4111, 1.4229, 1.4243, 1.4275, 1.4336,
        1.4377, 1.4407, 1.4419, 1.4432, 1.4436, 1.4455, 1.4585, 1.4591, 1.4625,
        1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897, 1.4975, 1.5089, 1.5392,
        1.5449, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5612, 1.5641, 1.5647,
        1.5696, 1.5702, 1.5746, 1.5939, 1.5953, 1.6031, 1.6174, 1.6871, 1.7110,
        1.7205, 1.7216, 1.7443, 1.7695, 1.8002, 1.8627, 1.8866])
2023-09-25 07:00:05,810 [INFO] - Instantiate Trainer
2023-09-25 07:00:05,810 [INFO] - Calling Trainer Fit
2023-09-25 07:00:05,810 [INFO] - Starting training, total number of epochs: 130
2023-09-25 07:00:05,810 [INFO] - Epoch: 1/130
2023-09-25 07:01:08,608 [INFO] - Training epoch stats:     Loss: 10.5380 - Binary-Cell-Dice: 0.6622 - Binary-Cell-Jacard: 0.5295 - Tissue-MC-Acc.: 0.1902
2023-09-25 07:02:09,205 [INFO] - Validation epoch stats:   Loss: 8.2323 - Binary-Cell-Dice: 0.7216 - Binary-Cell-Jacard: 0.5954 - bPQ-Score: 0.3352 - mPQ-Score: 0.1997 - Tissue-MC-Acc.: 0.3355
2023-09-25 07:02:09,207 [INFO] - New best model - save checkpoint
2023-09-25 07:05:26,633 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-25 07:05:26,678 [INFO] - Epoch: 2/130
2023-09-25 07:06:30,843 [INFO] - Training epoch stats:     Loss: 8.3316 - Binary-Cell-Dice: 0.7146 - Binary-Cell-Jacard: 0.5921 - Tissue-MC-Acc.: 0.2394
2023-09-25 07:07:30,850 [INFO] - Validation epoch stats:   Loss: 7.4829 - Binary-Cell-Dice: 0.7485 - Binary-Cell-Jacard: 0.6357 - bPQ-Score: 0.4347 - mPQ-Score: 0.2680 - Tissue-MC-Acc.: 0.3264
2023-09-25 07:07:30,852 [INFO] - New best model - save checkpoint
2023-09-25 07:10:36,430 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-25 07:10:36,474 [INFO] - Epoch: 3/130
2023-09-25 07:11:41,652 [INFO] - Training epoch stats:     Loss: 7.8820 - Binary-Cell-Dice: 0.7325 - Binary-Cell-Jacard: 0.6119 - Tissue-MC-Acc.: 0.2331
2023-09-25 07:12:43,817 [INFO] - Validation epoch stats:   Loss: 7.1002 - Binary-Cell-Dice: 0.7640 - Binary-Cell-Jacard: 0.6530 - bPQ-Score: 0.4581 - mPQ-Score: 0.2990 - Tissue-MC-Acc.: 0.3181
2023-09-25 07:12:43,819 [INFO] - New best model - save checkpoint
2023-09-25 07:16:02,547 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-25 07:16:02,615 [INFO] - Epoch: 4/130
2023-09-25 07:17:05,325 [INFO] - Training epoch stats:     Loss: 7.6818 - Binary-Cell-Dice: 0.7354 - Binary-Cell-Jacard: 0.6170 - Tissue-MC-Acc.: 0.2228
2023-09-25 07:18:07,273 [INFO] - Validation epoch stats:   Loss: 6.9818 - Binary-Cell-Dice: 0.7697 - Binary-Cell-Jacard: 0.6641 - bPQ-Score: 0.4782 - mPQ-Score: 0.3295 - Tissue-MC-Acc.: 0.3204
2023-09-25 07:18:07,276 [INFO] - New best model - save checkpoint
2023-09-25 07:21:24,613 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-25 07:21:24,659 [INFO] - Epoch: 5/130
2023-09-25 07:22:26,443 [INFO] - Training epoch stats:     Loss: 7.5312 - Binary-Cell-Dice: 0.7420 - Binary-Cell-Jacard: 0.6230 - Tissue-MC-Acc.: 0.2394
2023-09-25 07:23:29,030 [INFO] - Validation epoch stats:   Loss: 6.9090 - Binary-Cell-Dice: 0.7631 - Binary-Cell-Jacard: 0.6564 - bPQ-Score: 0.4800 - mPQ-Score: 0.3314 - Tissue-MC-Acc.: 0.3245
2023-09-25 07:23:29,033 [INFO] - New best model - save checkpoint
2023-09-25 07:26:08,891 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-25 07:26:08,932 [INFO] - Epoch: 6/130
2023-09-25 07:27:09,643 [INFO] - Training epoch stats:     Loss: 7.4583 - Binary-Cell-Dice: 0.7406 - Binary-Cell-Jacard: 0.6239 - Tissue-MC-Acc.: 0.2295
2023-09-25 07:28:13,621 [INFO] - Validation epoch stats:   Loss: 6.6384 - Binary-Cell-Dice: 0.7747 - Binary-Cell-Jacard: 0.6719 - bPQ-Score: 0.4917 - mPQ-Score: 0.3354 - Tissue-MC-Acc.: 0.3306
2023-09-25 07:28:13,660 [INFO] - New best model - save checkpoint
2023-09-25 07:30:53,572 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-25 07:30:53,573 [INFO] - Epoch: 7/130
2023-09-25 07:31:54,712 [INFO] - Training epoch stats:     Loss: 7.4076 - Binary-Cell-Dice: 0.7515 - Binary-Cell-Jacard: 0.6356 - Tissue-MC-Acc.: 0.2354
2023-09-25 07:33:08,871 [INFO] - Validation epoch stats:   Loss: 6.7821 - Binary-Cell-Dice: 0.7704 - Binary-Cell-Jacard: 0.6676 - bPQ-Score: 0.4883 - mPQ-Score: 0.3307 - Tissue-MC-Acc.: 0.3362
2023-09-25 07:34:15,956 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-25 07:34:15,957 [INFO] - Epoch: 8/130
2023-09-25 07:35:16,421 [INFO] - Training epoch stats:     Loss: 7.3068 - Binary-Cell-Dice: 0.7492 - Binary-Cell-Jacard: 0.6370 - Tissue-MC-Acc.: 0.2442
2023-09-25 07:36:23,982 [INFO] - Validation epoch stats:   Loss: 6.5983 - Binary-Cell-Dice: 0.7736 - Binary-Cell-Jacard: 0.6701 - bPQ-Score: 0.4854 - mPQ-Score: 0.3388 - Tissue-MC-Acc.: 0.3351
2023-09-25 07:37:34,569 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-25 07:37:34,570 [INFO] - Epoch: 9/130
2023-09-25 07:38:35,323 [INFO] - Training epoch stats:     Loss: 7.2362 - Binary-Cell-Dice: 0.7496 - Binary-Cell-Jacard: 0.6365 - Tissue-MC-Acc.: 0.2446
2023-09-25 07:39:54,433 [INFO] - Validation epoch stats:   Loss: 6.6455 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6753 - bPQ-Score: 0.5007 - mPQ-Score: 0.3536 - Tissue-MC-Acc.: 0.3351
2023-09-25 07:39:54,466 [INFO] - New best model - save checkpoint
2023-09-25 07:41:50,636 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-25 07:41:50,681 [INFO] - Epoch: 10/130
2023-09-25 07:42:56,721 [INFO] - Training epoch stats:     Loss: 7.1996 - Binary-Cell-Dice: 0.7488 - Binary-Cell-Jacard: 0.6406 - Tissue-MC-Acc.: 0.2509
2023-09-25 07:43:59,115 [INFO] - Validation epoch stats:   Loss: 6.5900 - Binary-Cell-Dice: 0.7758 - Binary-Cell-Jacard: 0.6760 - bPQ-Score: 0.4891 - mPQ-Score: 0.3465 - Tissue-MC-Acc.: 0.3358
2023-09-25 07:45:14,270 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-25 07:45:14,270 [INFO] - Epoch: 11/130
2023-09-25 07:46:16,611 [INFO] - Training epoch stats:     Loss: 7.1835 - Binary-Cell-Dice: 0.7477 - Binary-Cell-Jacard: 0.6403 - Tissue-MC-Acc.: 0.2295
2023-09-25 07:47:21,491 [INFO] - Validation epoch stats:   Loss: 6.5957 - Binary-Cell-Dice: 0.7787 - Binary-Cell-Jacard: 0.6783 - bPQ-Score: 0.5051 - mPQ-Score: 0.3583 - Tissue-MC-Acc.: 0.3370
2023-09-25 07:47:21,495 [INFO] - New best model - save checkpoint
2023-09-25 07:49:23,185 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-25 07:49:23,187 [INFO] - Epoch: 12/130
2023-09-25 07:50:23,743 [INFO] - Training epoch stats:     Loss: 7.0898 - Binary-Cell-Dice: 0.7582 - Binary-Cell-Jacard: 0.6491 - Tissue-MC-Acc.: 0.2446
2023-09-25 07:51:28,714 [INFO] - Validation epoch stats:   Loss: 6.6778 - Binary-Cell-Dice: 0.7790 - Binary-Cell-Jacard: 0.6781 - bPQ-Score: 0.5023 - mPQ-Score: 0.3545 - Tissue-MC-Acc.: 0.3366
2023-09-25 07:52:48,130 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-25 07:52:48,131 [INFO] - Epoch: 13/130
2023-09-25 07:53:51,316 [INFO] - Training epoch stats:     Loss: 7.1245 - Binary-Cell-Dice: 0.7608 - Binary-Cell-Jacard: 0.6490 - Tissue-MC-Acc.: 0.2449
2023-09-25 07:55:05,934 [INFO] - Validation epoch stats:   Loss: 6.5679 - Binary-Cell-Dice: 0.7800 - Binary-Cell-Jacard: 0.6792 - bPQ-Score: 0.4999 - mPQ-Score: 0.3483 - Tissue-MC-Acc.: 0.3381
2023-09-25 07:56:09,488 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-25 07:56:09,522 [INFO] - Epoch: 14/130
2023-09-25 07:57:14,397 [INFO] - Training epoch stats:     Loss: 7.0887 - Binary-Cell-Dice: 0.7656 - Binary-Cell-Jacard: 0.6526 - Tissue-MC-Acc.: 0.2315
2023-09-25 07:58:35,155 [INFO] - Validation epoch stats:   Loss: 6.4806 - Binary-Cell-Dice: 0.7776 - Binary-Cell-Jacard: 0.6770 - bPQ-Score: 0.4983 - mPQ-Score: 0.3490 - Tissue-MC-Acc.: 0.3381
2023-09-25 07:59:23,607 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-25 07:59:23,607 [INFO] - Epoch: 15/130
2023-09-25 08:00:24,489 [INFO] - Training epoch stats:     Loss: 7.0433 - Binary-Cell-Dice: 0.7571 - Binary-Cell-Jacard: 0.6486 - Tissue-MC-Acc.: 0.2418
2023-09-25 08:01:37,203 [INFO] - Validation epoch stats:   Loss: 6.6966 - Binary-Cell-Dice: 0.7724 - Binary-Cell-Jacard: 0.6715 - bPQ-Score: 0.4947 - mPQ-Score: 0.3435 - Tissue-MC-Acc.: 0.3385
2023-09-25 08:02:16,534 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-25 08:02:16,535 [INFO] - Epoch: 16/130
2023-09-25 08:03:17,875 [INFO] - Training epoch stats:     Loss: 6.9902 - Binary-Cell-Dice: 0.7592 - Binary-Cell-Jacard: 0.6510 - Tissue-MC-Acc.: 0.2453
2023-09-25 08:04:45,971 [INFO] - Validation epoch stats:   Loss: 6.4942 - Binary-Cell-Dice: 0.7771 - Binary-Cell-Jacard: 0.6743 - bPQ-Score: 0.5030 - mPQ-Score: 0.3603 - Tissue-MC-Acc.: 0.3381
2023-09-25 08:05:20,490 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-25 08:05:20,491 [INFO] - Epoch: 17/130
2023-09-25 08:06:20,549 [INFO] - Training epoch stats:     Loss: 6.9918 - Binary-Cell-Dice: 0.7519 - Binary-Cell-Jacard: 0.6421 - Tissue-MC-Acc.: 0.2398
2023-09-25 08:07:24,215 [INFO] - Validation epoch stats:   Loss: 6.5609 - Binary-Cell-Dice: 0.7790 - Binary-Cell-Jacard: 0.6834 - bPQ-Score: 0.5095 - mPQ-Score: 0.3607 - Tissue-MC-Acc.: 0.3392
2023-09-25 08:07:24,262 [INFO] - New best model - save checkpoint
2023-09-25 08:09:23,685 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-25 08:09:23,688 [INFO] - Epoch: 18/130
2023-09-25 08:10:24,979 [INFO] - Training epoch stats:     Loss: 7.0455 - Binary-Cell-Dice: 0.7631 - Binary-Cell-Jacard: 0.6549 - Tissue-MC-Acc.: 0.2362
2023-09-25 08:11:27,664 [INFO] - Validation epoch stats:   Loss: 6.3938 - Binary-Cell-Dice: 0.7832 - Binary-Cell-Jacard: 0.6871 - bPQ-Score: 0.5074 - mPQ-Score: 0.3653 - Tissue-MC-Acc.: 0.3389
2023-09-25 08:12:14,166 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-25 08:12:14,167 [INFO] - Epoch: 19/130
2023-09-25 08:13:15,071 [INFO] - Training epoch stats:     Loss: 6.9143 - Binary-Cell-Dice: 0.7572 - Binary-Cell-Jacard: 0.6515 - Tissue-MC-Acc.: 0.2556
2023-09-25 08:14:18,542 [INFO] - Validation epoch stats:   Loss: 6.3222 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.6910 - bPQ-Score: 0.5071 - mPQ-Score: 0.3617 - Tissue-MC-Acc.: 0.3426
2023-09-25 08:15:35,011 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-25 08:15:35,012 [INFO] - Epoch: 20/130
2023-09-25 08:16:41,313 [INFO] - Training epoch stats:     Loss: 6.9157 - Binary-Cell-Dice: 0.7565 - Binary-Cell-Jacard: 0.6509 - Tissue-MC-Acc.: 0.2604
2023-09-25 08:17:45,432 [INFO] - Validation epoch stats:   Loss: 6.4202 - Binary-Cell-Dice: 0.7827 - Binary-Cell-Jacard: 0.6859 - bPQ-Score: 0.5106 - mPQ-Score: 0.3708 - Tissue-MC-Acc.: 0.3400
2023-09-25 08:17:45,435 [INFO] - New best model - save checkpoint
2023-09-25 08:20:05,907 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-25 08:20:05,955 [INFO] - Epoch: 21/130
2023-09-25 08:21:08,176 [INFO] - Training epoch stats:     Loss: 6.8527 - Binary-Cell-Dice: 0.7662 - Binary-Cell-Jacard: 0.6576 - Tissue-MC-Acc.: 0.2549
2023-09-25 08:22:12,483 [INFO] - Validation epoch stats:   Loss: 6.4343 - Binary-Cell-Dice: 0.7804 - Binary-Cell-Jacard: 0.6816 - bPQ-Score: 0.5053 - mPQ-Score: 0.3599 - Tissue-MC-Acc.: 0.3419
2023-09-25 08:23:50,648 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-25 08:23:50,686 [INFO] - Epoch: 22/130
2023-09-25 08:24:55,580 [INFO] - Training epoch stats:     Loss: 6.9397 - Binary-Cell-Dice: 0.7588 - Binary-Cell-Jacard: 0.6560 - Tissue-MC-Acc.: 0.2525
2023-09-25 08:25:58,731 [INFO] - Validation epoch stats:   Loss: 6.3499 - Binary-Cell-Dice: 0.7813 - Binary-Cell-Jacard: 0.6820 - bPQ-Score: 0.5131 - mPQ-Score: 0.3730 - Tissue-MC-Acc.: 0.3404
2023-09-25 08:25:58,733 [INFO] - New best model - save checkpoint
2023-09-25 08:28:24,352 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-25 08:28:24,354 [INFO] - Epoch: 23/130
2023-09-25 08:29:25,357 [INFO] - Training epoch stats:     Loss: 6.8835 - Binary-Cell-Dice: 0.7616 - Binary-Cell-Jacard: 0.6569 - Tissue-MC-Acc.: 0.2549
2023-09-25 08:30:39,818 [INFO] - Validation epoch stats:   Loss: 6.3854 - Binary-Cell-Dice: 0.7814 - Binary-Cell-Jacard: 0.6822 - bPQ-Score: 0.5053 - mPQ-Score: 0.3636 - Tissue-MC-Acc.: 0.3404
2023-09-25 08:31:47,116 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-25 08:31:47,117 [INFO] - Epoch: 24/130
2023-09-25 08:32:46,658 [INFO] - Training epoch stats:     Loss: 6.8675 - Binary-Cell-Dice: 0.7626 - Binary-Cell-Jacard: 0.6630 - Tissue-MC-Acc.: 0.2434
2023-09-25 08:33:53,621 [INFO] - Validation epoch stats:   Loss: 6.5251 - Binary-Cell-Dice: 0.7800 - Binary-Cell-Jacard: 0.6839 - bPQ-Score: 0.5057 - mPQ-Score: 0.3681 - Tissue-MC-Acc.: 0.3396
2023-09-25 08:35:07,664 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-25 08:35:07,665 [INFO] - Epoch: 25/130
2023-09-25 08:36:07,756 [INFO] - Training epoch stats:     Loss: 6.7793 - Binary-Cell-Dice: 0.7657 - Binary-Cell-Jacard: 0.6632 - Tissue-MC-Acc.: 0.2525
2023-09-25 08:37:12,394 [INFO] - Validation epoch stats:   Loss: 6.4022 - Binary-Cell-Dice: 0.7820 - Binary-Cell-Jacard: 0.6878 - bPQ-Score: 0.5092 - mPQ-Score: 0.3696 - Tissue-MC-Acc.: 0.3430
2023-09-25 08:38:10,713 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-25 08:38:10,714 [INFO] - Epoch: 26/130
2023-09-25 08:39:56,546 [INFO] - Training epoch stats:     Loss: 7.1168 - Binary-Cell-Dice: 0.7494 - Binary-Cell-Jacard: 0.6451 - Tissue-MC-Acc.: 0.2798
2023-09-25 08:41:13,949 [INFO] - Validation epoch stats:   Loss: 6.4604 - Binary-Cell-Dice: 0.7825 - Binary-Cell-Jacard: 0.6816 - bPQ-Score: 0.5012 - mPQ-Score: 0.3443 - Tissue-MC-Acc.: 0.4428
2023-09-25 08:42:59,186 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-25 08:42:59,190 [INFO] - Epoch: 27/130
2023-09-25 08:44:41,895 [INFO] - Training epoch stats:     Loss: 6.8643 - Binary-Cell-Dice: 0.7627 - Binary-Cell-Jacard: 0.6549 - Tissue-MC-Acc.: 0.3753
2023-09-25 08:46:00,986 [INFO] - Validation epoch stats:   Loss: 6.3768 - Binary-Cell-Dice: 0.7825 - Binary-Cell-Jacard: 0.6861 - bPQ-Score: 0.5112 - mPQ-Score: 0.3635 - Tissue-MC-Acc.: 0.4985
2023-09-25 08:47:35,017 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-25 08:47:35,019 [INFO] - Epoch: 28/130
2023-09-25 08:49:18,365 [INFO] - Training epoch stats:     Loss: 6.7794 - Binary-Cell-Dice: 0.7641 - Binary-Cell-Jacard: 0.6594 - Tissue-MC-Acc.: 0.4336
2023-09-25 08:50:24,386 [INFO] - Validation epoch stats:   Loss: 6.2729 - Binary-Cell-Dice: 0.7843 - Binary-Cell-Jacard: 0.6864 - bPQ-Score: 0.5105 - mPQ-Score: 0.3676 - Tissue-MC-Acc.: 0.5218
2023-09-25 08:52:47,159 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-25 08:52:47,188 [INFO] - Epoch: 29/130
2023-09-25 08:54:35,892 [INFO] - Training epoch stats:     Loss: 6.7212 - Binary-Cell-Dice: 0.7641 - Binary-Cell-Jacard: 0.6593 - Tissue-MC-Acc.: 0.4546
2023-09-25 08:55:41,599 [INFO] - Validation epoch stats:   Loss: 6.2877 - Binary-Cell-Dice: 0.7848 - Binary-Cell-Jacard: 0.6896 - bPQ-Score: 0.5057 - mPQ-Score: 0.3659 - Tissue-MC-Acc.: 0.5331
2023-09-25 08:59:30,204 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-25 08:59:30,276 [INFO] - Epoch: 30/130
2023-09-25 09:01:19,792 [INFO] - Training epoch stats:     Loss: 6.6132 - Binary-Cell-Dice: 0.7666 - Binary-Cell-Jacard: 0.6669 - Tissue-MC-Acc.: 0.5042
2023-09-25 09:02:37,378 [INFO] - Validation epoch stats:   Loss: 6.2725 - Binary-Cell-Dice: 0.7845 - Binary-Cell-Jacard: 0.6930 - bPQ-Score: 0.5112 - mPQ-Score: 0.3634 - Tissue-MC-Acc.: 0.5712
2023-09-25 09:05:32,164 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-25 09:05:32,214 [INFO] - Epoch: 31/130
2023-09-25 09:07:17,415 [INFO] - Training epoch stats:     Loss: 6.5658 - Binary-Cell-Dice: 0.7685 - Binary-Cell-Jacard: 0.6696 - Tissue-MC-Acc.: 0.5371
2023-09-25 09:08:32,041 [INFO] - Validation epoch stats:   Loss: 6.2575 - Binary-Cell-Dice: 0.7880 - Binary-Cell-Jacard: 0.6982 - bPQ-Score: 0.5124 - mPQ-Score: 0.3696 - Tissue-MC-Acc.: 0.5606
2023-09-25 09:12:14,380 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-25 09:12:14,418 [INFO] - Epoch: 32/130
2023-09-25 09:14:01,813 [INFO] - Training epoch stats:     Loss: 6.5422 - Binary-Cell-Dice: 0.7782 - Binary-Cell-Jacard: 0.6752 - Tissue-MC-Acc.: 0.5569
2023-09-25 09:15:05,152 [INFO] - Validation epoch stats:   Loss: 6.1994 - Binary-Cell-Dice: 0.7876 - Binary-Cell-Jacard: 0.6954 - bPQ-Score: 0.5181 - mPQ-Score: 0.3808 - Tissue-MC-Acc.: 0.5621
2023-09-25 09:15:05,154 [INFO] - New best model - save checkpoint
2023-09-25 09:20:56,420 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-25 09:20:56,421 [INFO] - Epoch: 33/130
2023-09-25 09:22:39,196 [INFO] - Training epoch stats:     Loss: 6.4795 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6748 - Tissue-MC-Acc.: 0.5977
2023-09-25 09:23:42,896 [INFO] - Validation epoch stats:   Loss: 6.2867 - Binary-Cell-Dice: 0.7833 - Binary-Cell-Jacard: 0.6912 - bPQ-Score: 0.5051 - mPQ-Score: 0.3719 - Tissue-MC-Acc.: 0.6269
2023-09-25 09:31:23,560 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-25 09:31:23,609 [INFO] - Epoch: 34/130
2023-09-25 09:33:50,393 [INFO] - Training epoch stats:     Loss: 6.3553 - Binary-Cell-Dice: 0.7798 - Binary-Cell-Jacard: 0.6821 - Tissue-MC-Acc.: 0.6231
2023-09-25 09:35:02,754 [INFO] - Validation epoch stats:   Loss: 6.1860 - Binary-Cell-Dice: 0.7888 - Binary-Cell-Jacard: 0.7012 - bPQ-Score: 0.5170 - mPQ-Score: 0.3714 - Tissue-MC-Acc.: 0.6322
2023-09-25 09:36:41,529 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-25 09:36:41,566 [INFO] - Epoch: 35/130
2023-09-25 09:38:21,914 [INFO] - Training epoch stats:     Loss: 6.3299 - Binary-Cell-Dice: 0.7829 - Binary-Cell-Jacard: 0.6832 - Tissue-MC-Acc.: 0.6373
2023-09-25 09:39:39,413 [INFO] - Validation epoch stats:   Loss: 6.1776 - Binary-Cell-Dice: 0.7906 - Binary-Cell-Jacard: 0.7018 - bPQ-Score: 0.5179 - mPQ-Score: 0.3696 - Tissue-MC-Acc.: 0.6352
2023-09-25 09:42:07,279 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-25 09:42:07,281 [INFO] - Epoch: 36/130
2023-09-25 09:43:51,301 [INFO] - Training epoch stats:     Loss: 6.3040 - Binary-Cell-Dice: 0.7814 - Binary-Cell-Jacard: 0.6824 - Tissue-MC-Acc.: 0.6536
2023-09-25 09:45:32,518 [INFO] - Validation epoch stats:   Loss: 6.1037 - Binary-Cell-Dice: 0.7875 - Binary-Cell-Jacard: 0.6991 - bPQ-Score: 0.5159 - mPQ-Score: 0.3791 - Tissue-MC-Acc.: 0.6450
2023-09-25 09:47:51,711 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-25 09:47:51,720 [INFO] - Epoch: 37/130
2023-09-25 09:49:35,841 [INFO] - Training epoch stats:     Loss: 6.2375 - Binary-Cell-Dice: 0.7917 - Binary-Cell-Jacard: 0.6898 - Tissue-MC-Acc.: 0.6829
2023-09-25 09:51:04,629 [INFO] - Validation epoch stats:   Loss: 6.2471 - Binary-Cell-Dice: 0.7848 - Binary-Cell-Jacard: 0.6933 - bPQ-Score: 0.5124 - mPQ-Score: 0.3775 - Tissue-MC-Acc.: 0.6480
2023-09-25 10:38:54,181 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-25 10:38:54,186 [INFO] - Epoch: 38/130
2023-09-25 10:40:36,484 [INFO] - Training epoch stats:     Loss: 6.2515 - Binary-Cell-Dice: 0.7833 - Binary-Cell-Jacard: 0.6892 - Tissue-MC-Acc.: 0.6968
2023-09-25 10:41:40,731 [INFO] - Validation epoch stats:   Loss: 6.1324 - Binary-Cell-Dice: 0.7905 - Binary-Cell-Jacard: 0.7001 - bPQ-Score: 0.5206 - mPQ-Score: 0.3870 - Tissue-MC-Acc.: 0.6608
2023-09-25 10:41:40,735 [INFO] - New best model - save checkpoint
2023-09-25 10:50:32,444 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-25 10:50:32,512 [INFO] - Epoch: 39/130
2023-09-25 10:52:11,681 [INFO] - Training epoch stats:     Loss: 6.1670 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.6915 - Tissue-MC-Acc.: 0.7273
2023-09-25 10:53:16,411 [INFO] - Validation epoch stats:   Loss: 6.0114 - Binary-Cell-Dice: 0.7921 - Binary-Cell-Jacard: 0.7082 - bPQ-Score: 0.5274 - mPQ-Score: 0.3842 - Tissue-MC-Acc.: 0.6860
2023-09-25 10:53:16,416 [INFO] - New best model - save checkpoint
2023-09-25 10:58:59,416 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-25 10:58:59,418 [INFO] - Epoch: 40/130
2023-09-25 11:00:43,778 [INFO] - Training epoch stats:     Loss: 6.2102 - Binary-Cell-Dice: 0.7859 - Binary-Cell-Jacard: 0.6864 - Tissue-MC-Acc.: 0.7432
2023-09-25 11:02:00,641 [INFO] - Validation epoch stats:   Loss: 6.0474 - Binary-Cell-Dice: 0.7922 - Binary-Cell-Jacard: 0.7023 - bPQ-Score: 0.5229 - mPQ-Score: 0.3905 - Tissue-MC-Acc.: 0.7029
2023-09-25 11:03:54,891 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-25 11:03:54,893 [INFO] - Epoch: 41/130
2023-09-25 11:05:39,147 [INFO] - Training epoch stats:     Loss: 6.0541 - Binary-Cell-Dice: 0.7903 - Binary-Cell-Jacard: 0.6978 - Tissue-MC-Acc.: 0.7574
2023-09-25 11:06:54,587 [INFO] - Validation epoch stats:   Loss: 6.0083 - Binary-Cell-Dice: 0.7936 - Binary-Cell-Jacard: 0.7043 - bPQ-Score: 0.5294 - mPQ-Score: 0.3975 - Tissue-MC-Acc.: 0.7157
2023-09-25 11:06:54,590 [INFO] - New best model - save checkpoint
2023-09-25 11:14:21,581 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-25 11:14:21,583 [INFO] - Epoch: 42/130
2023-09-25 11:16:04,511 [INFO] - Training epoch stats:     Loss: 6.0248 - Binary-Cell-Dice: 0.7915 - Binary-Cell-Jacard: 0.6992 - Tissue-MC-Acc.: 0.7927
2023-09-25 11:17:10,708 [INFO] - Validation epoch stats:   Loss: 6.0468 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7081 - bPQ-Score: 0.5288 - mPQ-Score: 0.3934 - Tissue-MC-Acc.: 0.7252
2023-09-25 11:20:54,041 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-25 11:20:54,073 [INFO] - Epoch: 43/130
2023-09-25 11:22:40,272 [INFO] - Training epoch stats:     Loss: 5.9936 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7010 - Tissue-MC-Acc.: 0.8046
2023-09-25 11:23:43,861 [INFO] - Validation epoch stats:   Loss: 6.0519 - Binary-Cell-Dice: 0.7893 - Binary-Cell-Jacard: 0.7042 - bPQ-Score: 0.5217 - mPQ-Score: 0.3904 - Tissue-MC-Acc.: 0.7327
2023-09-25 11:27:57,808 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-25 11:27:57,870 [INFO] - Epoch: 44/130
2023-09-25 11:29:45,801 [INFO] - Training epoch stats:     Loss: 5.9655 - Binary-Cell-Dice: 0.7866 - Binary-Cell-Jacard: 0.6999 - Tissue-MC-Acc.: 0.8319
2023-09-25 11:30:53,328 [INFO] - Validation epoch stats:   Loss: 6.0059 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7077 - bPQ-Score: 0.5270 - mPQ-Score: 0.3985 - Tissue-MC-Acc.: 0.7485
2023-09-25 11:33:56,606 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-25 11:33:56,655 [INFO] - Epoch: 45/130
2023-09-25 11:35:40,645 [INFO] - Training epoch stats:     Loss: 5.9544 - Binary-Cell-Dice: 0.7946 - Binary-Cell-Jacard: 0.7030 - Tissue-MC-Acc.: 0.8450
2023-09-25 11:37:04,216 [INFO] - Validation epoch stats:   Loss: 5.9963 - Binary-Cell-Dice: 0.7943 - Binary-Cell-Jacard: 0.7081 - bPQ-Score: 0.5270 - mPQ-Score: 0.3932 - Tissue-MC-Acc.: 0.7696
2023-09-25 11:38:47,692 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-25 11:38:47,695 [INFO] - Epoch: 46/130
2023-09-25 11:40:28,308 [INFO] - Training epoch stats:     Loss: 5.9225 - Binary-Cell-Dice: 0.7884 - Binary-Cell-Jacard: 0.7004 - Tissue-MC-Acc.: 0.8831
2023-09-25 11:41:53,038 [INFO] - Validation epoch stats:   Loss: 6.0017 - Binary-Cell-Dice: 0.7913 - Binary-Cell-Jacard: 0.7056 - bPQ-Score: 0.5280 - mPQ-Score: 0.3988 - Tissue-MC-Acc.: 0.7639
2023-09-25 11:44:44,966 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-25 11:44:45,012 [INFO] - Epoch: 47/130
2023-09-25 11:46:33,660 [INFO] - Training epoch stats:     Loss: 5.9139 - Binary-Cell-Dice: 0.7953 - Binary-Cell-Jacard: 0.7049 - Tissue-MC-Acc.: 0.8823
2023-09-25 11:47:49,187 [INFO] - Validation epoch stats:   Loss: 6.0057 - Binary-Cell-Dice: 0.7923 - Binary-Cell-Jacard: 0.7084 - bPQ-Score: 0.5263 - mPQ-Score: 0.3932 - Tissue-MC-Acc.: 0.7816
2023-09-25 11:50:20,739 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-25 11:50:20,776 [INFO] - Epoch: 48/130
2023-09-25 11:52:07,413 [INFO] - Training epoch stats:     Loss: 5.8613 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7064 - Tissue-MC-Acc.: 0.9092
2023-09-25 11:53:10,587 [INFO] - Validation epoch stats:   Loss: 5.9762 - Binary-Cell-Dice: 0.7939 - Binary-Cell-Jacard: 0.7073 - bPQ-Score: 0.5311 - mPQ-Score: 0.4031 - Tissue-MC-Acc.: 0.7884
2023-09-25 11:53:10,590 [INFO] - New best model - save checkpoint
2023-09-25 11:59:51,973 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-25 11:59:52,022 [INFO] - Epoch: 49/130
2023-09-25 12:01:34,523 [INFO] - Training epoch stats:     Loss: 5.8091 - Binary-Cell-Dice: 0.7955 - Binary-Cell-Jacard: 0.7108 - Tissue-MC-Acc.: 0.9160
2023-09-25 12:02:37,701 [INFO] - Validation epoch stats:   Loss: 6.0199 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7062 - bPQ-Score: 0.5278 - mPQ-Score: 0.3992 - Tissue-MC-Acc.: 0.8061
2023-09-25 12:05:28,026 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-25 12:05:28,086 [INFO] - Epoch: 50/130
2023-09-25 12:07:10,650 [INFO] - Training epoch stats:     Loss: 5.8053 - Binary-Cell-Dice: 0.7933 - Binary-Cell-Jacard: 0.7154 - Tissue-MC-Acc.: 0.9187
2023-09-25 12:08:30,410 [INFO] - Validation epoch stats:   Loss: 6.0288 - Binary-Cell-Dice: 0.7909 - Binary-Cell-Jacard: 0.7085 - bPQ-Score: 0.5278 - mPQ-Score: 0.4000 - Tissue-MC-Acc.: 0.7974
2023-09-25 12:09:55,905 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-25 12:09:55,905 [INFO] - Epoch: 51/130
2023-09-25 12:11:40,472 [INFO] - Training epoch stats:     Loss: 5.7615 - Binary-Cell-Dice: 0.7939 - Binary-Cell-Jacard: 0.7112 - Tissue-MC-Acc.: 0.9382
2023-09-25 12:12:58,583 [INFO] - Validation epoch stats:   Loss: 5.9681 - Binary-Cell-Dice: 0.7943 - Binary-Cell-Jacard: 0.7063 - bPQ-Score: 0.5305 - mPQ-Score: 0.4020 - Tissue-MC-Acc.: 0.8155
2023-09-25 12:14:57,801 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-25 12:14:57,803 [INFO] - Epoch: 52/130
2023-09-25 12:16:39,215 [INFO] - Training epoch stats:     Loss: 5.7317 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7140 - Tissue-MC-Acc.: 0.9469
2023-09-25 12:17:42,519 [INFO] - Validation epoch stats:   Loss: 5.9798 - Binary-Cell-Dice: 0.7929 - Binary-Cell-Jacard: 0.7099 - bPQ-Score: 0.5321 - mPQ-Score: 0.4007 - Tissue-MC-Acc.: 0.8110
2023-09-25 12:17:42,566 [INFO] - New best model - save checkpoint
2023-09-25 12:24:14,588 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-25 12:24:14,600 [INFO] - Epoch: 53/130
2023-09-25 12:25:57,258 [INFO] - Training epoch stats:     Loss: 5.7570 - Binary-Cell-Dice: 0.7979 - Binary-Cell-Jacard: 0.7119 - Tissue-MC-Acc.: 0.9469
2023-09-25 12:27:00,839 [INFO] - Validation epoch stats:   Loss: 6.0226 - Binary-Cell-Dice: 0.7935 - Binary-Cell-Jacard: 0.7070 - bPQ-Score: 0.5344 - mPQ-Score: 0.4046 - Tissue-MC-Acc.: 0.8125
2023-09-25 12:27:00,841 [INFO] - New best model - save checkpoint
2023-09-25 12:32:52,968 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-25 12:32:52,971 [INFO] - Epoch: 54/130
2023-09-25 12:34:35,414 [INFO] - Training epoch stats:     Loss: 5.7394 - Binary-Cell-Dice: 0.8053 - Binary-Cell-Jacard: 0.7230 - Tissue-MC-Acc.: 0.9540
2023-09-25 12:36:01,414 [INFO] - Validation epoch stats:   Loss: 5.9502 - Binary-Cell-Dice: 0.7945 - Binary-Cell-Jacard: 0.7098 - bPQ-Score: 0.5329 - mPQ-Score: 0.3993 - Tissue-MC-Acc.: 0.8189
2023-09-25 12:41:33,145 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-25 12:41:33,182 [INFO] - Epoch: 55/130
2023-09-25 12:43:19,340 [INFO] - Training epoch stats:     Loss: 5.7382 - Binary-Cell-Dice: 0.8042 - Binary-Cell-Jacard: 0.7211 - Tissue-MC-Acc.: 0.9528
2023-09-25 12:44:39,654 [INFO] - Validation epoch stats:   Loss: 5.9634 - Binary-Cell-Dice: 0.7946 - Binary-Cell-Jacard: 0.7102 - bPQ-Score: 0.5304 - mPQ-Score: 0.3941 - Tissue-MC-Acc.: 0.8245
2023-09-25 12:51:36,950 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-25 12:51:37,113 [INFO] - Epoch: 56/130
2023-09-25 12:53:25,975 [INFO] - Training epoch stats:     Loss: 5.7012 - Binary-Cell-Dice: 0.8096 - Binary-Cell-Jacard: 0.7225 - Tissue-MC-Acc.: 0.9663
2023-09-25 12:57:30,126 [INFO] - Validation epoch stats:   Loss: 5.9617 - Binary-Cell-Dice: 0.7953 - Binary-Cell-Jacard: 0.7104 - bPQ-Score: 0.5328 - mPQ-Score: 0.4013 - Tissue-MC-Acc.: 0.8238
2023-09-25 13:15:23,296 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-25 13:15:23,314 [INFO] - Epoch: 57/130
2023-09-25 13:17:02,229 [INFO] - Training epoch stats:     Loss: 5.5830 - Binary-Cell-Dice: 0.8056 - Binary-Cell-Jacard: 0.7232 - Tissue-MC-Acc.: 0.9687
2023-09-25 13:18:21,111 [INFO] - Validation epoch stats:   Loss: 5.9503 - Binary-Cell-Dice: 0.7954 - Binary-Cell-Jacard: 0.7097 - bPQ-Score: 0.5338 - mPQ-Score: 0.4048 - Tissue-MC-Acc.: 0.8336
2023-09-25 13:21:37,670 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-25 13:21:37,678 [INFO] - Epoch: 58/130
2023-09-25 13:23:17,448 [INFO] - Training epoch stats:     Loss: 5.6038 - Binary-Cell-Dice: 0.8007 - Binary-Cell-Jacard: 0.7200 - Tissue-MC-Acc.: 0.9588
2023-09-25 13:24:23,637 [INFO] - Validation epoch stats:   Loss: 5.9633 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7075 - bPQ-Score: 0.5323 - mPQ-Score: 0.4057 - Tissue-MC-Acc.: 0.8389
2023-09-25 13:29:16,246 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-25 13:29:16,257 [INFO] - Epoch: 59/130
2023-09-25 13:30:56,001 [INFO] - Training epoch stats:     Loss: 5.5976 - Binary-Cell-Dice: 0.8138 - Binary-Cell-Jacard: 0.7293 - Tissue-MC-Acc.: 0.9707
2023-09-25 13:32:02,806 [INFO] - Validation epoch stats:   Loss: 5.9724 - Binary-Cell-Dice: 0.7952 - Binary-Cell-Jacard: 0.7094 - bPQ-Score: 0.5323 - mPQ-Score: 0.4041 - Tissue-MC-Acc.: 0.8471
2023-09-25 13:36:13,718 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-25 13:36:13,725 [INFO] - Epoch: 60/130
2023-09-25 13:38:02,202 [INFO] - Training epoch stats:     Loss: 5.5711 - Binary-Cell-Dice: 0.8063 - Binary-Cell-Jacard: 0.7228 - Tissue-MC-Acc.: 0.9782
2023-09-25 13:39:08,236 [INFO] - Validation epoch stats:   Loss: 5.9431 - Binary-Cell-Dice: 0.7956 - Binary-Cell-Jacard: 0.7103 - bPQ-Score: 0.5353 - mPQ-Score: 0.4039 - Tissue-MC-Acc.: 0.8502
2023-09-25 13:39:08,244 [INFO] - New best model - save checkpoint
2023-09-25 13:47:12,680 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-25 13:47:12,690 [INFO] - Epoch: 61/130
2023-09-25 13:48:51,222 [INFO] - Training epoch stats:     Loss: 5.5856 - Binary-Cell-Dice: 0.8035 - Binary-Cell-Jacard: 0.7234 - Tissue-MC-Acc.: 0.9778
2023-09-25 13:50:11,039 [INFO] - Validation epoch stats:   Loss: 6.0012 - Binary-Cell-Dice: 0.7933 - Binary-Cell-Jacard: 0.7068 - bPQ-Score: 0.5315 - mPQ-Score: 0.4045 - Tissue-MC-Acc.: 0.8517
2023-09-25 13:52:40,970 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-25 13:52:40,971 [INFO] - Epoch: 62/130
2023-09-25 13:54:22,051 [INFO] - Training epoch stats:     Loss: 5.5973 - Binary-Cell-Dice: 0.8082 - Binary-Cell-Jacard: 0.7239 - Tissue-MC-Acc.: 0.9810
2023-09-25 13:55:27,372 [INFO] - Validation epoch stats:   Loss: 5.9824 - Binary-Cell-Dice: 0.7931 - Binary-Cell-Jacard: 0.7095 - bPQ-Score: 0.5314 - mPQ-Score: 0.3996 - Tissue-MC-Acc.: 0.8558
2023-09-25 13:58:03,287 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-25 13:58:03,348 [INFO] - Epoch: 63/130
2023-09-25 13:59:45,946 [INFO] - Training epoch stats:     Loss: 5.5353 - Binary-Cell-Dice: 0.8030 - Binary-Cell-Jacard: 0.7258 - Tissue-MC-Acc.: 0.9841
2023-09-25 14:00:52,451 [INFO] - Validation epoch stats:   Loss: 5.9640 - Binary-Cell-Dice: 0.7944 - Binary-Cell-Jacard: 0.7118 - bPQ-Score: 0.5355 - mPQ-Score: 0.4061 - Tissue-MC-Acc.: 0.8422
2023-09-25 14:00:52,453 [INFO] - New best model - save checkpoint
2023-09-25 14:07:12,550 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-25 14:07:12,553 [INFO] - Epoch: 64/130
2023-09-25 14:08:54,541 [INFO] - Training epoch stats:     Loss: 5.5417 - Binary-Cell-Dice: 0.8093 - Binary-Cell-Jacard: 0.7291 - Tissue-MC-Acc.: 0.9786
2023-09-25 14:09:59,941 [INFO] - Validation epoch stats:   Loss: 5.9889 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7089 - bPQ-Score: 0.5305 - mPQ-Score: 0.4010 - Tissue-MC-Acc.: 0.8562
2023-09-25 14:14:25,063 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-25 14:14:25,069 [INFO] - Epoch: 65/130
2023-09-25 14:16:06,373 [INFO] - Training epoch stats:     Loss: 5.4904 - Binary-Cell-Dice: 0.8053 - Binary-Cell-Jacard: 0.7329 - Tissue-MC-Acc.: 0.9845
2023-09-25 14:17:12,667 [INFO] - Validation epoch stats:   Loss: 6.0224 - Binary-Cell-Dice: 0.7932 - Binary-Cell-Jacard: 0.7088 - bPQ-Score: 0.5325 - mPQ-Score: 0.4012 - Tissue-MC-Acc.: 0.8554
2023-09-25 14:24:50,075 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-25 14:24:50,081 [INFO] - Epoch: 66/130
2023-09-25 14:26:31,664 [INFO] - Training epoch stats:     Loss: 5.5224 - Binary-Cell-Dice: 0.8046 - Binary-Cell-Jacard: 0.7271 - Tissue-MC-Acc.: 0.9814
2023-09-25 14:27:38,165 [INFO] - Validation epoch stats:   Loss: 5.9366 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7135 - bPQ-Score: 0.5375 - mPQ-Score: 0.4079 - Tissue-MC-Acc.: 0.8584
2023-09-25 14:27:38,176 [INFO] - New best model - save checkpoint
2023-09-25 14:35:37,723 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-25 14:35:37,728 [INFO] - Epoch: 67/130
2023-09-25 14:37:16,857 [INFO] - Training epoch stats:     Loss: 5.5308 - Binary-Cell-Dice: 0.8046 - Binary-Cell-Jacard: 0.7233 - Tissue-MC-Acc.: 0.9865
2023-09-25 14:39:40,411 [INFO] - Validation epoch stats:   Loss: 5.9820 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7090 - bPQ-Score: 0.5325 - mPQ-Score: 0.4001 - Tissue-MC-Acc.: 0.8584
2023-09-25 14:41:09,666 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-25 14:41:09,666 [INFO] - Epoch: 68/130
2023-09-25 14:42:49,082 [INFO] - Training epoch stats:     Loss: 5.4473 - Binary-Cell-Dice: 0.8072 - Binary-Cell-Jacard: 0.7325 - Tissue-MC-Acc.: 0.9909
2023-09-25 14:45:19,173 [INFO] - Validation epoch stats:   Loss: 5.9726 - Binary-Cell-Dice: 0.7953 - Binary-Cell-Jacard: 0.7098 - bPQ-Score: 0.5326 - mPQ-Score: 0.4038 - Tissue-MC-Acc.: 0.8614
2023-09-25 14:46:46,012 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-25 14:46:46,020 [INFO] - Epoch: 69/130
2023-09-25 14:48:26,974 [INFO] - Training epoch stats:     Loss: 5.4843 - Binary-Cell-Dice: 0.8059 - Binary-Cell-Jacard: 0.7309 - Tissue-MC-Acc.: 0.9853
2023-09-25 14:49:44,985 [INFO] - Validation epoch stats:   Loss: 5.9733 - Binary-Cell-Dice: 0.7941 - Binary-Cell-Jacard: 0.7098 - bPQ-Score: 0.5337 - mPQ-Score: 0.4057 - Tissue-MC-Acc.: 0.8588
2023-09-25 14:52:11,849 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-25 14:52:11,853 [INFO] - Epoch: 70/130
2023-09-25 14:53:51,790 [INFO] - Training epoch stats:     Loss: 5.4367 - Binary-Cell-Dice: 0.8077 - Binary-Cell-Jacard: 0.7305 - Tissue-MC-Acc.: 0.9849
2023-09-25 14:54:59,865 [INFO] - Validation epoch stats:   Loss: 5.9803 - Binary-Cell-Dice: 0.7960 - Binary-Cell-Jacard: 0.7101 - bPQ-Score: 0.5349 - mPQ-Score: 0.4046 - Tissue-MC-Acc.: 0.8596
2023-09-25 14:59:17,833 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-25 14:59:17,836 [INFO] - Epoch: 71/130
2023-09-25 15:01:02,539 [INFO] - Training epoch stats:     Loss: 5.4594 - Binary-Cell-Dice: 0.8097 - Binary-Cell-Jacard: 0.7324 - Tissue-MC-Acc.: 0.9889
2023-09-25 15:02:08,639 [INFO] - Validation epoch stats:   Loss: 6.0135 - Binary-Cell-Dice: 0.7933 - Binary-Cell-Jacard: 0.7078 - bPQ-Score: 0.5324 - mPQ-Score: 0.4019 - Tissue-MC-Acc.: 0.8584
2023-09-25 15:06:27,409 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-25 15:06:27,450 [INFO] - Epoch: 72/130
2023-09-25 15:08:12,956 [INFO] - Training epoch stats:     Loss: 5.4460 - Binary-Cell-Dice: 0.8085 - Binary-Cell-Jacard: 0.7334 - Tissue-MC-Acc.: 0.9893
2023-09-25 15:09:19,661 [INFO] - Validation epoch stats:   Loss: 5.9959 - Binary-Cell-Dice: 0.7934 - Binary-Cell-Jacard: 0.7082 - bPQ-Score: 0.5336 - mPQ-Score: 0.4066 - Tissue-MC-Acc.: 0.8637
2023-09-25 15:13:58,073 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-25 15:13:58,120 [INFO] - Epoch: 73/130
2023-09-25 15:15:43,233 [INFO] - Training epoch stats:     Loss: 5.4367 - Binary-Cell-Dice: 0.8071 - Binary-Cell-Jacard: 0.7315 - Tissue-MC-Acc.: 0.9885
2023-09-25 15:16:49,000 [INFO] - Validation epoch stats:   Loss: 5.9704 - Binary-Cell-Dice: 0.7945 - Binary-Cell-Jacard: 0.7086 - bPQ-Score: 0.5357 - mPQ-Score: 0.4103 - Tissue-MC-Acc.: 0.8645
2023-09-25 15:21:30,807 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-25 15:21:30,822 [INFO] - Epoch: 74/130
2023-09-25 15:23:10,446 [INFO] - Training epoch stats:     Loss: 5.4251 - Binary-Cell-Dice: 0.8110 - Binary-Cell-Jacard: 0.7321 - Tissue-MC-Acc.: 0.9917
2023-09-25 15:24:17,342 [INFO] - Validation epoch stats:   Loss: 6.0159 - Binary-Cell-Dice: 0.7931 - Binary-Cell-Jacard: 0.7062 - bPQ-Score: 0.5340 - mPQ-Score: 0.4103 - Tissue-MC-Acc.: 0.8614
2023-09-25 15:28:22,200 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-25 15:28:22,203 [INFO] - Epoch: 75/130
2023-09-25 15:30:07,494 [INFO] - Training epoch stats:     Loss: 5.4028 - Binary-Cell-Dice: 0.8154 - Binary-Cell-Jacard: 0.7335 - Tissue-MC-Acc.: 0.9893
2023-09-25 15:31:13,016 [INFO] - Validation epoch stats:   Loss: 6.0043 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7075 - bPQ-Score: 0.5320 - mPQ-Score: 0.4044 - Tissue-MC-Acc.: 0.8566
2023-09-25 15:35:24,283 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-25 15:35:24,330 [INFO] - Epoch: 76/130
2023-09-25 15:37:12,927 [INFO] - Training epoch stats:     Loss: 5.4423 - Binary-Cell-Dice: 0.8141 - Binary-Cell-Jacard: 0.7351 - Tissue-MC-Acc.: 0.9897
2023-09-25 15:38:19,274 [INFO] - Validation epoch stats:   Loss: 6.0084 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7068 - bPQ-Score: 0.5338 - mPQ-Score: 0.4078 - Tissue-MC-Acc.: 0.8618
2023-09-25 15:42:33,029 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-25 15:42:33,110 [INFO] - Epoch: 77/130
2023-09-25 15:44:21,573 [INFO] - Training epoch stats:     Loss: 5.3654 - Binary-Cell-Dice: 0.8152 - Binary-Cell-Jacard: 0.7365 - Tissue-MC-Acc.: 0.9917
2023-09-25 15:45:27,126 [INFO] - Validation epoch stats:   Loss: 6.0081 - Binary-Cell-Dice: 0.7933 - Binary-Cell-Jacard: 0.7073 - bPQ-Score: 0.5340 - mPQ-Score: 0.4063 - Tissue-MC-Acc.: 0.8652
2023-09-25 15:49:58,985 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-25 15:49:58,991 [INFO] - Epoch: 78/130
2023-09-25 15:51:41,616 [INFO] - Training epoch stats:     Loss: 5.4282 - Binary-Cell-Dice: 0.8106 - Binary-Cell-Jacard: 0.7349 - Tissue-MC-Acc.: 0.9901
2023-09-25 15:52:47,738 [INFO] - Validation epoch stats:   Loss: 5.9825 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7091 - bPQ-Score: 0.5338 - mPQ-Score: 0.4081 - Tissue-MC-Acc.: 0.8675
2023-09-25 16:00:01,757 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-25 16:00:01,762 [INFO] - Epoch: 79/130
2023-09-25 16:01:43,096 [INFO] - Training epoch stats:     Loss: 5.3819 - Binary-Cell-Dice: 0.8121 - Binary-Cell-Jacard: 0.7382 - Tissue-MC-Acc.: 0.9909
2023-09-25 16:02:48,861 [INFO] - Validation epoch stats:   Loss: 6.0095 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7085 - bPQ-Score: 0.5323 - mPQ-Score: 0.4059 - Tissue-MC-Acc.: 0.8667
2023-09-25 16:07:04,954 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-25 16:07:04,959 [INFO] - Epoch: 80/130
2023-09-25 16:08:48,751 [INFO] - Training epoch stats:     Loss: 5.4227 - Binary-Cell-Dice: 0.8094 - Binary-Cell-Jacard: 0.7372 - Tissue-MC-Acc.: 0.9921
2023-09-25 16:09:56,160 [INFO] - Validation epoch stats:   Loss: 6.0005 - Binary-Cell-Dice: 0.7923 - Binary-Cell-Jacard: 0.7085 - bPQ-Score: 0.5306 - mPQ-Score: 0.4029 - Tissue-MC-Acc.: 0.8671
2023-09-25 16:14:08,782 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-25 16:14:08,794 [INFO] - Epoch: 81/130
2023-09-25 16:15:50,278 [INFO] - Training epoch stats:     Loss: 5.3400 - Binary-Cell-Dice: 0.8123 - Binary-Cell-Jacard: 0.7372 - Tissue-MC-Acc.: 0.9901
2023-09-25 16:16:55,984 [INFO] - Validation epoch stats:   Loss: 6.0205 - Binary-Cell-Dice: 0.7924 - Binary-Cell-Jacard: 0.7074 - bPQ-Score: 0.5331 - mPQ-Score: 0.4056 - Tissue-MC-Acc.: 0.8701
2023-09-25 16:21:30,351 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-25 16:21:30,360 [INFO] - Epoch: 82/130
2023-09-25 16:23:11,622 [INFO] - Training epoch stats:     Loss: 5.3559 - Binary-Cell-Dice: 0.8160 - Binary-Cell-Jacard: 0.7384 - Tissue-MC-Acc.: 0.9901
2023-09-25 16:24:17,366 [INFO] - Validation epoch stats:   Loss: 6.0103 - Binary-Cell-Dice: 0.7943 - Binary-Cell-Jacard: 0.7086 - bPQ-Score: 0.5369 - mPQ-Score: 0.4104 - Tissue-MC-Acc.: 0.8656
2023-09-25 16:28:54,951 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-25 16:28:54,963 [INFO] - Epoch: 83/130
2023-09-25 16:30:35,726 [INFO] - Training epoch stats:     Loss: 5.3201 - Binary-Cell-Dice: 0.8142 - Binary-Cell-Jacard: 0.7401 - Tissue-MC-Acc.: 0.9933
2023-09-25 16:31:41,653 [INFO] - Validation epoch stats:   Loss: 6.0323 - Binary-Cell-Dice: 0.7923 - Binary-Cell-Jacard: 0.7058 - bPQ-Score: 0.5342 - mPQ-Score: 0.4081 - Tissue-MC-Acc.: 0.8656
2023-09-25 16:36:26,808 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-25 16:36:26,823 [INFO] - Epoch: 84/130
2023-09-25 16:38:08,353 [INFO] - Training epoch stats:     Loss: 5.3483 - Binary-Cell-Dice: 0.8204 - Binary-Cell-Jacard: 0.7440 - Tissue-MC-Acc.: 0.9921
2023-09-25 16:39:14,180 [INFO] - Validation epoch stats:   Loss: 6.0083 - Binary-Cell-Dice: 0.7926 - Binary-Cell-Jacard: 0.7079 - bPQ-Score: 0.5329 - mPQ-Score: 0.4040 - Tissue-MC-Acc.: 0.8641
2023-09-25 16:43:59,957 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-25 16:43:59,974 [INFO] - Epoch: 85/130
2023-09-25 16:45:41,305 [INFO] - Training epoch stats:     Loss: 5.3162 - Binary-Cell-Dice: 0.8180 - Binary-Cell-Jacard: 0.7411 - Tissue-MC-Acc.: 0.9913
2023-09-25 16:46:46,615 [INFO] - Validation epoch stats:   Loss: 6.0314 - Binary-Cell-Dice: 0.7915 - Binary-Cell-Jacard: 0.7069 - bPQ-Score: 0.5297 - mPQ-Score: 0.4020 - Tissue-MC-Acc.: 0.8652
2023-09-25 16:51:14,158 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-25 16:51:14,166 [INFO] - Epoch: 86/130
2023-09-25 16:52:56,785 [INFO] - Training epoch stats:     Loss: 5.3405 - Binary-Cell-Dice: 0.8153 - Binary-Cell-Jacard: 0.7411 - Tissue-MC-Acc.: 0.9921
2023-09-25 16:54:03,405 [INFO] - Validation epoch stats:   Loss: 6.0073 - Binary-Cell-Dice: 0.7936 - Binary-Cell-Jacard: 0.7079 - bPQ-Score: 0.5348 - mPQ-Score: 0.4078 - Tissue-MC-Acc.: 0.8626
2023-09-25 16:59:39,006 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-25 16:59:39,012 [INFO] - Epoch: 87/130
2023-09-25 17:01:19,498 [INFO] - Training epoch stats:     Loss: 5.3381 - Binary-Cell-Dice: 0.8136 - Binary-Cell-Jacard: 0.7426 - Tissue-MC-Acc.: 0.9897
2023-09-25 17:02:25,322 [INFO] - Validation epoch stats:   Loss: 6.0044 - Binary-Cell-Dice: 0.7917 - Binary-Cell-Jacard: 0.7085 - bPQ-Score: 0.5311 - mPQ-Score: 0.4019 - Tissue-MC-Acc.: 0.8701
2023-09-25 17:07:43,002 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-25 17:07:43,004 [INFO] - Epoch: 88/130
2023-09-25 17:09:25,636 [INFO] - Training epoch stats:     Loss: 5.3077 - Binary-Cell-Dice: 0.8207 - Binary-Cell-Jacard: 0.7469 - Tissue-MC-Acc.: 0.9897
2023-09-25 17:10:32,296 [INFO] - Validation epoch stats:   Loss: 6.0302 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7075 - bPQ-Score: 0.5335 - mPQ-Score: 0.4060 - Tissue-MC-Acc.: 0.8686
2023-09-25 17:15:25,406 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:15:25,413 [INFO] - Epoch: 89/130
2023-09-25 17:17:07,090 [INFO] - Training epoch stats:     Loss: 5.3380 - Binary-Cell-Dice: 0.8194 - Binary-Cell-Jacard: 0.7406 - Tissue-MC-Acc.: 0.9909
2023-09-25 17:18:14,268 [INFO] - Validation epoch stats:   Loss: 5.9965 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7086 - bPQ-Score: 0.5361 - mPQ-Score: 0.4070 - Tissue-MC-Acc.: 0.8660
2023-09-25 17:23:13,658 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:23:13,661 [INFO] - Epoch: 90/130
2023-09-25 17:24:55,123 [INFO] - Training epoch stats:     Loss: 5.2659 - Binary-Cell-Dice: 0.8237 - Binary-Cell-Jacard: 0.7435 - Tissue-MC-Acc.: 0.9929
2023-09-25 17:26:00,533 [INFO] - Validation epoch stats:   Loss: 6.0414 - Binary-Cell-Dice: 0.7922 - Binary-Cell-Jacard: 0.7069 - bPQ-Score: 0.5347 - mPQ-Score: 0.4064 - Tissue-MC-Acc.: 0.8686
2023-09-25 17:30:04,797 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:30:04,848 [INFO] - Epoch: 91/130
2023-09-25 17:31:47,901 [INFO] - Training epoch stats:     Loss: 5.2676 - Binary-Cell-Dice: 0.8149 - Binary-Cell-Jacard: 0.7430 - Tissue-MC-Acc.: 0.9921
2023-09-25 17:32:52,840 [INFO] - Validation epoch stats:   Loss: 6.0291 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7081 - bPQ-Score: 0.5361 - mPQ-Score: 0.4091 - Tissue-MC-Acc.: 0.8660
2023-09-25 17:37:18,461 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:37:18,524 [INFO] - Epoch: 92/130
2023-09-25 17:39:02,192 [INFO] - Training epoch stats:     Loss: 5.3205 - Binary-Cell-Dice: 0.8206 - Binary-Cell-Jacard: 0.7435 - Tissue-MC-Acc.: 0.9952
2023-09-25 17:40:07,773 [INFO] - Validation epoch stats:   Loss: 6.0483 - Binary-Cell-Dice: 0.7922 - Binary-Cell-Jacard: 0.7052 - bPQ-Score: 0.5322 - mPQ-Score: 0.4042 - Tissue-MC-Acc.: 0.8656
2023-09-25 17:44:21,938 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:44:22,017 [INFO] - Epoch: 93/130
2023-09-25 17:46:11,494 [INFO] - Training epoch stats:     Loss: 5.3000 - Binary-Cell-Dice: 0.8178 - Binary-Cell-Jacard: 0.7445 - Tissue-MC-Acc.: 0.9913
2023-09-25 17:47:17,765 [INFO] - Validation epoch stats:   Loss: 6.0484 - Binary-Cell-Dice: 0.7916 - Binary-Cell-Jacard: 0.7064 - bPQ-Score: 0.5328 - mPQ-Score: 0.4075 - Tissue-MC-Acc.: 0.8701
2023-09-25 17:50:41,799 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-25 17:50:41,854 [INFO] - Epoch: 94/130
2023-09-25 17:52:29,652 [INFO] - Training epoch stats:     Loss: 5.2645 - Binary-Cell-Dice: 0.8180 - Binary-Cell-Jacard: 0.7456 - Tissue-MC-Acc.: 0.9941
2023-09-25 17:53:34,567 [INFO] - Validation epoch stats:   Loss: 6.0385 - Binary-Cell-Dice: 0.7919 - Binary-Cell-Jacard: 0.7077 - bPQ-Score: 0.5316 - mPQ-Score: 0.4040 - Tissue-MC-Acc.: 0.8682
2023-09-25 17:55:19,198 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-25 17:55:19,201 [INFO] - Epoch: 95/130
2023-09-25 17:57:03,251 [INFO] - Training epoch stats:     Loss: 5.3157 - Binary-Cell-Dice: 0.8249 - Binary-Cell-Jacard: 0.7476 - Tissue-MC-Acc.: 0.9945
2023-09-25 17:58:30,369 [INFO] - Validation epoch stats:   Loss: 6.0378 - Binary-Cell-Dice: 0.7925 - Binary-Cell-Jacard: 0.7076 - bPQ-Score: 0.5332 - mPQ-Score: 0.4049 - Tissue-MC-Acc.: 0.8694
2023-09-25 18:00:15,456 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:00:15,468 [INFO] - Epoch: 96/130
2023-09-25 18:01:59,119 [INFO] - Training epoch stats:     Loss: 5.3240 - Binary-Cell-Dice: 0.8092 - Binary-Cell-Jacard: 0.7412 - Tissue-MC-Acc.: 0.9901
2023-09-25 18:03:25,015 [INFO] - Validation epoch stats:   Loss: 6.0065 - Binary-Cell-Dice: 0.7936 - Binary-Cell-Jacard: 0.7091 - bPQ-Score: 0.5370 - mPQ-Score: 0.4085 - Tissue-MC-Acc.: 0.8694
2023-09-25 18:06:23,403 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:06:23,413 [INFO] - Epoch: 97/130
2023-09-25 18:08:08,513 [INFO] - Training epoch stats:     Loss: 5.2920 - Binary-Cell-Dice: 0.8170 - Binary-Cell-Jacard: 0.7422 - Tissue-MC-Acc.: 0.9956
2023-09-25 18:09:14,813 [INFO] - Validation epoch stats:   Loss: 6.0478 - Binary-Cell-Dice: 0.7931 - Binary-Cell-Jacard: 0.7075 - bPQ-Score: 0.5336 - mPQ-Score: 0.4057 - Tissue-MC-Acc.: 0.8701
2023-09-25 18:13:11,219 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:13:11,231 [INFO] - Epoch: 98/130
2023-09-25 18:14:51,656 [INFO] - Training epoch stats:     Loss: 5.2962 - Binary-Cell-Dice: 0.8191 - Binary-Cell-Jacard: 0.7454 - Tissue-MC-Acc.: 0.9933
2023-09-25 18:15:57,780 [INFO] - Validation epoch stats:   Loss: 6.0212 - Binary-Cell-Dice: 0.7947 - Binary-Cell-Jacard: 0.7079 - bPQ-Score: 0.5367 - mPQ-Score: 0.4094 - Tissue-MC-Acc.: 0.8675
2023-09-25 18:21:03,968 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:21:03,974 [INFO] - Epoch: 99/130
2023-09-25 18:22:45,403 [INFO] - Training epoch stats:     Loss: 5.2632 - Binary-Cell-Dice: 0.8199 - Binary-Cell-Jacard: 0.7475 - Tissue-MC-Acc.: 0.9937
2023-09-25 18:23:50,730 [INFO] - Validation epoch stats:   Loss: 6.0440 - Binary-Cell-Dice: 0.7924 - Binary-Cell-Jacard: 0.7069 - bPQ-Score: 0.5355 - mPQ-Score: 0.4083 - Tissue-MC-Acc.: 0.8682
2023-09-25 18:28:23,821 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:28:23,833 [INFO] - Epoch: 100/130
2023-09-25 18:30:04,168 [INFO] - Training epoch stats:     Loss: 5.2412 - Binary-Cell-Dice: 0.8206 - Binary-Cell-Jacard: 0.7477 - Tissue-MC-Acc.: 0.9933
2023-09-25 18:31:09,612 [INFO] - Validation epoch stats:   Loss: 6.0337 - Binary-Cell-Dice: 0.7932 - Binary-Cell-Jacard: 0.7066 - bPQ-Score: 0.5357 - mPQ-Score: 0.4077 - Tissue-MC-Acc.: 0.8697
2023-09-25 18:36:03,254 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:36:03,264 [INFO] - Epoch: 101/130
2023-09-25 18:37:42,737 [INFO] - Training epoch stats:     Loss: 5.2935 - Binary-Cell-Dice: 0.8250 - Binary-Cell-Jacard: 0.7488 - Tissue-MC-Acc.: 0.9901
2023-09-25 18:38:50,759 [INFO] - Validation epoch stats:   Loss: 6.0428 - Binary-Cell-Dice: 0.7926 - Binary-Cell-Jacard: 0.7078 - bPQ-Score: 0.5352 - mPQ-Score: 0.4066 - Tissue-MC-Acc.: 0.8705
2023-09-25 18:42:31,470 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:42:31,525 [INFO] - Epoch: 102/130
2023-09-25 18:44:14,229 [INFO] - Training epoch stats:     Loss: 5.3317 - Binary-Cell-Dice: 0.8156 - Binary-Cell-Jacard: 0.7451 - Tissue-MC-Acc.: 0.9929
2023-09-25 18:45:19,781 [INFO] - Validation epoch stats:   Loss: 6.0324 - Binary-Cell-Dice: 0.7932 - Binary-Cell-Jacard: 0.7087 - bPQ-Score: 0.5358 - mPQ-Score: 0.4082 - Tissue-MC-Acc.: 0.8705
2023-09-25 18:47:55,456 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:47:55,512 [INFO] - Epoch: 103/130
2023-09-25 18:49:39,061 [INFO] - Training epoch stats:     Loss: 5.2925 - Binary-Cell-Dice: 0.8202 - Binary-Cell-Jacard: 0.7484 - Tissue-MC-Acc.: 0.9933
2023-09-25 18:50:56,833 [INFO] - Validation epoch stats:   Loss: 6.0404 - Binary-Cell-Dice: 0.7913 - Binary-Cell-Jacard: 0.7079 - bPQ-Score: 0.5344 - mPQ-Score: 0.4069 - Tissue-MC-Acc.: 0.8694
2023-09-25 18:52:40,970 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-25 18:52:40,974 [INFO] - Epoch: 104/130
2023-09-25 18:54:20,593 [INFO] - Training epoch stats:     Loss: 5.2883 - Binary-Cell-Dice: 0.8155 - Binary-Cell-Jacard: 0.7427 - Tissue-MC-Acc.: 0.9933
2023-09-25 18:55:43,315 [INFO] - Validation epoch stats:   Loss: 6.0209 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7101 - bPQ-Score: 0.5335 - mPQ-Score: 0.4067 - Tissue-MC-Acc.: 0.8705
2023-09-25 18:57:33,334 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-25 18:57:33,343 [INFO] - Epoch: 105/130
2023-09-25 18:59:13,593 [INFO] - Training epoch stats:     Loss: 5.2516 - Binary-Cell-Dice: 0.8182 - Binary-Cell-Jacard: 0.7460 - Tissue-MC-Acc.: 0.9925
2023-09-25 19:01:59,705 [INFO] - Validation epoch stats:   Loss: 6.0540 - Binary-Cell-Dice: 0.7922 - Binary-Cell-Jacard: 0.7080 - bPQ-Score: 0.5324 - mPQ-Score: 0.4059 - Tissue-MC-Acc.: 0.8697
2023-09-25 19:04:02,082 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:04:02,090 [INFO] - Epoch: 106/130
2023-09-25 19:05:41,936 [INFO] - Training epoch stats:     Loss: 5.3179 - Binary-Cell-Dice: 0.8147 - Binary-Cell-Jacard: 0.7409 - Tissue-MC-Acc.: 0.9921
2023-09-25 19:07:04,989 [INFO] - Validation epoch stats:   Loss: 6.0161 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7086 - bPQ-Score: 0.5332 - mPQ-Score: 0.4046 - Tissue-MC-Acc.: 0.8705
2023-09-25 19:10:18,339 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:10:18,351 [INFO] - Epoch: 107/130
2023-09-25 19:11:59,417 [INFO] - Training epoch stats:     Loss: 5.2673 - Binary-Cell-Dice: 0.8259 - Binary-Cell-Jacard: 0.7473 - Tissue-MC-Acc.: 0.9956
2023-09-25 19:13:06,205 [INFO] - Validation epoch stats:   Loss: 6.0093 - Binary-Cell-Dice: 0.7935 - Binary-Cell-Jacard: 0.7075 - bPQ-Score: 0.5367 - mPQ-Score: 0.4092 - Tissue-MC-Acc.: 0.8690
2023-09-25 19:18:23,313 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:18:23,318 [INFO] - Epoch: 108/130
2023-09-25 19:20:04,173 [INFO] - Training epoch stats:     Loss: 5.2703 - Binary-Cell-Dice: 0.8254 - Binary-Cell-Jacard: 0.7496 - Tissue-MC-Acc.: 0.9905
2023-09-25 19:21:08,754 [INFO] - Validation epoch stats:   Loss: 6.0640 - Binary-Cell-Dice: 0.7925 - Binary-Cell-Jacard: 0.7073 - bPQ-Score: 0.5329 - mPQ-Score: 0.4048 - Tissue-MC-Acc.: 0.8682
2023-09-25 19:24:05,609 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:24:05,655 [INFO] - Epoch: 109/130
2023-09-25 19:25:49,284 [INFO] - Training epoch stats:     Loss: 5.3312 - Binary-Cell-Dice: 0.8155 - Binary-Cell-Jacard: 0.7465 - Tissue-MC-Acc.: 0.9929
2023-09-25 19:26:53,914 [INFO] - Validation epoch stats:   Loss: 6.0974 - Binary-Cell-Dice: 0.7918 - Binary-Cell-Jacard: 0.7034 - bPQ-Score: 0.5356 - mPQ-Score: 0.4069 - Tissue-MC-Acc.: 0.8701
2023-09-25 19:31:00,074 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:31:00,141 [INFO] - Epoch: 110/130
2023-09-25 19:32:44,872 [INFO] - Training epoch stats:     Loss: 5.1622 - Binary-Cell-Dice: 0.8203 - Binary-Cell-Jacard: 0.7471 - Tissue-MC-Acc.: 0.9929
2023-09-25 19:33:50,078 [INFO] - Validation epoch stats:   Loss: 6.0386 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7083 - bPQ-Score: 0.5324 - mPQ-Score: 0.4054 - Tissue-MC-Acc.: 0.8712
2023-09-25 19:39:01,370 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:39:01,382 [INFO] - Epoch: 111/130
2023-09-25 19:40:42,030 [INFO] - Training epoch stats:     Loss: 5.2353 - Binary-Cell-Dice: 0.8187 - Binary-Cell-Jacard: 0.7449 - Tissue-MC-Acc.: 0.9956
2023-09-25 19:41:46,587 [INFO] - Validation epoch stats:   Loss: 6.0495 - Binary-Cell-Dice: 0.7918 - Binary-Cell-Jacard: 0.7078 - bPQ-Score: 0.5329 - mPQ-Score: 0.4054 - Tissue-MC-Acc.: 0.8694
2023-09-25 19:46:35,456 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:46:35,458 [INFO] - Epoch: 112/130
2023-09-25 19:48:15,871 [INFO] - Training epoch stats:     Loss: 5.3185 - Binary-Cell-Dice: 0.8166 - Binary-Cell-Jacard: 0.7439 - Tissue-MC-Acc.: 0.9933
2023-09-25 19:49:21,047 [INFO] - Validation epoch stats:   Loss: 6.0483 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7077 - bPQ-Score: 0.5336 - mPQ-Score: 0.4059 - Tissue-MC-Acc.: 0.8727
2023-09-25 19:53:33,359 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 19:53:33,361 [INFO] - Epoch: 113/130
2023-09-25 19:55:14,436 [INFO] - Training epoch stats:     Loss: 5.3109 - Binary-Cell-Dice: 0.8161 - Binary-Cell-Jacard: 0.7448 - Tissue-MC-Acc.: 0.9917
2023-09-25 19:56:19,345 [INFO] - Validation epoch stats:   Loss: 6.0130 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7098 - bPQ-Score: 0.5337 - mPQ-Score: 0.4066 - Tissue-MC-Acc.: 0.8716
2023-09-25 20:00:16,719 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:00:16,724 [INFO] - Epoch: 114/130
2023-09-25 20:01:56,929 [INFO] - Training epoch stats:     Loss: 5.2383 - Binary-Cell-Dice: 0.8226 - Binary-Cell-Jacard: 0.7447 - Tissue-MC-Acc.: 0.9929
2023-09-25 20:03:01,517 [INFO] - Validation epoch stats:   Loss: 6.0444 - Binary-Cell-Dice: 0.7911 - Binary-Cell-Jacard: 0.7077 - bPQ-Score: 0.5325 - mPQ-Score: 0.4060 - Tissue-MC-Acc.: 0.8709
2023-09-25 20:06:59,174 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:06:59,176 [INFO] - Epoch: 115/130
2023-09-25 20:08:40,011 [INFO] - Training epoch stats:     Loss: 5.2394 - Binary-Cell-Dice: 0.8184 - Binary-Cell-Jacard: 0.7469 - Tissue-MC-Acc.: 0.9941
2023-09-25 20:09:44,714 [INFO] - Validation epoch stats:   Loss: 6.0482 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7084 - bPQ-Score: 0.5345 - mPQ-Score: 0.4072 - Tissue-MC-Acc.: 0.8697
2023-09-25 20:13:41,461 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:13:41,463 [INFO] - Epoch: 116/130
2023-09-25 20:15:21,972 [INFO] - Training epoch stats:     Loss: 5.2246 - Binary-Cell-Dice: 0.8205 - Binary-Cell-Jacard: 0.7477 - Tissue-MC-Acc.: 0.9937
2023-09-25 20:16:26,043 [INFO] - Validation epoch stats:   Loss: 6.0573 - Binary-Cell-Dice: 0.7934 - Binary-Cell-Jacard: 0.7069 - bPQ-Score: 0.5336 - mPQ-Score: 0.4066 - Tissue-MC-Acc.: 0.8716
2023-09-25 20:20:35,688 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:20:35,731 [INFO] - Epoch: 117/130
2023-09-25 20:22:17,718 [INFO] - Training epoch stats:     Loss: 5.2663 - Binary-Cell-Dice: 0.8194 - Binary-Cell-Jacard: 0.7482 - Tissue-MC-Acc.: 0.9956
2023-09-25 20:23:39,248 [INFO] - Validation epoch stats:   Loss: 6.0566 - Binary-Cell-Dice: 0.7915 - Binary-Cell-Jacard: 0.7072 - bPQ-Score: 0.5342 - mPQ-Score: 0.4054 - Tissue-MC-Acc.: 0.8697
2023-09-25 20:28:10,045 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:28:10,050 [INFO] - Epoch: 118/130
2023-09-25 20:29:51,015 [INFO] - Training epoch stats:     Loss: 5.2394 - Binary-Cell-Dice: 0.8178 - Binary-Cell-Jacard: 0.7471 - Tissue-MC-Acc.: 0.9948
2023-09-25 20:30:54,803 [INFO] - Validation epoch stats:   Loss: 6.0359 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7093 - bPQ-Score: 0.5330 - mPQ-Score: 0.4058 - Tissue-MC-Acc.: 0.8709
2023-09-25 20:33:56,533 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:33:56,571 [INFO] - Epoch: 119/130
2023-09-25 20:35:37,454 [INFO] - Training epoch stats:     Loss: 5.2238 - Binary-Cell-Dice: 0.8185 - Binary-Cell-Jacard: 0.7480 - Tissue-MC-Acc.: 0.9925
2023-09-25 20:36:53,253 [INFO] - Validation epoch stats:   Loss: 6.0467 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7080 - bPQ-Score: 0.5359 - mPQ-Score: 0.4077 - Tissue-MC-Acc.: 0.8705
2023-09-25 20:39:27,571 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:39:27,608 [INFO] - Epoch: 120/130
2023-09-25 20:41:08,163 [INFO] - Training epoch stats:     Loss: 5.2949 - Binary-Cell-Dice: 0.8212 - Binary-Cell-Jacard: 0.7459 - Tissue-MC-Acc.: 0.9945
2023-09-25 20:42:11,886 [INFO] - Validation epoch stats:   Loss: 6.0348 - Binary-Cell-Dice: 0.7920 - Binary-Cell-Jacard: 0.7091 - bPQ-Score: 0.5328 - mPQ-Score: 0.4048 - Tissue-MC-Acc.: 0.8697
2023-09-25 20:45:01,218 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:45:01,267 [INFO] - Epoch: 121/130
2023-09-25 20:46:49,495 [INFO] - Training epoch stats:     Loss: 5.2335 - Binary-Cell-Dice: 0.8226 - Binary-Cell-Jacard: 0.7495 - Tissue-MC-Acc.: 0.9933
2023-09-25 20:47:52,680 [INFO] - Validation epoch stats:   Loss: 6.0393 - Binary-Cell-Dice: 0.7925 - Binary-Cell-Jacard: 0.7092 - bPQ-Score: 0.5328 - mPQ-Score: 0.4050 - Tissue-MC-Acc.: 0.8701
2023-09-25 20:50:53,709 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:50:53,749 [INFO] - Epoch: 122/130
2023-09-25 20:52:39,601 [INFO] - Training epoch stats:     Loss: 5.2083 - Binary-Cell-Dice: 0.8196 - Binary-Cell-Jacard: 0.7517 - Tissue-MC-Acc.: 0.9945
2023-09-25 20:53:43,639 [INFO] - Validation epoch stats:   Loss: 6.0267 - Binary-Cell-Dice: 0.7930 - Binary-Cell-Jacard: 0.7088 - bPQ-Score: 0.5359 - mPQ-Score: 0.4076 - Tissue-MC-Acc.: 0.8694
2023-09-25 20:56:15,163 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 20:56:15,209 [INFO] - Epoch: 123/130
2023-09-25 20:58:00,494 [INFO] - Training epoch stats:     Loss: 5.2707 - Binary-Cell-Dice: 0.8144 - Binary-Cell-Jacard: 0.7452 - Tissue-MC-Acc.: 0.9921
2023-09-25 20:59:03,637 [INFO] - Validation epoch stats:   Loss: 6.0429 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.7071 - bPQ-Score: 0.5357 - mPQ-Score: 0.4084 - Tissue-MC-Acc.: 0.8697
2023-09-25 21:00:19,976 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 21:00:19,980 [INFO] - Epoch: 124/130
2023-09-25 21:02:00,862 [INFO] - Training epoch stats:     Loss: 5.2456 - Binary-Cell-Dice: 0.8230 - Binary-Cell-Jacard: 0.7461 - Tissue-MC-Acc.: 0.9956
2023-09-25 21:03:18,490 [INFO] - Validation epoch stats:   Loss: 6.0483 - Binary-Cell-Dice: 0.7926 - Binary-Cell-Jacard: 0.7080 - bPQ-Score: 0.5351 - mPQ-Score: 0.4081 - Tissue-MC-Acc.: 0.8701
2023-09-25 21:04:41,053 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-25 21:04:41,054 [INFO] - Epoch: 125/130
2023-09-25 21:06:25,850 [INFO] - Training epoch stats:     Loss: 5.2248 - Binary-Cell-Dice: 0.8181 - Binary-Cell-Jacard: 0.7476 - Tissue-MC-Acc.: 0.9941
2023-09-25 21:07:45,369 [INFO] - Validation epoch stats:   Loss: 6.0351 - Binary-Cell-Dice: 0.7923 - Binary-Cell-Jacard: 0.7085 - bPQ-Score: 0.5355 - mPQ-Score: 0.4082 - Tissue-MC-Acc.: 0.8705
2023-09-25 21:10:31,677 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-25 21:10:31,681 [INFO] - Epoch: 126/130
2023-09-25 21:12:17,786 [INFO] - Training epoch stats:     Loss: 5.2072 - Binary-Cell-Dice: 0.8257 - Binary-Cell-Jacard: 0.7475 - Tissue-MC-Acc.: 0.9948
2023-09-25 21:13:21,336 [INFO] - Validation epoch stats:   Loss: 6.0639 - Binary-Cell-Dice: 0.7929 - Binary-Cell-Jacard: 0.7080 - bPQ-Score: 0.5355 - mPQ-Score: 0.4079 - Tissue-MC-Acc.: 0.8697
2023-09-25 21:16:13,040 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-25 21:16:13,042 [INFO] - Epoch: 127/130
2023-09-25 21:17:56,603 [INFO] - Training epoch stats:     Loss: 5.1970 - Binary-Cell-Dice: 0.8250 - Binary-Cell-Jacard: 0.7510 - Tissue-MC-Acc.: 0.9945
2023-09-25 21:19:00,661 [INFO] - Validation epoch stats:   Loss: 6.0486 - Binary-Cell-Dice: 0.7924 - Binary-Cell-Jacard: 0.7069 - bPQ-Score: 0.5353 - mPQ-Score: 0.4073 - Tissue-MC-Acc.: 0.8690
2023-09-25 21:22:15,688 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-25 21:22:15,693 [INFO] - Epoch: 128/130
2023-09-25 21:24:02,253 [INFO] - Training epoch stats:     Loss: 5.3181 - Binary-Cell-Dice: 0.8106 - Binary-Cell-Jacard: 0.7446 - Tissue-MC-Acc.: 0.9937
2023-09-25 21:25:07,215 [INFO] - Validation epoch stats:   Loss: 6.0463 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7081 - bPQ-Score: 0.5331 - mPQ-Score: 0.4057 - Tissue-MC-Acc.: 0.8694
2023-09-25 21:28:28,650 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-25 21:28:28,653 [INFO] - Epoch: 129/130
2023-09-25 21:30:09,487 [INFO] - Training epoch stats:     Loss: 5.1507 - Binary-Cell-Dice: 0.8174 - Binary-Cell-Jacard: 0.7480 - Tissue-MC-Acc.: 0.9941
2023-09-25 21:31:13,651 [INFO] - Validation epoch stats:   Loss: 6.0194 - Binary-Cell-Dice: 0.7941 - Binary-Cell-Jacard: 0.7108 - bPQ-Score: 0.5360 - mPQ-Score: 0.4055 - Tissue-MC-Acc.: 0.8694
2023-09-25 21:34:00,971 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-25 21:34:01,011 [INFO] - Epoch: 130/130
2023-09-25 21:35:43,531 [INFO] - Training epoch stats:     Loss: 5.2798 - Binary-Cell-Dice: 0.8179 - Binary-Cell-Jacard: 0.7456 - Tissue-MC-Acc.: 0.9941
2023-09-25 21:36:57,275 [INFO] - Validation epoch stats:   Loss: 6.0580 - Binary-Cell-Dice: 0.7928 - Binary-Cell-Jacard: 0.7070 - bPQ-Score: 0.5337 - mPQ-Score: 0.4080 - Tissue-MC-Acc.: 0.8690
2023-09-25 21:39:47,610 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
