<All keys matched successfully>
Loading inference transformations
Detected cells before cleaning: 1309
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 262
Iteration 1: Found overlap of # cells: 11
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 780
Detected cells before cleaning: 1513
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 338
Iteration 1: Found overlap of # cells: 11
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 899
Detected cells before cleaning: 958
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 197
Iteration 1: Found overlap of # cells: 2
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 587
Detected cells before cleaning: 910
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 193
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 555
Detected cells before cleaning: 1107
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 233
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 692
Detected cells before cleaning: 752
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 149
Iteration 1: Found overlap of # cells: 8
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 456
Detected cells before cleaning: 1578
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 379
Iteration 1: Found overlap of # cells: 13
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 951
Detected cells before cleaning: 992
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 209
Iteration 1: Found overlap of # cells: 4
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 594
Detected cells before cleaning: 979
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 187
Iteration 1: Found overlap of # cells: 14
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 585
Detected cells before cleaning: 719
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 132
Iteration 1: Found overlap of # cells: 6
Iteration 2: Found overlap of # cells: 1
Iteration 3: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 425
Detected cells before cleaning: 673
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 128
Iteration 1: Found overlap of # cells: 5
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 407
Detected cells before cleaning: 566
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 130
Iteration 1: Found overlap of # cells: 9
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 326
Detected cells before cleaning: 1078
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 218
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 657
Detected cells before cleaning: 912
Initializing Cell-Postprocessor
Finding edge-cells for merging
Removal of cells detected multiple times
Iteration 0: Found overlap of # cells: 181
Iteration 1: Found overlap of # cells: 3
Iteration 2: Found overlap of # cells: 0
Found all overlapping cells
Detected cells after cleaning: 547
******************** Binary Dataset metrics ********************
Binary-Cell-Dice-Mean:    0.7626438140869141
Binary-Cell-Jacard-Mean:  0.6195405125617981
bPQ:                      0.505832171053376
bDQ:                      0.7128867550346704
bSQ:                      0.7060287399582108
f1_detection:             0.834640607736314
precision_detection:      0.748737410788296
recall_detection:         0.9449365583780208
