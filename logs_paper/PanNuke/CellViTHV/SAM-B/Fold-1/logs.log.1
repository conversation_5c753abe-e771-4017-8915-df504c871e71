2023-09-09 06:22:45,385 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-09 06:22:45,447 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f038e193490>]
2023-09-09 06:22:45,448 [INFO] - Using GPU: cuda:0
2023-09-09 06:22:45,448 [INFO] - Using device: cuda:0
2023-09-09 06:22:45,449 [INFO] - Loss functions:
2023-09-09 06:22:45,449 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-09 06:22:53,771 [INFO] - Loaded CellViT-SAM model with backbone: SAM-B
2023-09-09 06:22:53,774 [INFO] -
Model: CellViTSAM(
  (encoder): ViTCellViTDeit(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(16, 16), stride=(16, 16))
    )
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (1): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (2): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (3): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (4): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (5): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (6): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (7): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (8): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (9): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (10): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (11): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
    )
    (neck): Sequential(
      (0): Conv2d(768, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (1): LayerNorm2d()
      (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
      (3): LayerNorm2d()
    )
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
)
2023-09-09 06:22:54,927 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViTSAM                                    [1, 6, 256, 256]          --
├─ViTCellViTDeit: 1-1                         [1, 256]                  3,145,728
│    └─PatchEmbed: 2-1                        [1, 16, 16, 768]          --
│    │    └─Conv2d: 3-1                       [1, 768, 16, 16]          (590,592)
│    └─ModuleList: 2-2                        --                        --
│    │    └─Block: 3-2                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-3                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-4                        [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-5                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-6                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-7                        [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-8                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-9                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-10                       [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-11                       [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-12                       [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-13                       [1, 16, 16, 768]          (7,104,128)
│    └─Sequential: 2-3                        [1, 256, 16, 16]          --
│    │    └─Conv2d: 3-14                      [1, 256, 16, 16]          (196,608)
│    │    └─LayerNorm2d: 3-15                 [1, 256, 16, 16]          (512)
│    │    └─Conv2d: 3-16                      [1, 256, 16, 16]          (589,824)
│    │    └─LayerNorm2d: 3-17                 [1, 256, 16, 16]          (512)
├─Linear: 1-2                                 [1, 19]                   4,883
├─Sequential: 1-11                            --                        (recursive)
│    └─ConvTranspose2d: 2-4                   [1, 512, 32, 32]          1,573,376
├─Sequential: 1-4                             [1, 512, 32, 32]          --
│    └─Deconv2DBlock: 2-5                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-18                  [1, 512, 32, 32]          3,934,208
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-6                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-19                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-20                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-21                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-22             [1, 256, 64, 64]          524,544
├─Sequential: 1-6                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-7                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-23                  [1, 512, 32, 32]          3,934,208
│    └─Deconv2DBlock: 2-8                     [1, 256, 64, 64]          --
│    │    └─Sequential: 3-24                  [1, 256, 64, 64]          1,115,136
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-9                        [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-25                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-26                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-27             [1, 128, 128, 128]        131,200
├─Sequential: 1-8                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-10                    [1, 512, 32, 32]          --
│    │    └─Sequential: 3-28                  [1, 512, 32, 32]          3,934,208
│    └─Deconv2DBlock: 2-11                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-29                  [1, 256, 64, 64]          1,115,136
│    └─Deconv2DBlock: 2-12                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-30                  [1, 128, 128, 128]        279,040
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-13                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-31                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-32                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-33             [1, 64, 256, 256]         32,832
├─Sequential: 1-10                            [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-14                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-34                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-15                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-35                  [1, 64, 256, 256]         18,624
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-16                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-36                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-37                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-38                      [1, 2, 256, 256]          130
├─Sequential: 1-20                            --                        (recursive)
│    └─ConvTranspose2d: 2-17                  [1, 512, 32, 32]          1,573,376
├─Sequential: 1-13                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-18                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-39                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-19                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-40                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-41                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-42                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-43             [1, 256, 64, 64]          524,544
├─Sequential: 1-15                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-44                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-21                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-22                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-46                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-47                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-48             [1, 128, 128, 128]        131,200
├─Sequential: 1-17                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-49                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-24                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-50                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-51                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-26                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-52                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-53                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-54             [1, 64, 256, 256]         32,832
├─Sequential: 1-19                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-27                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-55                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-28                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-56                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-29                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-57                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-58                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-59                      [1, 2, 256, 256]          130
├─Sequential: 1-29                            --                        (recursive)
│    └─ConvTranspose2d: 2-30                  [1, 512, 32, 32]          1,573,376
├─Sequential: 1-22                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-31                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-60                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-32                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-61                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-62                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-63                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-64             [1, 256, 64, 64]          524,544
├─Sequential: 1-24                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-65                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-34                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-35                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-67                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-68                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-69             [1, 128, 128, 128]        131,200
├─Sequential: 1-26                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-70                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-37                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-71                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-72                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-39                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-73                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-74                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-75             [1, 64, 256, 256]         32,832
├─Sequential: 1-28                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-40                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-76                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-41                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-77                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-42                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-78                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-79                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-80                      [1, 6, 256, 256]          390
===============================================================================================
Total params: 146,094,557
Trainable params: 56,423,645
Non-trainable params: 89,670,912
Total mult-adds (G): 200.15
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 2006.45
Params size (MB): 571.42
Estimated Total Size (MB): 2578.66
===============================================================================================
2023-09-09 06:22:57,804 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-09 06:22:57,804 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-09 06:22:57,804 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-09 06:23:07,474 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-09 06:23:07,484 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314, 0.7691,
        0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9667, 0.9823,
        0.9883, 0.9902, 1.0044, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200, 1.0215,
        1.0260, 1.0278, 1.0367, 1.0449, 1.0536, 1.0910, 1.0913, 1.0922, 1.0927,
        1.1088, 1.1094, 1.1143, 1.1149, 1.1161, 1.1173, 1.1216, 1.1256, 1.1287,
        1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1480, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1660, 1.1777, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2580, 1.2595, 1.2621, 1.2651, 1.2663, 1.2666, 1.2669, 1.2678, 1.2680,
        1.2699, 1.2829, 1.2835, 1.2844, 1.2850, 1.2869, 1.2884, 1.2890, 1.2899,
        1.2902, 1.2905, 1.2915, 1.2929, 1.2972, 1.3055, 1.3072, 1.3141, 1.3156,
        1.3165, 1.3219, 1.3221, 1.3227, 1.3234, 1.3276, 1.3282, 1.3294, 1.3306,
        1.3321, 1.3380, 1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3766,
        1.3885, 1.3891, 1.3940, 1.3971, 1.4033, 1.4081, 1.4099, 1.4111, 1.4197,
        1.4275, 1.4277, 1.4332, 1.4336, 1.4362, 1.4377, 1.4407, 1.4419, 1.4436,
        1.4455, 1.4585, 1.4591, 1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897,
        1.4975, 1.5089, 1.5130, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5641,
        1.5647, 1.5696, 1.5702, 1.5712, 1.5746, 1.5953, 1.6174, 1.6260, 1.6871,
        1.6874, 1.7349, 1.7468, 1.7502, 1.7695, 1.8002, 1.8615, 1.8627, 1.8866,
        1.9432])
2023-09-09 06:23:07,484 [INFO] - Instantiate Trainer
2023-09-09 06:23:07,484 [INFO] - Calling Trainer Fit
2023-09-09 06:23:07,484 [INFO] - Starting training, total number of epochs: 130
2023-09-09 06:23:07,484 [INFO] - Epoch: 1/130
2023-09-09 06:27:02,505 [INFO] - Training epoch stats:     Loss: 8.5441 - Binary-Cell-Dice: 0.6977 - Binary-Cell-Jacard: 0.5730 - Tissue-MC-Acc.: 0.2007
2023-09-09 06:29:38,751 [INFO] - Validation epoch stats:   Loss: 6.4510 - Binary-Cell-Dice: 0.7593 - Binary-Cell-Jacard: 0.6545 - PQ-Score: 0.5225 - Tissue-MC-Acc.: 0.2969
2023-09-09 06:29:38,755 [INFO] - New best model - save checkpoint
2023-09-09 06:30:02,760 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-09 06:30:02,761 [INFO] - Epoch: 2/130
2023-09-09 06:36:32,144 [INFO] - Training epoch stats:     Loss: 6.2533 - Binary-Cell-Dice: 0.7600 - Binary-Cell-Jacard: 0.6488 - Tissue-MC-Acc.: 0.2319
2023-09-09 06:38:55,663 [INFO] - Validation epoch stats:   Loss: 5.8694 - Binary-Cell-Dice: 0.7693 - Binary-Cell-Jacard: 0.6701 - PQ-Score: 0.5466 - Tissue-MC-Acc.: 0.2969
2023-09-09 06:38:55,667 [INFO] - New best model - save checkpoint
2023-09-09 06:41:56,661 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-09 06:41:56,669 [INFO] - Epoch: 3/130
2023-09-09 06:48:49,651 [INFO] - Training epoch stats:     Loss: 5.9719 - Binary-Cell-Dice: 0.7723 - Binary-Cell-Jacard: 0.6619 - Tissue-MC-Acc.: 0.2402
2023-09-09 06:51:17,906 [INFO] - Validation epoch stats:   Loss: 5.8268 - Binary-Cell-Dice: 0.7717 - Binary-Cell-Jacard: 0.6771 - PQ-Score: 0.5602 - Tissue-MC-Acc.: 0.2969
2023-09-09 06:51:17,915 [INFO] - New best model - save checkpoint
2023-09-09 06:54:30,493 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-09 06:54:30,496 [INFO] - Epoch: 4/130
2023-09-09 06:57:58,348 [INFO] - Training epoch stats:     Loss: 5.8749 - Binary-Cell-Dice: 0.7739 - Binary-Cell-Jacard: 0.6745 - Tissue-MC-Acc.: 0.2289
2023-09-09 07:00:46,736 [INFO] - Validation epoch stats:   Loss: 5.6670 - Binary-Cell-Dice: 0.7804 - Binary-Cell-Jacard: 0.6898 - PQ-Score: 0.5731 - Tissue-MC-Acc.: 0.3028
2023-09-09 07:00:46,740 [INFO] - New best model - save checkpoint
2023-09-09 07:03:22,022 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-09 07:03:22,028 [INFO] - Epoch: 5/130
2023-09-09 07:06:15,870 [INFO] - Training epoch stats:     Loss: 5.7811 - Binary-Cell-Dice: 0.7822 - Binary-Cell-Jacard: 0.6821 - Tissue-MC-Acc.: 0.2372
2023-09-09 07:08:48,594 [INFO] - Validation epoch stats:   Loss: 5.5960 - Binary-Cell-Dice: 0.7790 - Binary-Cell-Jacard: 0.6843 - PQ-Score: 0.5737 - Tissue-MC-Acc.: 0.3048
2023-09-09 07:08:48,603 [INFO] - New best model - save checkpoint
2023-09-09 07:11:06,302 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-09 07:11:06,304 [INFO] - Epoch: 6/130
2023-09-09 07:15:36,691 [INFO] - Training epoch stats:     Loss: 5.7650 - Binary-Cell-Dice: 0.7845 - Binary-Cell-Jacard: 0.6808 - Tissue-MC-Acc.: 0.2568
2023-09-09 07:18:47,941 [INFO] - Validation epoch stats:   Loss: 5.6256 - Binary-Cell-Dice: 0.7841 - Binary-Cell-Jacard: 0.6917 - PQ-Score: 0.5744 - Tissue-MC-Acc.: 0.3048
2023-09-09 07:18:47,950 [INFO] - New best model - save checkpoint
2023-09-09 07:26:03,853 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-09 07:26:03,862 [INFO] - Epoch: 7/130
2023-09-09 07:28:54,223 [INFO] - Training epoch stats:     Loss: 5.7338 - Binary-Cell-Dice: 0.7882 - Binary-Cell-Jacard: 0.6860 - Tissue-MC-Acc.: 0.2387
2023-09-09 07:32:56,064 [INFO] - Validation epoch stats:   Loss: 5.5193 - Binary-Cell-Dice: 0.7830 - Binary-Cell-Jacard: 0.6959 - PQ-Score: 0.5857 - Tissue-MC-Acc.: 0.3068
2023-09-09 07:32:56,069 [INFO] - New best model - save checkpoint
2023-09-09 07:34:13,943 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-09 07:34:13,943 [INFO] - Epoch: 8/130
2023-09-09 07:37:55,228 [INFO] - Training epoch stats:     Loss: 5.6933 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.6899 - Tissue-MC-Acc.: 0.2534
2023-09-09 07:40:52,232 [INFO] - Validation epoch stats:   Loss: 5.4978 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6961 - PQ-Score: 0.5831 - Tissue-MC-Acc.: 0.3068
2023-09-09 07:41:30,881 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-09 07:41:30,882 [INFO] - Epoch: 9/130
2023-09-09 07:45:43,334 [INFO] - Training epoch stats:     Loss: 5.6339 - Binary-Cell-Dice: 0.7829 - Binary-Cell-Jacard: 0.6865 - Tissue-MC-Acc.: 0.2459
2023-09-09 07:48:12,601 [INFO] - Validation epoch stats:   Loss: 5.4747 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.6990 - PQ-Score: 0.5878 - Tissue-MC-Acc.: 0.3084
2023-09-09 07:48:12,605 [INFO] - New best model - save checkpoint
2023-09-09 07:49:33,011 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-09 07:49:33,012 [INFO] - Epoch: 10/130
2023-09-09 07:54:11,883 [INFO] - Training epoch stats:     Loss: 5.6024 - Binary-Cell-Dice: 0.7918 - Binary-Cell-Jacard: 0.6945 - Tissue-MC-Acc.: 0.2609
2023-09-09 07:56:18,654 [INFO] - Validation epoch stats:   Loss: 5.5051 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6981 - PQ-Score: 0.5862 - Tissue-MC-Acc.: 0.3084
2023-09-09 07:57:33,759 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-09 07:57:33,760 [INFO] - Epoch: 11/130
2023-09-09 07:59:56,874 [INFO] - Training epoch stats:     Loss: 5.6027 - Binary-Cell-Dice: 0.7925 - Binary-Cell-Jacard: 0.6959 - Tissue-MC-Acc.: 0.2511
2023-09-09 08:02:07,127 [INFO] - Validation epoch stats:   Loss: 5.4588 - Binary-Cell-Dice: 0.7845 - Binary-Cell-Jacard: 0.6845 - PQ-Score: 0.5882 - Tissue-MC-Acc.: 0.3088
2023-09-09 08:02:07,137 [INFO] - New best model - save checkpoint
2023-09-09 08:05:06,944 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-09 08:05:06,947 [INFO] - Epoch: 12/130
2023-09-09 08:09:58,467 [INFO] - Training epoch stats:     Loss: 5.5848 - Binary-Cell-Dice: 0.7886 - Binary-Cell-Jacard: 0.6925 - Tissue-MC-Acc.: 0.2523
2023-09-09 08:12:02,468 [INFO] - Validation epoch stats:   Loss: 5.3929 - Binary-Cell-Dice: 0.7858 - Binary-Cell-Jacard: 0.6997 - PQ-Score: 0.5907 - Tissue-MC-Acc.: 0.3123
2023-09-09 08:12:02,478 [INFO] - New best model - save checkpoint
2023-09-09 08:13:40,864 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-09 08:13:40,867 [INFO] - Epoch: 13/130
2023-09-09 08:19:00,544 [INFO] - Training epoch stats:     Loss: 5.5168 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7007 - Tissue-MC-Acc.: 0.2564
2023-09-09 08:21:53,086 [INFO] - Validation epoch stats:   Loss: 5.3964 - Binary-Cell-Dice: 0.7880 - Binary-Cell-Jacard: 0.7008 - PQ-Score: 0.5903 - Tissue-MC-Acc.: 0.3159
2023-09-09 08:22:40,065 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-09 08:22:40,065 [INFO] - Epoch: 14/130
2023-09-09 08:26:05,120 [INFO] - Training epoch stats:     Loss: 5.5047 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7029 - Tissue-MC-Acc.: 0.2605
2023-09-09 08:28:25,271 [INFO] - Validation epoch stats:   Loss: 5.4336 - Binary-Cell-Dice: 0.7883 - Binary-Cell-Jacard: 0.7033 - PQ-Score: 0.5975 - Tissue-MC-Acc.: 0.3147
2023-09-09 08:28:25,283 [INFO] - New best model - save checkpoint
2023-09-09 08:31:50,070 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-09 08:31:50,072 [INFO] - Epoch: 15/130
2023-09-09 08:37:00,852 [INFO] - Training epoch stats:     Loss: 5.4877 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7007 - Tissue-MC-Acc.: 0.2504
2023-09-09 08:39:20,654 [INFO] - Validation epoch stats:   Loss: 5.3539 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.7028 - PQ-Score: 0.5951 - Tissue-MC-Acc.: 0.3155
2023-09-09 08:42:50,001 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-09 08:42:50,011 [INFO] - Epoch: 16/130
2023-09-09 08:46:17,898 [INFO] - Training epoch stats:     Loss: 5.4751 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7046 - Tissue-MC-Acc.: 0.2398
2023-09-09 08:49:30,946 [INFO] - Validation epoch stats:   Loss: 5.3477 - Binary-Cell-Dice: 0.7896 - Binary-Cell-Jacard: 0.7054 - PQ-Score: 0.5976 - Tissue-MC-Acc.: 0.3203
2023-09-09 08:49:30,957 [INFO] - New best model - save checkpoint
2023-09-09 08:52:12,964 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-09 08:52:12,966 [INFO] - Epoch: 17/130
2023-09-09 08:56:54,740 [INFO] - Training epoch stats:     Loss: 5.4770 - Binary-Cell-Dice: 0.7974 - Binary-Cell-Jacard: 0.7050 - Tissue-MC-Acc.: 0.2669
2023-09-09 09:00:14,100 [INFO] - Validation epoch stats:   Loss: 5.3647 - Binary-Cell-Dice: 0.7889 - Binary-Cell-Jacard: 0.7039 - PQ-Score: 0.5953 - Tissue-MC-Acc.: 0.3183
2023-09-09 09:02:12,529 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-09 09:02:12,585 [INFO] - Epoch: 18/130
2023-09-09 09:04:58,153 [INFO] - Training epoch stats:     Loss: 5.5063 - Binary-Cell-Dice: 0.8001 - Binary-Cell-Jacard: 0.7033 - Tissue-MC-Acc.: 0.2492
2023-09-09 09:07:02,350 [INFO] - Validation epoch stats:   Loss: 5.3045 - Binary-Cell-Dice: 0.7905 - Binary-Cell-Jacard: 0.7063 - PQ-Score: 0.5963 - Tissue-MC-Acc.: 0.3199
2023-09-09 09:08:08,817 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-09 09:08:08,818 [INFO] - Epoch: 19/130
2023-09-09 09:11:01,535 [INFO] - Training epoch stats:     Loss: 5.4291 - Binary-Cell-Dice: 0.7994 - Binary-Cell-Jacard: 0.7066 - Tissue-MC-Acc.: 0.2451
2023-09-09 09:13:04,620 [INFO] - Validation epoch stats:   Loss: 5.3352 - Binary-Cell-Dice: 0.7886 - Binary-Cell-Jacard: 0.7053 - PQ-Score: 0.5975 - Tissue-MC-Acc.: 0.3218
2023-09-09 09:14:02,729 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-09 09:14:02,729 [INFO] - Epoch: 20/130
2023-09-09 09:18:12,311 [INFO] - Training epoch stats:     Loss: 5.4241 - Binary-Cell-Dice: 0.7958 - Binary-Cell-Jacard: 0.7051 - Tissue-MC-Acc.: 0.2451
2023-09-09 09:20:50,295 [INFO] - Validation epoch stats:   Loss: 5.3354 - Binary-Cell-Dice: 0.7862 - Binary-Cell-Jacard: 0.7030 - PQ-Score: 0.5953 - Tissue-MC-Acc.: 0.3207
2023-09-09 09:22:46,864 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-09 09:22:46,867 [INFO] - Epoch: 21/130
2023-09-09 09:27:42,291 [INFO] - Training epoch stats:     Loss: 5.3918 - Binary-Cell-Dice: 0.8017 - Binary-Cell-Jacard: 0.7086 - Tissue-MC-Acc.: 0.2462
2023-09-09 09:29:53,055 [INFO] - Validation epoch stats:   Loss: 5.3052 - Binary-Cell-Dice: 0.7904 - Binary-Cell-Jacard: 0.7065 - PQ-Score: 0.5996 - Tissue-MC-Acc.: 0.3222
2023-09-09 09:29:53,198 [INFO] - New best model - save checkpoint
2023-09-09 09:33:31,601 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-09 09:33:31,603 [INFO] - Epoch: 22/130
2023-09-09 09:37:33,317 [INFO] - Training epoch stats:     Loss: 5.3851 - Binary-Cell-Dice: 0.8059 - Binary-Cell-Jacard: 0.7118 - Tissue-MC-Acc.: 0.2602
2023-09-09 09:39:57,169 [INFO] - Validation epoch stats:   Loss: 5.3004 - Binary-Cell-Dice: 0.7884 - Binary-Cell-Jacard: 0.7055 - PQ-Score: 0.5967 - Tissue-MC-Acc.: 0.3195
2023-09-09 09:40:45,647 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-09 09:40:45,648 [INFO] - Epoch: 23/130
2023-09-09 09:45:34,727 [INFO] - Training epoch stats:     Loss: 5.4181 - Binary-Cell-Dice: 0.8093 - Binary-Cell-Jacard: 0.7170 - Tissue-MC-Acc.: 0.2572
2023-09-09 09:47:38,777 [INFO] - Validation epoch stats:   Loss: 5.2905 - Binary-Cell-Dice: 0.7909 - Binary-Cell-Jacard: 0.7061 - PQ-Score: 0.6008 - Tissue-MC-Acc.: 0.3199
2023-09-09 09:47:38,781 [INFO] - New best model - save checkpoint
2023-09-09 09:50:23,204 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-09 09:50:23,206 [INFO] - Epoch: 24/130
2023-09-09 09:55:11,456 [INFO] - Training epoch stats:     Loss: 5.3593 - Binary-Cell-Dice: 0.8124 - Binary-Cell-Jacard: 0.7137 - Tissue-MC-Acc.: 0.2598
2023-09-09 09:57:35,890 [INFO] - Validation epoch stats:   Loss: 5.2720 - Binary-Cell-Dice: 0.7925 - Binary-Cell-Jacard: 0.7104 - PQ-Score: 0.6016 - Tissue-MC-Acc.: 0.3187
2023-09-09 09:57:35,900 [INFO] - New best model - save checkpoint
2023-09-09 10:04:12,924 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-09 10:04:13,093 [INFO] - Epoch: 25/130
2023-09-09 10:08:07,203 [INFO] - Training epoch stats:     Loss: 5.3761 - Binary-Cell-Dice: 0.8060 - Binary-Cell-Jacard: 0.7131 - Tissue-MC-Acc.: 0.2628
2023-09-09 10:10:27,365 [INFO] - Validation epoch stats:   Loss: 5.2753 - Binary-Cell-Dice: 0.7905 - Binary-Cell-Jacard: 0.7068 - PQ-Score: 0.5967 - Tissue-MC-Acc.: 0.3214
2023-09-09 10:13:19,742 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-09 10:13:19,807 [INFO] - Epoch: 26/130
2023-09-09 10:17:46,925 [INFO] - Training epoch stats:     Loss: 5.4859 - Binary-Cell-Dice: 0.7913 - Binary-Cell-Jacard: 0.6949 - Tissue-MC-Acc.: 0.3249
2023-09-09 10:19:57,248 [INFO] - Validation epoch stats:   Loss: 5.3841 - Binary-Cell-Dice: 0.7910 - Binary-Cell-Jacard: 0.7075 - PQ-Score: 0.5950 - Tissue-MC-Acc.: 0.4598
2023-09-09 10:23:36,413 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-09 10:23:36,417 [INFO] - Epoch: 27/130
2023-09-09 10:26:49,338 [INFO] - Training epoch stats:     Loss: 5.3977 - Binary-Cell-Dice: 0.8082 - Binary-Cell-Jacard: 0.7078 - Tissue-MC-Acc.: 0.4386
2023-09-09 10:29:18,768 [INFO] - Validation epoch stats:   Loss: 5.3348 - Binary-Cell-Dice: 0.7880 - Binary-Cell-Jacard: 0.7025 - PQ-Score: 0.5984 - Tissue-MC-Acc.: 0.5184
2023-09-09 10:35:13,605 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-09 10:35:13,701 [INFO] - Epoch: 28/130
2023-09-09 10:39:56,015 [INFO] - Training epoch stats:     Loss: 5.3146 - Binary-Cell-Dice: 0.8013 - Binary-Cell-Jacard: 0.7125 - Tissue-MC-Acc.: 0.4755
2023-09-09 10:42:24,065 [INFO] - Validation epoch stats:   Loss: 5.3083 - Binary-Cell-Dice: 0.7863 - Binary-Cell-Jacard: 0.7024 - PQ-Score: 0.5926 - Tissue-MC-Acc.: 0.4923
2023-09-09 10:46:10,128 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-09 10:46:10,131 [INFO] - Epoch: 29/130
2023-09-09 10:49:05,994 [INFO] - Training epoch stats:     Loss: 5.2603 - Binary-Cell-Dice: 0.8036 - Binary-Cell-Jacard: 0.7137 - Tissue-MC-Acc.: 0.5060
2023-09-09 10:51:20,221 [INFO] - Validation epoch stats:   Loss: 5.2178 - Binary-Cell-Dice: 0.7914 - Binary-Cell-Jacard: 0.7092 - PQ-Score: 0.5981 - Tissue-MC-Acc.: 0.5569
2023-09-09 10:54:26,300 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-09 10:54:26,304 [INFO] - Epoch: 30/130
2023-09-09 10:58:30,510 [INFO] - Training epoch stats:     Loss: 5.2970 - Binary-Cell-Dice: 0.8066 - Binary-Cell-Jacard: 0.7142 - Tissue-MC-Acc.: 0.5260
2023-09-09 11:00:46,267 [INFO] - Validation epoch stats:   Loss: 5.1581 - Binary-Cell-Dice: 0.7964 - Binary-Cell-Jacard: 0.7126 - PQ-Score: 0.6110 - Tissue-MC-Acc.: 0.5866
2023-09-09 11:00:46,277 [INFO] - New best model - save checkpoint
2023-09-09 11:08:47,613 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-09 11:08:47,614 [INFO] - Epoch: 31/130
2023-09-09 11:13:40,364 [INFO] - Training epoch stats:     Loss: 5.1949 - Binary-Cell-Dice: 0.8120 - Binary-Cell-Jacard: 0.7247 - Tissue-MC-Acc.: 0.5350
2023-09-09 11:15:52,305 [INFO] - Validation epoch stats:   Loss: 5.1634 - Binary-Cell-Dice: 0.7921 - Binary-Cell-Jacard: 0.7096 - PQ-Score: 0.5974 - Tissue-MC-Acc.: 0.5937
2023-09-09 11:18:18,645 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-09 11:18:18,647 [INFO] - Epoch: 32/130
2023-09-09 11:21:30,504 [INFO] - Training epoch stats:     Loss: 5.1821 - Binary-Cell-Dice: 0.8066 - Binary-Cell-Jacard: 0.7191 - Tissue-MC-Acc.: 0.5723
2023-09-09 11:23:41,610 [INFO] - Validation epoch stats:   Loss: 5.1122 - Binary-Cell-Dice: 0.7980 - Binary-Cell-Jacard: 0.7166 - PQ-Score: 0.6120 - Tissue-MC-Acc.: 0.6116
2023-09-09 11:23:41,614 [INFO] - New best model - save checkpoint
2023-09-09 11:27:32,402 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-09 11:27:32,403 [INFO] - Epoch: 33/130
2023-09-09 11:32:23,450 [INFO] - Training epoch stats:     Loss: 5.1856 - Binary-Cell-Dice: 0.8094 - Binary-Cell-Jacard: 0.7203 - Tissue-MC-Acc.: 0.6167
2023-09-09 11:34:47,548 [INFO] - Validation epoch stats:   Loss: 5.1167 - Binary-Cell-Dice: 0.7972 - Binary-Cell-Jacard: 0.7181 - PQ-Score: 0.6113 - Tissue-MC-Acc.: 0.5981
2023-09-09 11:40:58,801 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-09 11:40:58,850 [INFO] - Epoch: 34/130
2023-09-09 11:44:06,118 [INFO] - Training epoch stats:     Loss: 5.1225 - Binary-Cell-Dice: 0.8131 - Binary-Cell-Jacard: 0.7264 - Tissue-MC-Acc.: 0.6096
2023-09-09 11:46:29,267 [INFO] - Validation epoch stats:   Loss: 5.1219 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7171 - PQ-Score: 0.6116 - Tissue-MC-Acc.: 0.6231
2023-09-09 11:50:07,006 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-09 11:50:07,008 [INFO] - Epoch: 35/130
2023-09-09 11:53:14,986 [INFO] - Training epoch stats:     Loss: 5.1012 - Binary-Cell-Dice: 0.8111 - Binary-Cell-Jacard: 0.7246 - Tissue-MC-Acc.: 0.6401
2023-09-09 11:55:24,624 [INFO] - Validation epoch stats:   Loss: 5.1148 - Binary-Cell-Dice: 0.7957 - Binary-Cell-Jacard: 0.7152 - PQ-Score: 0.6097 - Tissue-MC-Acc.: 0.6433
2023-09-09 11:55:44,462 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-09 11:55:44,463 [INFO] - Epoch: 36/130
2023-09-09 12:00:17,423 [INFO] - Training epoch stats:     Loss: 5.0623 - Binary-Cell-Dice: 0.8137 - Binary-Cell-Jacard: 0.7255 - Tissue-MC-Acc.: 0.6581
2023-09-09 12:02:41,275 [INFO] - Validation epoch stats:   Loss: 5.0711 - Binary-Cell-Dice: 0.7990 - Binary-Cell-Jacard: 0.7177 - PQ-Score: 0.6152 - Tissue-MC-Acc.: 0.6730
2023-09-09 12:02:41,277 [INFO] - New best model - save checkpoint
2023-09-09 12:03:41,097 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-09 12:03:41,098 [INFO] - Epoch: 37/130
2023-09-09 12:06:45,045 [INFO] - Training epoch stats:     Loss: 5.0353 - Binary-Cell-Dice: 0.8192 - Binary-Cell-Jacard: 0.7309 - Tissue-MC-Acc.: 0.6702
2023-09-09 12:09:01,631 [INFO] - Validation epoch stats:   Loss: 5.0723 - Binary-Cell-Dice: 0.7992 - Binary-Cell-Jacard: 0.7192 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.6861
2023-09-09 12:12:22,506 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-09 12:12:22,507 [INFO] - Epoch: 38/130
2023-09-09 12:15:48,572 [INFO] - Training epoch stats:     Loss: 5.0449 - Binary-Cell-Dice: 0.8169 - Binary-Cell-Jacard: 0.7330 - Tissue-MC-Acc.: 0.6864
2023-09-09 12:17:51,387 [INFO] - Validation epoch stats:   Loss: 5.0547 - Binary-Cell-Dice: 0.7981 - Binary-Cell-Jacard: 0.7194 - PQ-Score: 0.6151 - Tissue-MC-Acc.: 0.6603
2023-09-09 12:20:48,386 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-09 12:20:48,391 [INFO] - Epoch: 39/130
2023-09-09 12:24:02,203 [INFO] - Training epoch stats:     Loss: 5.0002 - Binary-Cell-Dice: 0.8180 - Binary-Cell-Jacard: 0.7342 - Tissue-MC-Acc.: 0.7154
2023-09-09 12:26:08,967 [INFO] - Validation epoch stats:   Loss: 5.0495 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7188 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.7079
2023-09-09 12:27:40,248 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-09 12:27:40,418 [INFO] - Epoch: 40/130
2023-09-09 12:30:47,901 [INFO] - Training epoch stats:     Loss: 4.9659 - Binary-Cell-Dice: 0.8178 - Binary-Cell-Jacard: 0.7361 - Tissue-MC-Acc.: 0.7282
2023-09-09 12:32:46,875 [INFO] - Validation epoch stats:   Loss: 5.0666 - Binary-Cell-Dice: 0.7986 - Binary-Cell-Jacard: 0.7192 - PQ-Score: 0.6153 - Tissue-MC-Acc.: 0.7027
2023-09-09 12:32:46,878 [INFO] - New best model - save checkpoint
2023-09-09 12:33:56,231 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-09 12:33:56,232 [INFO] - Epoch: 41/130
2023-09-09 12:39:46,439 [INFO] - Training epoch stats:     Loss: 4.9286 - Binary-Cell-Dice: 0.8212 - Binary-Cell-Jacard: 0.7381 - Tissue-MC-Acc.: 0.7549
2023-09-09 12:41:55,242 [INFO] - Validation epoch stats:   Loss: 5.0319 - Binary-Cell-Dice: 0.8004 - Binary-Cell-Jacard: 0.7219 - PQ-Score: 0.6175 - Tissue-MC-Acc.: 0.7412
2023-09-09 12:41:55,251 [INFO] - New best model - save checkpoint
2023-09-09 12:46:13,241 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-09 12:46:13,242 [INFO] - Epoch: 42/130
2023-09-09 12:51:09,297 [INFO] - Training epoch stats:     Loss: 4.9228 - Binary-Cell-Dice: 0.8187 - Binary-Cell-Jacard: 0.7401 - Tissue-MC-Acc.: 0.7801
2023-09-09 12:53:34,333 [INFO] - Validation epoch stats:   Loss: 5.0126 - Binary-Cell-Dice: 0.8004 - Binary-Cell-Jacard: 0.7199 - PQ-Score: 0.6183 - Tissue-MC-Acc.: 0.7582
2023-09-09 12:53:34,336 [INFO] - New best model - save checkpoint
2023-09-09 12:54:16,167 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-09 12:54:16,168 [INFO] - Epoch: 43/130
2023-09-09 12:57:20,380 [INFO] - Training epoch stats:     Loss: 4.9113 - Binary-Cell-Dice: 0.8150 - Binary-Cell-Jacard: 0.7334 - Tissue-MC-Acc.: 0.8027
2023-09-09 13:00:58,370 [INFO] - Validation epoch stats:   Loss: 5.0054 - Binary-Cell-Dice: 0.8000 - Binary-Cell-Jacard: 0.7226 - PQ-Score: 0.6185 - Tissue-MC-Acc.: 0.7515
2023-09-09 13:00:58,373 [INFO] - New best model - save checkpoint
2023-09-09 13:02:17,639 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-09 13:02:17,639 [INFO] - Epoch: 44/130
2023-09-09 13:06:12,015 [INFO] - Training epoch stats:     Loss: 4.9019 - Binary-Cell-Dice: 0.8193 - Binary-Cell-Jacard: 0.7407 - Tissue-MC-Acc.: 0.8144
2023-09-09 13:08:36,835 [INFO] - Validation epoch stats:   Loss: 4.9939 - Binary-Cell-Dice: 0.8011 - Binary-Cell-Jacard: 0.7237 - PQ-Score: 0.6175 - Tissue-MC-Acc.: 0.7800
2023-09-09 13:10:20,198 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-09 13:10:20,201 [INFO] - Epoch: 45/130
2023-09-09 13:15:19,069 [INFO] - Training epoch stats:     Loss: 4.8733 - Binary-Cell-Dice: 0.8217 - Binary-Cell-Jacard: 0.7440 - Tissue-MC-Acc.: 0.8302
2023-09-09 13:18:06,466 [INFO] - Validation epoch stats:   Loss: 5.0081 - Binary-Cell-Dice: 0.7965 - Binary-Cell-Jacard: 0.7207 - PQ-Score: 0.6134 - Tissue-MC-Acc.: 0.7769
2023-09-09 13:20:25,614 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-09 13:20:25,615 [INFO] - Epoch: 46/130
2023-09-09 13:25:34,797 [INFO] - Training epoch stats:     Loss: 4.8709 - Binary-Cell-Dice: 0.8164 - Binary-Cell-Jacard: 0.7402 - Tissue-MC-Acc.: 0.8554
2023-09-09 13:27:44,655 [INFO] - Validation epoch stats:   Loss: 5.0103 - Binary-Cell-Dice: 0.7992 - Binary-Cell-Jacard: 0.7216 - PQ-Score: 0.6154 - Tissue-MC-Acc.: 0.7788
2023-09-09 13:31:52,512 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-09 13:31:52,519 [INFO] - Epoch: 47/130
2023-09-09 13:35:00,908 [INFO] - Training epoch stats:     Loss: 4.8131 - Binary-Cell-Dice: 0.8190 - Binary-Cell-Jacard: 0.7438 - Tissue-MC-Acc.: 0.8528
2023-09-09 13:37:09,246 [INFO] - Validation epoch stats:   Loss: 4.9653 - Binary-Cell-Dice: 0.8025 - Binary-Cell-Jacard: 0.7273 - PQ-Score: 0.6221 - Tissue-MC-Acc.: 0.8133
2023-09-09 13:37:09,249 [INFO] - New best model - save checkpoint
2023-09-09 13:39:08,345 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-09 13:39:08,353 [INFO] - Epoch: 48/130
2023-09-09 13:44:54,843 [INFO] - Training epoch stats:     Loss: 4.8393 - Binary-Cell-Dice: 0.8276 - Binary-Cell-Jacard: 0.7504 - Tissue-MC-Acc.: 0.8765
2023-09-09 13:47:37,222 [INFO] - Validation epoch stats:   Loss: 4.9685 - Binary-Cell-Dice: 0.8028 - Binary-Cell-Jacard: 0.7251 - PQ-Score: 0.6210 - Tissue-MC-Acc.: 0.8264
2023-09-09 13:48:00,068 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-09 13:48:00,069 [INFO] - Epoch: 49/130
2023-09-09 13:51:04,048 [INFO] - Training epoch stats:     Loss: 4.7909 - Binary-Cell-Dice: 0.8264 - Binary-Cell-Jacard: 0.7474 - Tissue-MC-Acc.: 0.8859
2023-09-09 13:53:42,377 [INFO] - Validation epoch stats:   Loss: 4.9743 - Binary-Cell-Dice: 0.8007 - Binary-Cell-Jacard: 0.7244 - PQ-Score: 0.6173 - Tissue-MC-Acc.: 0.8216
2023-09-09 13:56:11,133 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-09 13:56:11,134 [INFO] - Epoch: 50/130
2023-09-09 13:59:54,112 [INFO] - Training epoch stats:     Loss: 4.8161 - Binary-Cell-Dice: 0.8240 - Binary-Cell-Jacard: 0.7471 - Tissue-MC-Acc.: 0.8863
2023-09-09 14:02:11,408 [INFO] - Validation epoch stats:   Loss: 4.9591 - Binary-Cell-Dice: 0.8006 - Binary-Cell-Jacard: 0.7251 - PQ-Score: 0.6200 - Tissue-MC-Acc.: 0.8252
2023-09-09 14:04:45,945 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-09 14:04:45,946 [INFO] - Epoch: 51/130
2023-09-09 14:09:45,495 [INFO] - Training epoch stats:     Loss: 4.8023 - Binary-Cell-Dice: 0.8236 - Binary-Cell-Jacard: 0.7499 - Tissue-MC-Acc.: 0.8953
2023-09-09 14:11:55,802 [INFO] - Validation epoch stats:   Loss: 4.9882 - Binary-Cell-Dice: 0.7998 - Binary-Cell-Jacard: 0.7239 - PQ-Score: 0.6191 - Tissue-MC-Acc.: 0.8379
2023-09-09 14:14:39,930 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-09 14:14:39,938 [INFO] - Epoch: 52/130
2023-09-09 14:18:55,264 [INFO] - Training epoch stats:     Loss: 4.7571 - Binary-Cell-Dice: 0.8221 - Binary-Cell-Jacard: 0.7511 - Tissue-MC-Acc.: 0.9153
2023-09-09 14:21:19,303 [INFO] - Validation epoch stats:   Loss: 4.9554 - Binary-Cell-Dice: 0.8022 - Binary-Cell-Jacard: 0.7265 - PQ-Score: 0.6220 - Tissue-MC-Acc.: 0.8260
2023-09-09 14:24:15,461 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-09 14:24:15,466 [INFO] - Epoch: 53/130
2023-09-09 14:28:51,264 [INFO] - Training epoch stats:     Loss: 4.7798 - Binary-Cell-Dice: 0.8249 - Binary-Cell-Jacard: 0.7494 - Tissue-MC-Acc.: 0.9270
2023-09-09 14:30:59,905 [INFO] - Validation epoch stats:   Loss: 4.9558 - Binary-Cell-Dice: 0.8003 - Binary-Cell-Jacard: 0.7246 - PQ-Score: 0.6202 - Tissue-MC-Acc.: 0.8458
2023-09-09 14:33:39,226 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-09 14:33:39,232 [INFO] - Epoch: 54/130
2023-09-09 14:38:41,489 [INFO] - Training epoch stats:     Loss: 4.7563 - Binary-Cell-Dice: 0.8289 - Binary-Cell-Jacard: 0.7510 - Tissue-MC-Acc.: 0.9232
2023-09-09 14:41:12,850 [INFO] - Validation epoch stats:   Loss: 4.9488 - Binary-Cell-Dice: 0.8007 - Binary-Cell-Jacard: 0.7258 - PQ-Score: 0.6207 - Tissue-MC-Acc.: 0.8490
2023-09-09 14:41:41,975 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-09 14:41:41,975 [INFO] - Epoch: 55/130
2023-09-09 14:45:43,521 [INFO] - Training epoch stats:     Loss: 4.7733 - Binary-Cell-Dice: 0.8238 - Binary-Cell-Jacard: 0.7503 - Tissue-MC-Acc.: 0.9360
2023-09-09 14:48:23,463 [INFO] - Validation epoch stats:   Loss: 4.9450 - Binary-Cell-Dice: 0.8035 - Binary-Cell-Jacard: 0.7281 - PQ-Score: 0.6221 - Tissue-MC-Acc.: 0.8462
2023-09-09 14:49:30,551 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-09 14:49:30,552 [INFO] - Epoch: 56/130
2023-09-09 14:54:14,103 [INFO] - Training epoch stats:     Loss: 4.7315 - Binary-Cell-Dice: 0.8276 - Binary-Cell-Jacard: 0.7527 - Tissue-MC-Acc.: 0.9383
2023-09-09 14:56:47,646 [INFO] - Validation epoch stats:   Loss: 4.9486 - Binary-Cell-Dice: 0.8021 - Binary-Cell-Jacard: 0.7268 - PQ-Score: 0.6214 - Tissue-MC-Acc.: 0.8581
2023-09-09 14:57:28,426 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-09 14:57:28,427 [INFO] - Epoch: 57/130
2023-09-09 15:02:30,035 [INFO] - Training epoch stats:     Loss: 4.7318 - Binary-Cell-Dice: 0.8285 - Binary-Cell-Jacard: 0.7559 - Tissue-MC-Acc.: 0.9499
2023-09-09 15:04:58,196 [INFO] - Validation epoch stats:   Loss: 4.9306 - Binary-Cell-Dice: 0.8023 - Binary-Cell-Jacard: 0.7285 - PQ-Score: 0.6226 - Tissue-MC-Acc.: 0.8637
2023-09-09 15:04:58,204 [INFO] - New best model - save checkpoint
2023-09-09 15:10:03,735 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-09 15:10:03,740 [INFO] - Epoch: 58/130
2023-09-09 15:16:29,772 [INFO] - Training epoch stats:     Loss: 4.7261 - Binary-Cell-Dice: 0.8331 - Binary-Cell-Jacard: 0.7563 - Tissue-MC-Acc.: 0.9556
2023-09-09 15:18:55,761 [INFO] - Validation epoch stats:   Loss: 4.9411 - Binary-Cell-Dice: 0.8016 - Binary-Cell-Jacard: 0.7273 - PQ-Score: 0.6223 - Tissue-MC-Acc.: 0.8716
2023-09-09 15:21:47,320 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-09 15:21:47,329 [INFO] - Epoch: 59/130
2023-09-09 15:24:54,805 [INFO] - Training epoch stats:     Loss: 4.7149 - Binary-Cell-Dice: 0.8323 - Binary-Cell-Jacard: 0.7571 - Tissue-MC-Acc.: 0.9477
2023-09-09 15:27:15,993 [INFO] - Validation epoch stats:   Loss: 4.9549 - Binary-Cell-Dice: 0.8014 - Binary-Cell-Jacard: 0.7265 - PQ-Score: 0.6225 - Tissue-MC-Acc.: 0.8656
2023-09-09 15:32:07,136 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-09 15:32:07,137 [INFO] - Epoch: 60/130
2023-09-09 15:35:59,679 [INFO] - Training epoch stats:     Loss: 4.7287 - Binary-Cell-Dice: 0.8234 - Binary-Cell-Jacard: 0.7516 - Tissue-MC-Acc.: 0.9522
2023-09-09 15:38:15,522 [INFO] - Validation epoch stats:   Loss: 4.9326 - Binary-Cell-Dice: 0.8012 - Binary-Cell-Jacard: 0.7265 - PQ-Score: 0.6216 - Tissue-MC-Acc.: 0.8779
2023-09-09 15:43:08,690 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-09 15:43:08,835 [INFO] - Epoch: 61/130
2023-09-09 15:46:09,338 [INFO] - Training epoch stats:     Loss: 4.6690 - Binary-Cell-Dice: 0.8256 - Binary-Cell-Jacard: 0.7571 - Tissue-MC-Acc.: 0.9529
2023-09-09 15:48:09,075 [INFO] - Validation epoch stats:   Loss: 4.9391 - Binary-Cell-Dice: 0.8004 - Binary-Cell-Jacard: 0.7264 - PQ-Score: 0.6229 - Tissue-MC-Acc.: 0.8759
2023-09-09 15:48:09,077 [INFO] - New best model - save checkpoint
2023-09-09 15:56:04,205 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-09 15:56:04,216 [INFO] - Epoch: 62/130
2023-09-09 15:59:12,445 [INFO] - Training epoch stats:     Loss: 4.6889 - Binary-Cell-Dice: 0.8274 - Binary-Cell-Jacard: 0.7542 - Tissue-MC-Acc.: 0.9593
2023-09-09 16:01:12,265 [INFO] - Validation epoch stats:   Loss: 4.9469 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7283 - PQ-Score: 0.6241 - Tissue-MC-Acc.: 0.8835
2023-09-09 16:01:12,267 [INFO] - New best model - save checkpoint
2023-09-09 16:07:15,630 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-09 16:07:15,642 [INFO] - Epoch: 63/130
2023-09-09 16:10:25,630 [INFO] - Training epoch stats:     Loss: 4.7058 - Binary-Cell-Dice: 0.8362 - Binary-Cell-Jacard: 0.7624 - Tissue-MC-Acc.: 0.9601
2023-09-09 16:12:39,709 [INFO] - Validation epoch stats:   Loss: 4.9335 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7271 - PQ-Score: 0.6213 - Tissue-MC-Acc.: 0.8736
2023-09-09 16:13:27,390 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-09 16:13:27,391 [INFO] - Epoch: 64/130
2023-09-09 16:16:55,495 [INFO] - Training epoch stats:     Loss: 4.6454 - Binary-Cell-Dice: 0.8291 - Binary-Cell-Jacard: 0.7572 - Tissue-MC-Acc.: 0.9684
2023-09-09 16:20:29,166 [INFO] - Validation epoch stats:   Loss: 4.9260 - Binary-Cell-Dice: 0.8005 - Binary-Cell-Jacard: 0.7273 - PQ-Score: 0.6213 - Tissue-MC-Acc.: 0.8819
2023-09-09 16:22:07,329 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-09 16:22:07,330 [INFO] - Epoch: 65/130
2023-09-09 16:26:37,868 [INFO] - Training epoch stats:     Loss: 4.6919 - Binary-Cell-Dice: 0.8324 - Binary-Cell-Jacard: 0.7588 - Tissue-MC-Acc.: 0.9676
2023-09-09 16:29:54,846 [INFO] - Validation epoch stats:   Loss: 4.9333 - Binary-Cell-Dice: 0.8020 - Binary-Cell-Jacard: 0.7273 - PQ-Score: 0.6224 - Tissue-MC-Acc.: 0.8898
2023-09-09 16:32:18,498 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-09 16:32:18,499 [INFO] - Epoch: 66/130
2023-09-09 16:36:01,121 [INFO] - Training epoch stats:     Loss: 4.6744 - Binary-Cell-Dice: 0.8287 - Binary-Cell-Jacard: 0.7580 - Tissue-MC-Acc.: 0.9767
2023-09-09 16:38:31,448 [INFO] - Validation epoch stats:   Loss: 4.9236 - Binary-Cell-Dice: 0.8023 - Binary-Cell-Jacard: 0.7289 - PQ-Score: 0.6237 - Tissue-MC-Acc.: 0.8839
2023-09-09 16:43:04,572 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-09 16:43:04,579 [INFO] - Epoch: 67/130
2023-09-09 16:45:58,126 [INFO] - Training epoch stats:     Loss: 4.6574 - Binary-Cell-Dice: 0.8324 - Binary-Cell-Jacard: 0.7624 - Tissue-MC-Acc.: 0.9748
2023-09-09 16:48:17,422 [INFO] - Validation epoch stats:   Loss: 4.9333 - Binary-Cell-Dice: 0.8021 - Binary-Cell-Jacard: 0.7267 - PQ-Score: 0.6237 - Tissue-MC-Acc.: 0.8866
2023-09-09 16:51:26,458 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-09 16:51:26,459 [INFO] - Epoch: 68/130
2023-09-09 16:54:11,320 [INFO] - Training epoch stats:     Loss: 4.6264 - Binary-Cell-Dice: 0.8381 - Binary-Cell-Jacard: 0.7647 - Tissue-MC-Acc.: 0.9812
2023-09-09 16:56:16,692 [INFO] - Validation epoch stats:   Loss: 4.9241 - Binary-Cell-Dice: 0.8024 - Binary-Cell-Jacard: 0.7287 - PQ-Score: 0.6236 - Tissue-MC-Acc.: 0.8890
2023-09-09 16:56:37,705 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-09 16:56:37,706 [INFO] - Epoch: 69/130
2023-09-09 16:59:42,517 [INFO] - Training epoch stats:     Loss: 4.6836 - Binary-Cell-Dice: 0.8233 - Binary-Cell-Jacard: 0.7594 - Tissue-MC-Acc.: 0.9680
2023-09-09 17:01:49,322 [INFO] - Validation epoch stats:   Loss: 4.9184 - Binary-Cell-Dice: 0.8034 - Binary-Cell-Jacard: 0.7290 - PQ-Score: 0.6255 - Tissue-MC-Acc.: 0.8855
2023-09-09 17:01:49,330 [INFO] - New best model - save checkpoint
2023-09-09 17:08:16,814 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-09 17:08:16,824 [INFO] - Epoch: 70/130
2023-09-09 17:12:02,592 [INFO] - Training epoch stats:     Loss: 4.6372 - Binary-Cell-Dice: 0.8239 - Binary-Cell-Jacard: 0.7560 - Tissue-MC-Acc.: 0.9767
2023-09-09 17:15:32,092 [INFO] - Validation epoch stats:   Loss: 4.9224 - Binary-Cell-Dice: 0.8024 - Binary-Cell-Jacard: 0.7286 - PQ-Score: 0.6238 - Tissue-MC-Acc.: 0.8926
2023-09-09 17:19:58,231 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-09 17:19:58,233 [INFO] - Epoch: 71/130
2023-09-09 17:24:29,903 [INFO] - Training epoch stats:     Loss: 4.6414 - Binary-Cell-Dice: 0.8332 - Binary-Cell-Jacard: 0.7605 - Tissue-MC-Acc.: 0.9793
2023-09-09 17:26:38,596 [INFO] - Validation epoch stats:   Loss: 4.9275 - Binary-Cell-Dice: 0.8003 - Binary-Cell-Jacard: 0.7259 - PQ-Score: 0.6222 - Tissue-MC-Acc.: 0.9013
2023-09-09 17:29:27,063 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-09 17:29:27,067 [INFO] - Epoch: 72/130
2023-09-09 17:32:25,429 [INFO] - Training epoch stats:     Loss: 4.6579 - Binary-Cell-Dice: 0.8305 - Binary-Cell-Jacard: 0.7623 - Tissue-MC-Acc.: 0.9755
2023-09-09 17:34:42,125 [INFO] - Validation epoch stats:   Loss: 4.9267 - Binary-Cell-Dice: 0.8022 - Binary-Cell-Jacard: 0.7275 - PQ-Score: 0.6235 - Tissue-MC-Acc.: 0.8946
2023-09-09 17:38:55,724 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-09 17:38:55,725 [INFO] - Epoch: 73/130
2023-09-09 17:42:52,151 [INFO] - Training epoch stats:     Loss: 4.6162 - Binary-Cell-Dice: 0.8322 - Binary-Cell-Jacard: 0.7620 - Tissue-MC-Acc.: 0.9789
2023-09-09 17:44:50,969 [INFO] - Validation epoch stats:   Loss: 4.9193 - Binary-Cell-Dice: 0.8005 - Binary-Cell-Jacard: 0.7269 - PQ-Score: 0.6219 - Tissue-MC-Acc.: 0.8938
2023-09-09 17:50:23,990 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 17:50:23,996 [INFO] - Epoch: 74/130
2023-09-09 17:53:59,178 [INFO] - Training epoch stats:     Loss: 4.6350 - Binary-Cell-Dice: 0.8355 - Binary-Cell-Jacard: 0.7637 - Tissue-MC-Acc.: 0.9816
2023-09-09 17:56:08,226 [INFO] - Validation epoch stats:   Loss: 4.9207 - Binary-Cell-Dice: 0.8033 - Binary-Cell-Jacard: 0.7302 - PQ-Score: 0.6242 - Tissue-MC-Acc.: 0.8922
2023-09-09 18:03:08,796 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 18:03:08,820 [INFO] - Epoch: 75/130
2023-09-09 18:06:32,865 [INFO] - Training epoch stats:     Loss: 4.6282 - Binary-Cell-Dice: 0.8301 - Binary-Cell-Jacard: 0.7643 - Tissue-MC-Acc.: 0.9812
2023-09-09 18:08:40,720 [INFO] - Validation epoch stats:   Loss: 4.9225 - Binary-Cell-Dice: 0.8017 - Binary-Cell-Jacard: 0.7278 - PQ-Score: 0.6242 - Tissue-MC-Acc.: 0.8946
2023-09-09 18:14:10,797 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-09 18:14:10,803 [INFO] - Epoch: 76/130
2023-09-09 18:18:04,217 [INFO] - Training epoch stats:     Loss: 4.5980 - Binary-Cell-Dice: 0.8310 - Binary-Cell-Jacard: 0.7639 - Tissue-MC-Acc.: 0.9823
2023-09-09 18:20:31,145 [INFO] - Validation epoch stats:   Loss: 4.9354 - Binary-Cell-Dice: 0.8016 - Binary-Cell-Jacard: 0.7273 - PQ-Score: 0.6228 - Tissue-MC-Acc.: 0.8910
2023-09-09 18:24:47,940 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 18:24:47,972 [INFO] - Epoch: 77/130
2023-09-09 18:27:41,066 [INFO] - Training epoch stats:     Loss: 4.6091 - Binary-Cell-Dice: 0.8343 - Binary-Cell-Jacard: 0.7599 - Tissue-MC-Acc.: 0.9789
2023-09-09 18:30:14,452 [INFO] - Validation epoch stats:   Loss: 4.9220 - Binary-Cell-Dice: 0.8025 - Binary-Cell-Jacard: 0.7286 - PQ-Score: 0.6247 - Tissue-MC-Acc.: 0.8878
2023-09-09 18:36:40,848 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 18:36:40,870 [INFO] - Epoch: 78/130
2023-09-09 18:40:39,541 [INFO] - Training epoch stats:     Loss: 4.6186 - Binary-Cell-Dice: 0.8346 - Binary-Cell-Jacard: 0.7652 - Tissue-MC-Acc.: 0.9842
2023-09-09 18:42:58,760 [INFO] - Validation epoch stats:   Loss: 4.9198 - Binary-Cell-Dice: 0.8028 - Binary-Cell-Jacard: 0.7293 - PQ-Score: 0.6252 - Tissue-MC-Acc.: 0.8930
2023-09-09 18:47:37,350 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-09 18:47:37,358 [INFO] - Epoch: 79/130
2023-09-09 18:50:55,364 [INFO] - Training epoch stats:     Loss: 4.5981 - Binary-Cell-Dice: 0.8311 - Binary-Cell-Jacard: 0.7641 - Tissue-MC-Acc.: 0.9778
2023-09-09 18:53:05,002 [INFO] - Validation epoch stats:   Loss: 4.9172 - Binary-Cell-Dice: 0.8034 - Binary-Cell-Jacard: 0.7296 - PQ-Score: 0.6255 - Tissue-MC-Acc.: 0.8954
2023-09-09 18:53:23,993 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 18:53:23,994 [INFO] - Epoch: 80/130
2023-09-09 18:56:01,391 [INFO] - Training epoch stats:     Loss: 4.5903 - Binary-Cell-Dice: 0.8364 - Binary-Cell-Jacard: 0.7632 - Tissue-MC-Acc.: 0.9849
2023-09-09 18:59:32,270 [INFO] - Validation epoch stats:   Loss: 4.9147 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7289 - PQ-Score: 0.6250 - Tissue-MC-Acc.: 0.9005
2023-09-09 19:00:54,605 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 19:00:54,605 [INFO] - Epoch: 81/130
2023-09-09 19:04:03,455 [INFO] - Training epoch stats:     Loss: 4.5841 - Binary-Cell-Dice: 0.8347 - Binary-Cell-Jacard: 0.7669 - Tissue-MC-Acc.: 0.9853
2023-09-09 19:06:04,154 [INFO] - Validation epoch stats:   Loss: 4.9192 - Binary-Cell-Dice: 0.8023 - Binary-Cell-Jacard: 0.7291 - PQ-Score: 0.6240 - Tissue-MC-Acc.: 0.8985
2023-09-09 19:11:30,542 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 19:11:30,554 [INFO] - Epoch: 82/130
2023-09-09 19:15:15,106 [INFO] - Training epoch stats:     Loss: 4.5907 - Binary-Cell-Dice: 0.8332 - Binary-Cell-Jacard: 0.7631 - Tissue-MC-Acc.: 0.9838
2023-09-09 19:17:18,457 [INFO] - Validation epoch stats:   Loss: 4.9275 - Binary-Cell-Dice: 0.8019 - Binary-Cell-Jacard: 0.7287 - PQ-Score: 0.6242 - Tissue-MC-Acc.: 0.8969
2023-09-09 19:21:37,224 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-09 19:21:37,244 [INFO] - Epoch: 83/130
2023-09-09 19:25:13,874 [INFO] - Training epoch stats:     Loss: 4.6026 - Binary-Cell-Dice: 0.8408 - Binary-Cell-Jacard: 0.7704 - Tissue-MC-Acc.: 0.9831
2023-09-09 19:27:07,486 [INFO] - Validation epoch stats:   Loss: 4.9242 - Binary-Cell-Dice: 0.8018 - Binary-Cell-Jacard: 0.7286 - PQ-Score: 0.6237 - Tissue-MC-Acc.: 0.8989
2023-09-09 19:33:08,389 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 19:33:08,397 [INFO] - Epoch: 84/130
2023-09-09 19:35:56,809 [INFO] - Training epoch stats:     Loss: 4.5955 - Binary-Cell-Dice: 0.8372 - Binary-Cell-Jacard: 0.7683 - Tissue-MC-Acc.: 0.9853
2023-09-09 19:38:28,685 [INFO] - Validation epoch stats:   Loss: 4.9239 - Binary-Cell-Dice: 0.8020 - Binary-Cell-Jacard: 0.7285 - PQ-Score: 0.6233 - Tissue-MC-Acc.: 0.9005
2023-09-09 19:42:44,789 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 19:42:44,797 [INFO] - Epoch: 85/130
2023-09-09 19:45:26,637 [INFO] - Training epoch stats:     Loss: 4.5949 - Binary-Cell-Dice: 0.8389 - Binary-Cell-Jacard: 0.7661 - Tissue-MC-Acc.: 0.9872
2023-09-09 19:49:04,499 [INFO] - Validation epoch stats:   Loss: 4.9309 - Binary-Cell-Dice: 0.8021 - Binary-Cell-Jacard: 0.7287 - PQ-Score: 0.6247 - Tissue-MC-Acc.: 0.9009
2023-09-09 19:53:34,404 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 19:53:34,408 [INFO] - Epoch: 86/130
2023-09-09 19:57:11,074 [INFO] - Training epoch stats:     Loss: 4.5923 - Binary-Cell-Dice: 0.8314 - Binary-Cell-Jacard: 0.7643 - Tissue-MC-Acc.: 0.9868
2023-09-09 19:59:27,745 [INFO] - Validation epoch stats:   Loss: 4.9222 - Binary-Cell-Dice: 0.8027 - Binary-Cell-Jacard: 0.7298 - PQ-Score: 0.6258 - Tissue-MC-Acc.: 0.9013
2023-09-09 19:59:27,747 [INFO] - New best model - save checkpoint
2023-09-09 20:02:13,815 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 20:02:13,822 [INFO] - Epoch: 87/130
2023-09-09 20:05:28,330 [INFO] - Training epoch stats:     Loss: 4.5995 - Binary-Cell-Dice: 0.8394 - Binary-Cell-Jacard: 0.7668 - Tissue-MC-Acc.: 0.9861
2023-09-09 20:08:13,260 [INFO] - Validation epoch stats:   Loss: 4.9247 - Binary-Cell-Dice: 0.8018 - Binary-Cell-Jacard: 0.7289 - PQ-Score: 0.6253 - Tissue-MC-Acc.: 0.9025
2023-09-09 20:08:33,977 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-09 20:08:33,978 [INFO] - Epoch: 88/130
2023-09-09 20:11:41,643 [INFO] - Training epoch stats:     Loss: 4.5709 - Binary-Cell-Dice: 0.8341 - Binary-Cell-Jacard: 0.7673 - Tissue-MC-Acc.: 0.9857
2023-09-09 20:13:50,740 [INFO] - Validation epoch stats:   Loss: 4.9208 - Binary-Cell-Dice: 0.8030 - Binary-Cell-Jacard: 0.7297 - PQ-Score: 0.6245 - Tissue-MC-Acc.: 0.8993
2023-09-09 20:16:10,863 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:16:10,871 [INFO] - Epoch: 89/130
2023-09-09 20:19:29,322 [INFO] - Training epoch stats:     Loss: 4.5733 - Binary-Cell-Dice: 0.8300 - Binary-Cell-Jacard: 0.7669 - Tissue-MC-Acc.: 0.9842
2023-09-09 20:22:09,110 [INFO] - Validation epoch stats:   Loss: 4.9261 - Binary-Cell-Dice: 0.8016 - Binary-Cell-Jacard: 0.7288 - PQ-Score: 0.6243 - Tissue-MC-Acc.: 0.8993
2023-09-09 20:24:41,617 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:24:41,745 [INFO] - Epoch: 90/130
2023-09-09 20:27:33,830 [INFO] - Training epoch stats:     Loss: 4.6037 - Binary-Cell-Dice: 0.8377 - Binary-Cell-Jacard: 0.7656 - Tissue-MC-Acc.: 0.9842
2023-09-09 20:29:50,724 [INFO] - Validation epoch stats:   Loss: 4.9228 - Binary-Cell-Dice: 0.8019 - Binary-Cell-Jacard: 0.7290 - PQ-Score: 0.6247 - Tissue-MC-Acc.: 0.9005
2023-09-09 20:31:23,118 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:31:23,118 [INFO] - Epoch: 91/130
2023-09-09 20:35:48,204 [INFO] - Training epoch stats:     Loss: 4.5907 - Binary-Cell-Dice: 0.8419 - Binary-Cell-Jacard: 0.7694 - Tissue-MC-Acc.: 0.9872
2023-09-09 20:38:13,036 [INFO] - Validation epoch stats:   Loss: 4.9177 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7297 - PQ-Score: 0.6264 - Tissue-MC-Acc.: 0.8977
2023-09-09 20:38:13,046 [INFO] - New best model - save checkpoint
2023-09-09 20:40:56,457 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:40:56,470 [INFO] - Epoch: 92/130
2023-09-09 20:43:54,892 [INFO] - Training epoch stats:     Loss: 4.5576 - Binary-Cell-Dice: 0.8327 - Binary-Cell-Jacard: 0.7675 - Tissue-MC-Acc.: 0.9887
2023-09-09 20:47:27,571 [INFO] - Validation epoch stats:   Loss: 4.9228 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7296 - PQ-Score: 0.6250 - Tissue-MC-Acc.: 0.9025
2023-09-09 20:48:32,475 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:48:32,476 [INFO] - Epoch: 93/130
2023-09-09 20:51:20,387 [INFO] - Training epoch stats:     Loss: 4.5711 - Binary-Cell-Dice: 0.8364 - Binary-Cell-Jacard: 0.7670 - Tissue-MC-Acc.: 0.9906
2023-09-09 20:53:28,091 [INFO] - Validation epoch stats:   Loss: 4.9205 - Binary-Cell-Dice: 0.8033 - Binary-Cell-Jacard: 0.7303 - PQ-Score: 0.6257 - Tissue-MC-Acc.: 0.8981
2023-09-09 20:58:13,127 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 20:58:13,128 [INFO] - Epoch: 94/130
2023-09-09 21:01:57,213 [INFO] - Training epoch stats:     Loss: 4.5825 - Binary-Cell-Dice: 0.8386 - Binary-Cell-Jacard: 0.7687 - Tissue-MC-Acc.: 0.9857
2023-09-09 21:04:10,780 [INFO] - Validation epoch stats:   Loss: 4.9189 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7292 - PQ-Score: 0.6264 - Tissue-MC-Acc.: 0.8997
2023-09-09 21:04:10,794 [INFO] - New best model - save checkpoint
2023-09-09 21:11:59,456 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-09 21:11:59,464 [INFO] - Epoch: 95/130
2023-09-09 21:14:52,464 [INFO] - Training epoch stats:     Loss: 4.5690 - Binary-Cell-Dice: 0.8318 - Binary-Cell-Jacard: 0.7668 - Tissue-MC-Acc.: 0.9834
2023-09-09 21:16:53,476 [INFO] - Validation epoch stats:   Loss: 4.9205 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7291 - PQ-Score: 0.6265 - Tissue-MC-Acc.: 0.8973
2023-09-09 21:16:53,484 [INFO] - New best model - save checkpoint
2023-09-09 21:22:33,783 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 21:22:33,790 [INFO] - Epoch: 96/130
2023-09-09 21:25:28,616 [INFO] - Training epoch stats:     Loss: 4.5638 - Binary-Cell-Dice: 0.8429 - Binary-Cell-Jacard: 0.7716 - Tissue-MC-Acc.: 0.9887
2023-09-09 21:28:44,685 [INFO] - Validation epoch stats:   Loss: 4.9160 - Binary-Cell-Dice: 0.8029 - Binary-Cell-Jacard: 0.7296 - PQ-Score: 0.6250 - Tissue-MC-Acc.: 0.9021
2023-09-09 21:34:11,802 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 21:34:11,812 [INFO] - Epoch: 97/130
2023-09-09 21:37:59,069 [INFO] - Training epoch stats:     Loss: 4.5851 - Binary-Cell-Dice: 0.8421 - Binary-Cell-Jacard: 0.7702 - Tissue-MC-Acc.: 0.9872
2023-09-09 21:40:21,256 [INFO] - Validation epoch stats:   Loss: 4.9223 - Binary-Cell-Dice: 0.8019 - Binary-Cell-Jacard: 0.7289 - PQ-Score: 0.6253 - Tissue-MC-Acc.: 0.9025
2023-09-09 21:42:26,370 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 21:42:26,371 [INFO] - Epoch: 98/130
2023-09-09 21:45:41,978 [INFO] - Training epoch stats:     Loss: 4.5733 - Binary-Cell-Dice: 0.8363 - Binary-Cell-Jacard: 0.7698 - Tissue-MC-Acc.: 0.9932
2023-09-09 21:48:43,867 [INFO] - Validation epoch stats:   Loss: 4.9256 - Binary-Cell-Dice: 0.8007 - Binary-Cell-Jacard: 0.7279 - PQ-Score: 0.6239 - Tissue-MC-Acc.: 0.9009
2023-09-09 21:49:01,188 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 21:49:01,189 [INFO] - Epoch: 99/130
2023-09-09 21:52:43,251 [INFO] - Training epoch stats:     Loss: 4.5704 - Binary-Cell-Dice: 0.8369 - Binary-Cell-Jacard: 0.7701 - Tissue-MC-Acc.: 0.9868
2023-09-09 21:54:51,562 [INFO] - Validation epoch stats:   Loss: 4.9309 - Binary-Cell-Dice: 0.8010 - Binary-Cell-Jacard: 0.7281 - PQ-Score: 0.6235 - Tissue-MC-Acc.: 0.9025
2023-09-09 21:56:59,515 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 21:56:59,525 [INFO] - Epoch: 100/130
2023-09-09 21:59:57,265 [INFO] - Training epoch stats:     Loss: 4.5628 - Binary-Cell-Dice: 0.8430 - Binary-Cell-Jacard: 0.7761 - Tissue-MC-Acc.: 0.9880
2023-09-09 22:01:59,557 [INFO] - Validation epoch stats:   Loss: 4.9268 - Binary-Cell-Dice: 0.8029 - Binary-Cell-Jacard: 0.7297 - PQ-Score: 0.6253 - Tissue-MC-Acc.: 0.9017
2023-09-09 22:07:31,199 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 22:07:31,201 [INFO] - Epoch: 101/130
2023-09-09 22:10:16,738 [INFO] - Training epoch stats:     Loss: 4.5879 - Binary-Cell-Dice: 0.8405 - Binary-Cell-Jacard: 0.7683 - Tissue-MC-Acc.: 0.9891
2023-09-09 22:12:16,739 [INFO] - Validation epoch stats:   Loss: 4.9214 - Binary-Cell-Dice: 0.8027 - Binary-Cell-Jacard: 0.7297 - PQ-Score: 0.6257 - Tissue-MC-Acc.: 0.9021
2023-09-09 22:17:48,688 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 22:17:48,807 [INFO] - Epoch: 102/130
2023-09-09 22:21:14,529 [INFO] - Training epoch stats:     Loss: 4.5634 - Binary-Cell-Dice: 0.8325 - Binary-Cell-Jacard: 0.7711 - Tissue-MC-Acc.: 0.9906
2023-09-09 22:23:55,862 [INFO] - Validation epoch stats:   Loss: 4.9237 - Binary-Cell-Dice: 0.8023 - Binary-Cell-Jacard: 0.7292 - PQ-Score: 0.6269 - Tissue-MC-Acc.: 0.9033
2023-09-09 22:23:55,870 [INFO] - New best model - save checkpoint
2023-09-09 22:31:07,061 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 22:31:07,062 [INFO] - Epoch: 103/130
2023-09-09 22:34:01,861 [INFO] - Training epoch stats:     Loss: 4.5472 - Binary-Cell-Dice: 0.8387 - Binary-Cell-Jacard: 0.7703 - Tissue-MC-Acc.: 0.9902
2023-09-09 22:36:46,566 [INFO] - Validation epoch stats:   Loss: 4.9250 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7294 - PQ-Score: 0.6275 - Tissue-MC-Acc.: 0.9033
2023-09-09 22:36:46,577 [INFO] - New best model - save checkpoint
2023-09-09 22:46:01,734 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 22:46:01,896 [INFO] - Epoch: 104/130
2023-09-09 22:49:13,165 [INFO] - Training epoch stats:     Loss: 4.5762 - Binary-Cell-Dice: 0.8376 - Binary-Cell-Jacard: 0.7681 - Tissue-MC-Acc.: 0.9883
2023-09-09 22:51:36,296 [INFO] - Validation epoch stats:   Loss: 4.9239 - Binary-Cell-Dice: 0.8027 - Binary-Cell-Jacard: 0.7298 - PQ-Score: 0.6269 - Tissue-MC-Acc.: 0.9041
2023-09-09 22:55:29,339 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-09 22:55:29,343 [INFO] - Epoch: 105/130
2023-09-09 22:58:38,330 [INFO] - Training epoch stats:     Loss: 4.5563 - Binary-Cell-Dice: 0.8358 - Binary-Cell-Jacard: 0.7720 - Tissue-MC-Acc.: 0.9880
2023-09-09 23:01:09,614 [INFO] - Validation epoch stats:   Loss: 4.9192 - Binary-Cell-Dice: 0.8024 - Binary-Cell-Jacard: 0.7296 - PQ-Score: 0.6265 - Tissue-MC-Acc.: 0.9033
2023-09-09 23:03:49,471 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 23:03:49,480 [INFO] - Epoch: 106/130
2023-09-09 23:07:16,573 [INFO] - Training epoch stats:     Loss: 4.5836 - Binary-Cell-Dice: 0.8408 - Binary-Cell-Jacard: 0.7692 - Tissue-MC-Acc.: 0.9895
2023-09-09 23:09:23,802 [INFO] - Validation epoch stats:   Loss: 4.9213 - Binary-Cell-Dice: 0.8019 - Binary-Cell-Jacard: 0.7289 - PQ-Score: 0.6253 - Tissue-MC-Acc.: 0.9025
2023-09-09 23:12:11,361 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 23:12:11,362 [INFO] - Epoch: 107/130
2023-09-09 23:16:16,941 [INFO] - Training epoch stats:     Loss: 4.5299 - Binary-Cell-Dice: 0.8397 - Binary-Cell-Jacard: 0.7729 - Tissue-MC-Acc.: 0.9906
2023-09-09 23:18:32,695 [INFO] - Validation epoch stats:   Loss: 4.9266 - Binary-Cell-Dice: 0.8025 - Binary-Cell-Jacard: 0.7292 - PQ-Score: 0.6259 - Tissue-MC-Acc.: 0.9037
