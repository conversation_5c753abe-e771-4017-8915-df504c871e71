2023-09-09 06:22:41,728 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-09 06:22:41,793 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f30f0792520>]
2023-09-09 06:22:41,793 [INFO] - Using GPU: cuda:0
2023-09-09 06:22:41,793 [INFO] - Using device: cuda:0
2023-09-09 06:22:41,794 [INFO] - Loss functions:
2023-09-09 06:22:41,795 [INFO] - {'nuclei_binary_map': {'focaltverskyloss': {'loss_fn': FocalTverskyLoss(), 'weight': 1}, 'dice': {'loss_fn': DiceLoss(), 'weight': 1}}, 'hv_map': {'mse': {'loss_fn': MSELossMaps(), 'weight': 2.5}, 'msge': {'loss_fn': MSGELossMaps(), 'weight': 8}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': <PERSON><PERSON><PERSON>oss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}, 'tissue_types': {'ce': {'loss_fn': CrossEntropyLoss(), 'weight': 0.1}}}
2023-09-09 06:22:50,821 [INFO] - Loaded CellViT-SAM model with backbone: SAM-B
2023-09-09 06:22:50,826 [INFO] -
Model: CellViTSAM(
  (encoder): ViTCellViTDeit(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 768, kernel_size=(16, 16), stride=(16, 16))
    )
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (1): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (2): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (3): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (4): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (5): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (6): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (7): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (8): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (9): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (10): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
      (11): Block(
        (norm1): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=768, out_features=2304, bias=True)
          (proj): Linear(in_features=768, out_features=768, bias=True)
        )
        (norm2): LayerNorm((768,), eps=1e-06, elementwise_affine=True)
        (mlp): MLPBlock(
          (lin1): Linear(in_features=768, out_features=3072, bias=True)
          (lin2): Linear(in_features=3072, out_features=768, bias=True)
          (act): GELU(approximate='none')
        )
      )
    )
    (neck): Sequential(
      (0): Conv2d(768, 256, kernel_size=(1, 1), stride=(1, 1), bias=False)
      (1): LayerNorm2d()
      (2): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1), bias=False)
      (3): LayerNorm2d()
    )
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (nuclei_binary_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (hv_map_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 2, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(768, 512, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(1024, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 512, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(512, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(512, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 6, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
)
2023-09-09 06:22:51,936 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViTSAM                                    [1, 6, 256, 256]          --
├─ViTCellViTDeit: 1-1                         [1, 256]                  3,145,728
│    └─PatchEmbed: 2-1                        [1, 16, 16, 768]          --
│    │    └─Conv2d: 3-1                       [1, 768, 16, 16]          (590,592)
│    └─ModuleList: 2-2                        --                        --
│    │    └─Block: 3-2                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-3                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-4                        [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-5                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-6                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-7                        [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-8                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-9                        [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-10                       [1, 16, 16, 768]          (7,104,128)
│    │    └─Block: 3-11                       [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-12                       [1, 16, 16, 768]          (7,091,328)
│    │    └─Block: 3-13                       [1, 16, 16, 768]          (7,104,128)
│    └─Sequential: 2-3                        [1, 256, 16, 16]          --
│    │    └─Conv2d: 3-14                      [1, 256, 16, 16]          (196,608)
│    │    └─LayerNorm2d: 3-15                 [1, 256, 16, 16]          (512)
│    │    └─Conv2d: 3-16                      [1, 256, 16, 16]          (589,824)
│    │    └─LayerNorm2d: 3-17                 [1, 256, 16, 16]          (512)
├─Linear: 1-2                                 [1, 19]                   4,883
├─Sequential: 1-11                            --                        (recursive)
│    └─ConvTranspose2d: 2-4                   [1, 512, 32, 32]          1,573,376
├─Sequential: 1-4                             [1, 512, 32, 32]          --
│    └─Deconv2DBlock: 2-5                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-18                  [1, 512, 32, 32]          3,934,208
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-6                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-19                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-20                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-21                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-22             [1, 256, 64, 64]          524,544
├─Sequential: 1-6                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-7                     [1, 512, 32, 32]          --
│    │    └─Sequential: 3-23                  [1, 512, 32, 32]          3,934,208
│    └─Deconv2DBlock: 2-8                     [1, 256, 64, 64]          --
│    │    └─Sequential: 3-24                  [1, 256, 64, 64]          1,115,136
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-9                        [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-25                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-26                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-27             [1, 128, 128, 128]        131,200
├─Sequential: 1-8                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-10                    [1, 512, 32, 32]          --
│    │    └─Sequential: 3-28                  [1, 512, 32, 32]          3,934,208
│    └─Deconv2DBlock: 2-11                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-29                  [1, 256, 64, 64]          1,115,136
│    └─Deconv2DBlock: 2-12                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-30                  [1, 128, 128, 128]        279,040
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-13                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-31                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-32                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-33             [1, 64, 256, 256]         32,832
├─Sequential: 1-10                            [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-14                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-34                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-15                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-35                  [1, 64, 256, 256]         18,624
├─Sequential: 1-11                            --                        (recursive)
│    └─Sequential: 2-16                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-36                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-37                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-38                      [1, 2, 256, 256]          130
├─Sequential: 1-20                            --                        (recursive)
│    └─ConvTranspose2d: 2-17                  [1, 512, 32, 32]          1,573,376
├─Sequential: 1-13                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-18                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-39                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-19                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-40                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-41                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-42                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-43             [1, 256, 64, 64]          524,544
├─Sequential: 1-15                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-44                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-21                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-22                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-46                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-47                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-48             [1, 128, 128, 128]        131,200
├─Sequential: 1-17                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-49                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-24                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-50                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-51                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-26                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-52                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-53                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-54             [1, 64, 256, 256]         32,832
├─Sequential: 1-19                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-27                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-55                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-28                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-56                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-20                            --                        (recursive)
│    └─Sequential: 2-29                       [1, 2, 256, 256]          --
│    │    └─Conv2DBlock: 3-57                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-58                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-59                      [1, 2, 256, 256]          130
├─Sequential: 1-29                            --                        (recursive)
│    └─ConvTranspose2d: 2-30                  [1, 512, 32, 32]          1,573,376
├─Sequential: 1-22                            [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-31                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-60                  [1, 512, 32, 32]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-32                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-61                 [1, 512, 32, 32]          4,720,128
│    │    └─Conv2DBlock: 3-62                 [1, 512, 32, 32]          2,360,832
│    │    └─Conv2DBlock: 3-63                 [1, 512, 32, 32]          2,360,832
│    │    └─ConvTranspose2d: 3-64             [1, 256, 64, 64]          524,544
├─Sequential: 1-24                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-65                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-34                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-35                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-67                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-68                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-69             [1, 128, 128, 128]        131,200
├─Sequential: 1-26                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 512, 32, 32]          (recursive)
│    │    └─Sequential: 3-70                  [1, 512, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-37                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-71                  [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-72                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-39                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-73                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-74                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-75             [1, 64, 256, 256]         32,832
├─Sequential: 1-28                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-40                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-76                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-41                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-77                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-29                            --                        (recursive)
│    └─Sequential: 2-42                       [1, 6, 256, 256]          --
│    │    └─Conv2DBlock: 3-78                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-79                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-80                      [1, 6, 256, 256]          390
===============================================================================================
Total params: 146,094,557
Trainable params: 56,423,645
Non-trainable params: 89,670,912
Total mult-adds (G): 200.15
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 2006.45
Params size (MB): 571.42
Estimated Total Size (MB): 2578.66
===============================================================================================
2023-09-09 06:22:52,789 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-09 06:22:52,789 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-09 06:22:52,789 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-09 06:22:56,312 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-09 06:22:56,314 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.5950, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314,
        0.7691, 0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9474,
        0.9667, 0.9823, 0.9883, 0.9902, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200,
        1.0215, 1.0260, 1.0278, 1.0367, 1.0449, 1.0464, 1.0536, 1.0910, 1.0913,
        1.0922, 1.0927, 1.1088, 1.1094, 1.1143, 1.1173, 1.1216, 1.1256, 1.1283,
        1.1287, 1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1777, 1.1792, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2527, 1.2576, 1.2580, 1.2595, 1.2606, 1.2621, 1.2651, 1.2663, 1.2666,
        1.2669, 1.2678, 1.2680, 1.2829, 1.2833, 1.2835, 1.2844, 1.2850, 1.2869,
        1.2884, 1.2899, 1.2902, 1.2915, 1.2929, 1.2972, 1.3012, 1.3055, 1.3141,
        1.3156, 1.3165, 1.3219, 1.3221, 1.3276, 1.3282, 1.3294, 1.3321, 1.3380,
        1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3885, 1.3891, 1.3940,
        1.3971, 1.4028, 1.4033, 1.4099, 1.4111, 1.4229, 1.4243, 1.4275, 1.4336,
        1.4377, 1.4407, 1.4419, 1.4432, 1.4436, 1.4455, 1.4585, 1.4591, 1.4625,
        1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897, 1.4975, 1.5089, 1.5392,
        1.5449, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5612, 1.5641, 1.5647,
        1.5696, 1.5702, 1.5746, 1.5939, 1.5953, 1.6031, 1.6174, 1.6871, 1.7110,
        1.7205, 1.7216, 1.7443, 1.7695, 1.8002, 1.8627, 1.8866])
2023-09-09 06:22:56,314 [INFO] - Instantiate Trainer
2023-09-09 06:22:56,315 [INFO] - Calling Trainer Fit
2023-09-09 06:22:56,315 [INFO] - Starting training, total number of epochs: 130
2023-09-09 06:22:56,315 [INFO] - Epoch: 1/130
2023-09-09 06:26:26,750 [INFO] - Training epoch stats:     Loss: 8.6312 - Binary-Cell-Dice: 0.6951 - Binary-Cell-Jacard: 0.5675 - Tissue-MC-Acc.: 0.1875
2023-09-09 06:31:21,717 [INFO] - Validation epoch stats:   Loss: 6.6370 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6600 - PQ-Score: 0.5281 - Tissue-MC-Acc.: 0.3114
2023-09-09 06:31:21,721 [INFO] - New best model - save checkpoint
2023-09-09 06:31:43,599 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-09 06:31:43,601 [INFO] - Epoch: 2/130
2023-09-09 06:35:59,781 [INFO] - Training epoch stats:     Loss: 6.3515 - Binary-Cell-Dice: 0.7440 - Binary-Cell-Jacard: 0.6339 - Tissue-MC-Acc.: 0.2239
2023-09-09 06:43:59,378 [INFO] - Validation epoch stats:   Loss: 5.9311 - Binary-Cell-Dice: 0.7785 - Binary-Cell-Jacard: 0.6681 - PQ-Score: 0.5603 - Tissue-MC-Acc.: 0.3114
2023-09-09 06:43:59,386 [INFO] - New best model - save checkpoint
2023-09-09 06:44:51,743 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-09 06:44:51,743 [INFO] - Epoch: 3/130
2023-09-09 06:49:52,440 [INFO] - Training epoch stats:     Loss: 5.9998 - Binary-Cell-Dice: 0.7655 - Binary-Cell-Jacard: 0.6541 - Tissue-MC-Acc.: 0.2132
2023-09-09 06:59:26,825 [INFO] - Validation epoch stats:   Loss: 5.7810 - Binary-Cell-Dice: 0.7781 - Binary-Cell-Jacard: 0.6777 - PQ-Score: 0.5627 - Tissue-MC-Acc.: 0.3114
2023-09-09 06:59:26,829 [INFO] - New best model - save checkpoint
2023-09-09 06:59:49,829 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-09 06:59:49,829 [INFO] - Epoch: 4/130
2023-09-09 07:02:40,549 [INFO] - Training epoch stats:     Loss: 5.8939 - Binary-Cell-Dice: 0.7667 - Binary-Cell-Jacard: 0.6621 - Tissue-MC-Acc.: 0.2259
2023-09-09 07:06:32,375 [INFO] - Validation epoch stats:   Loss: 5.6545 - Binary-Cell-Dice: 0.7879 - Binary-Cell-Jacard: 0.6915 - PQ-Score: 0.5778 - Tissue-MC-Acc.: 0.3204
2023-09-09 07:06:32,379 [INFO] - New best model - save checkpoint
2023-09-09 07:06:53,835 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-09 07:06:53,836 [INFO] - Epoch: 5/130
2023-09-09 07:10:10,282 [INFO] - Training epoch stats:     Loss: 5.8458 - Binary-Cell-Dice: 0.7732 - Binary-Cell-Jacard: 0.6717 - Tissue-MC-Acc.: 0.2192
2023-09-09 07:12:40,031 [INFO] - Validation epoch stats:   Loss: 5.5709 - Binary-Cell-Dice: 0.7846 - Binary-Cell-Jacard: 0.6848 - PQ-Score: 0.5797 - Tissue-MC-Acc.: 0.3234
2023-09-09 07:12:40,035 [INFO] - New best model - save checkpoint
2023-09-09 07:13:02,160 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-09 07:13:02,161 [INFO] - Epoch: 6/130
2023-09-09 07:16:11,523 [INFO] - Training epoch stats:     Loss: 5.7902 - Binary-Cell-Dice: 0.7731 - Binary-Cell-Jacard: 0.6724 - Tissue-MC-Acc.: 0.2449
2023-09-09 07:18:36,671 [INFO] - Validation epoch stats:   Loss: 5.5423 - Binary-Cell-Dice: 0.7921 - Binary-Cell-Jacard: 0.6980 - PQ-Score: 0.5874 - Tissue-MC-Acc.: 0.3204
2023-09-09 07:18:36,681 [INFO] - New best model - save checkpoint
2023-09-09 07:19:33,752 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-09 07:19:33,753 [INFO] - Epoch: 7/130
2023-09-09 07:22:20,648 [INFO] - Training epoch stats:     Loss: 5.7175 - Binary-Cell-Dice: 0.7757 - Binary-Cell-Jacard: 0.6780 - Tissue-MC-Acc.: 0.2354
2023-09-09 07:25:57,289 [INFO] - Validation epoch stats:   Loss: 5.4967 - Binary-Cell-Dice: 0.7942 - Binary-Cell-Jacard: 0.7014 - PQ-Score: 0.5847 - Tissue-MC-Acc.: 0.3227
2023-09-09 07:26:18,866 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-09 07:26:18,866 [INFO] - Epoch: 8/130
2023-09-09 07:28:58,938 [INFO] - Training epoch stats:     Loss: 5.6769 - Binary-Cell-Dice: 0.7753 - Binary-Cell-Jacard: 0.6787 - Tissue-MC-Acc.: 0.2434
2023-09-09 07:31:15,961 [INFO] - Validation epoch stats:   Loss: 5.4859 - Binary-Cell-Dice: 0.7940 - Binary-Cell-Jacard: 0.7004 - PQ-Score: 0.5971 - Tissue-MC-Acc.: 0.3234
2023-09-09 07:31:15,965 [INFO] - New best model - save checkpoint
2023-09-09 07:31:37,235 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-09 07:31:37,236 [INFO] - Epoch: 9/130
2023-09-09 07:34:43,725 [INFO] - Training epoch stats:     Loss: 5.6280 - Binary-Cell-Dice: 0.7748 - Binary-Cell-Jacard: 0.6826 - Tissue-MC-Acc.: 0.2323
2023-09-09 07:37:02,300 [INFO] - Validation epoch stats:   Loss: 5.4663 - Binary-Cell-Dice: 0.7953 - Binary-Cell-Jacard: 0.7051 - PQ-Score: 0.5963 - Tissue-MC-Acc.: 0.3291
2023-09-09 07:37:13,736 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-09 07:37:13,737 [INFO] - Epoch: 10/130
2023-09-09 07:39:40,639 [INFO] - Training epoch stats:     Loss: 5.6687 - Binary-Cell-Dice: 0.7791 - Binary-Cell-Jacard: 0.6842 - Tissue-MC-Acc.: 0.2449
2023-09-09 07:42:09,730 [INFO] - Validation epoch stats:   Loss: 5.4558 - Binary-Cell-Dice: 0.7941 - Binary-Cell-Jacard: 0.7028 - PQ-Score: 0.5981 - Tissue-MC-Acc.: 0.3242
2023-09-09 07:42:09,734 [INFO] - New best model - save checkpoint
2023-09-09 07:42:31,359 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-09 07:42:31,361 [INFO] - Epoch: 11/130
2023-09-09 07:45:03,971 [INFO] - Training epoch stats:     Loss: 5.6382 - Binary-Cell-Dice: 0.7797 - Binary-Cell-Jacard: 0.6830 - Tissue-MC-Acc.: 0.2398
2023-09-09 07:47:42,818 [INFO] - Validation epoch stats:   Loss: 5.4488 - Binary-Cell-Dice: 0.7954 - Binary-Cell-Jacard: 0.7063 - PQ-Score: 0.5975 - Tissue-MC-Acc.: 0.3321
2023-09-09 07:47:53,523 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-09 07:47:53,524 [INFO] - Epoch: 12/130
2023-09-09 07:50:51,652 [INFO] - Training epoch stats:     Loss: 5.6371 - Binary-Cell-Dice: 0.7851 - Binary-Cell-Jacard: 0.6881 - Tissue-MC-Acc.: 0.2358
2023-09-09 07:53:05,709 [INFO] - Validation epoch stats:   Loss: 5.3785 - Binary-Cell-Dice: 0.7973 - Binary-Cell-Jacard: 0.7081 - PQ-Score: 0.6027 - Tissue-MC-Acc.: 0.3340
2023-09-09 07:53:05,713 [INFO] - New best model - save checkpoint
2023-09-09 07:53:27,163 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-09 07:53:27,164 [INFO] - Epoch: 13/130
2023-09-09 07:57:17,994 [INFO] - Training epoch stats:     Loss: 5.5482 - Binary-Cell-Dice: 0.7877 - Binary-Cell-Jacard: 0.6917 - Tissue-MC-Acc.: 0.2485
2023-09-09 07:59:28,362 [INFO] - Validation epoch stats:   Loss: 5.3978 - Binary-Cell-Dice: 0.7978 - Binary-Cell-Jacard: 0.7064 - PQ-Score: 0.6029 - Tissue-MC-Acc.: 0.3321
2023-09-09 07:59:28,370 [INFO] - New best model - save checkpoint
2023-09-09 08:00:16,869 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-09 08:00:16,871 [INFO] - Epoch: 14/130
2023-09-09 08:02:54,356 [INFO] - Training epoch stats:     Loss: 5.5469 - Binary-Cell-Dice: 0.7850 - Binary-Cell-Jacard: 0.6927 - Tissue-MC-Acc.: 0.2497
2023-09-09 08:05:34,715 [INFO] - Validation epoch stats:   Loss: 5.4111 - Binary-Cell-Dice: 0.8013 - Binary-Cell-Jacard: 0.7121 - PQ-Score: 0.6050 - Tissue-MC-Acc.: 0.3321
2023-09-09 08:05:34,719 [INFO] - New best model - save checkpoint
2023-09-09 08:06:00,524 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-09 08:06:00,525 [INFO] - Epoch: 15/130
2023-09-09 08:09:16,228 [INFO] - Training epoch stats:     Loss: 5.4841 - Binary-Cell-Dice: 0.7860 - Binary-Cell-Jacard: 0.6939 - Tissue-MC-Acc.: 0.2497
2023-09-09 08:11:48,443 [INFO] - Validation epoch stats:   Loss: 5.3444 - Binary-Cell-Dice: 0.8004 - Binary-Cell-Jacard: 0.7142 - PQ-Score: 0.6084 - Tissue-MC-Acc.: 0.3302
2023-09-09 08:11:48,447 [INFO] - New best model - save checkpoint
2023-09-09 08:12:09,580 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-09 08:12:09,581 [INFO] - Epoch: 16/130
2023-09-09 08:15:55,991 [INFO] - Training epoch stats:     Loss: 5.4985 - Binary-Cell-Dice: 0.7848 - Binary-Cell-Jacard: 0.6951 - Tissue-MC-Acc.: 0.2418
2023-09-09 08:19:23,107 [INFO] - Validation epoch stats:   Loss: 5.3671 - Binary-Cell-Dice: 0.7980 - Binary-Cell-Jacard: 0.7122 - PQ-Score: 0.6039 - Tissue-MC-Acc.: 0.3325
2023-09-09 08:19:36,783 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-09 08:19:36,783 [INFO] - Epoch: 17/130
2023-09-09 08:24:13,010 [INFO] - Training epoch stats:     Loss: 5.4824 - Binary-Cell-Dice: 0.7875 - Binary-Cell-Jacard: 0.6985 - Tissue-MC-Acc.: 0.2311
2023-09-09 08:26:28,540 [INFO] - Validation epoch stats:   Loss: 5.3569 - Binary-Cell-Dice: 0.8005 - Binary-Cell-Jacard: 0.7157 - PQ-Score: 0.6048 - Tissue-MC-Acc.: 0.3317
2023-09-09 08:26:42,523 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-09 08:26:42,524 [INFO] - Epoch: 18/130
2023-09-09 08:29:32,812 [INFO] - Training epoch stats:     Loss: 5.4805 - Binary-Cell-Dice: 0.7932 - Binary-Cell-Jacard: 0.6978 - Tissue-MC-Acc.: 0.2521
2023-09-09 08:32:04,420 [INFO] - Validation epoch stats:   Loss: 5.3355 - Binary-Cell-Dice: 0.8005 - Binary-Cell-Jacard: 0.7124 - PQ-Score: 0.6089 - Tissue-MC-Acc.: 0.3325
2023-09-09 08:32:04,432 [INFO] - New best model - save checkpoint
2023-09-09 08:32:51,057 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-09 08:32:51,058 [INFO] - Epoch: 19/130
2023-09-09 08:35:28,591 [INFO] - Training epoch stats:     Loss: 5.4567 - Binary-Cell-Dice: 0.7927 - Binary-Cell-Jacard: 0.6977 - Tissue-MC-Acc.: 0.2481
2023-09-09 08:38:23,182 [INFO] - Validation epoch stats:   Loss: 5.3315 - Binary-Cell-Dice: 0.7990 - Binary-Cell-Jacard: 0.7111 - PQ-Score: 0.6084 - Tissue-MC-Acc.: 0.3298
2023-09-09 08:38:41,870 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-09 08:38:41,871 [INFO] - Epoch: 20/130
2023-09-09 08:41:50,424 [INFO] - Training epoch stats:     Loss: 5.5036 - Binary-Cell-Dice: 0.7855 - Binary-Cell-Jacard: 0.6966 - Tissue-MC-Acc.: 0.2184
2023-09-09 08:45:40,480 [INFO] - Validation epoch stats:   Loss: 5.3177 - Binary-Cell-Dice: 0.8011 - Binary-Cell-Jacard: 0.7130 - PQ-Score: 0.6086 - Tissue-MC-Acc.: 0.3355
2023-09-09 08:45:51,785 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-09 08:45:51,786 [INFO] - Epoch: 21/130
2023-09-09 08:51:14,394 [INFO] - Training epoch stats:     Loss: 5.4364 - Binary-Cell-Dice: 0.7889 - Binary-Cell-Jacard: 0.6956 - Tissue-MC-Acc.: 0.2477
2023-09-09 08:53:27,834 [INFO] - Validation epoch stats:   Loss: 5.2918 - Binary-Cell-Dice: 0.8022 - Binary-Cell-Jacard: 0.7170 - PQ-Score: 0.6083 - Tissue-MC-Acc.: 0.3396
2023-09-09 08:53:39,502 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-09 08:53:39,503 [INFO] - Epoch: 22/130
2023-09-09 08:57:01,849 [INFO] - Training epoch stats:     Loss: 5.4372 - Binary-Cell-Dice: 0.7946 - Binary-Cell-Jacard: 0.7047 - Tissue-MC-Acc.: 0.2410
2023-09-09 08:59:24,616 [INFO] - Validation epoch stats:   Loss: 5.3044 - Binary-Cell-Dice: 0.8026 - Binary-Cell-Jacard: 0.7143 - PQ-Score: 0.6112 - Tissue-MC-Acc.: 0.3400
2023-09-09 08:59:24,626 [INFO] - New best model - save checkpoint
2023-09-09 09:00:09,608 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-09 09:00:09,609 [INFO] - Epoch: 23/130
2023-09-09 09:03:34,094 [INFO] - Training epoch stats:     Loss: 5.3747 - Binary-Cell-Dice: 0.7947 - Binary-Cell-Jacard: 0.7061 - Tissue-MC-Acc.: 0.2509
2023-09-09 09:06:11,081 [INFO] - Validation epoch stats:   Loss: 5.2843 - Binary-Cell-Dice: 0.8012 - Binary-Cell-Jacard: 0.7148 - PQ-Score: 0.6077 - Tissue-MC-Acc.: 0.3373
2023-09-09 09:06:21,400 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-09 09:06:21,401 [INFO] - Epoch: 24/130
2023-09-09 09:10:40,621 [INFO] - Training epoch stats:     Loss: 5.3886 - Binary-Cell-Dice: 0.7945 - Binary-Cell-Jacard: 0.7064 - Tissue-MC-Acc.: 0.2331
2023-09-09 09:13:02,403 [INFO] - Validation epoch stats:   Loss: 5.3119 - Binary-Cell-Dice: 0.8008 - Binary-Cell-Jacard: 0.7149 - PQ-Score: 0.6105 - Tissue-MC-Acc.: 0.3404
2023-09-09 09:13:13,704 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-09 09:13:13,705 [INFO] - Epoch: 25/130
2023-09-09 09:16:06,335 [INFO] - Training epoch stats:     Loss: 5.3572 - Binary-Cell-Dice: 0.7951 - Binary-Cell-Jacard: 0.7067 - Tissue-MC-Acc.: 0.2525
2023-09-09 09:18:16,775 [INFO] - Validation epoch stats:   Loss: 5.2660 - Binary-Cell-Dice: 0.8004 - Binary-Cell-Jacard: 0.7165 - PQ-Score: 0.6097 - Tissue-MC-Acc.: 0.3358
2023-09-09 09:18:38,505 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-09 09:18:38,506 [INFO] - Epoch: 26/130
2023-09-09 09:23:23,113 [INFO] - Training epoch stats:     Loss: 5.4978 - Binary-Cell-Dice: 0.7827 - Binary-Cell-Jacard: 0.6894 - Tissue-MC-Acc.: 0.3119
2023-09-09 09:26:18,681 [INFO] - Validation epoch stats:   Loss: 5.3007 - Binary-Cell-Dice: 0.7962 - Binary-Cell-Jacard: 0.7087 - PQ-Score: 0.6051 - Tissue-MC-Acc.: 0.4729
2023-09-09 09:26:54,056 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-09 09:26:54,057 [INFO] - Epoch: 27/130
2023-09-09 09:31:32,779 [INFO] - Training epoch stats:     Loss: 5.4331 - Binary-Cell-Dice: 0.7846 - Binary-Cell-Jacard: 0.6952 - Tissue-MC-Acc.: 0.4197
2023-09-09 09:34:09,035 [INFO] - Validation epoch stats:   Loss: 5.3048 - Binary-Cell-Dice: 0.8008 - Binary-Cell-Jacard: 0.7127 - PQ-Score: 0.6103 - Tissue-MC-Acc.: 0.5008
2023-09-09 09:34:47,440 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-09 09:34:47,441 [INFO] - Epoch: 28/130
2023-09-09 09:39:17,371 [INFO] - Training epoch stats:     Loss: 5.3439 - Binary-Cell-Dice: 0.8007 - Binary-Cell-Jacard: 0.7074 - Tissue-MC-Acc.: 0.4534
2023-09-09 09:42:24,083 [INFO] - Validation epoch stats:   Loss: 5.2469 - Binary-Cell-Dice: 0.8024 - Binary-Cell-Jacard: 0.7172 - PQ-Score: 0.6086 - Tissue-MC-Acc.: 0.5482
2023-09-09 09:42:41,595 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-09 09:42:41,596 [INFO] - Epoch: 29/130
2023-09-09 09:46:49,564 [INFO] - Training epoch stats:     Loss: 5.3024 - Binary-Cell-Dice: 0.7937 - Binary-Cell-Jacard: 0.7048 - Tissue-MC-Acc.: 0.4978
2023-09-09 09:49:16,547 [INFO] - Validation epoch stats:   Loss: 5.2256 - Binary-Cell-Dice: 0.7983 - Binary-Cell-Jacard: 0.7152 - PQ-Score: 0.6073 - Tissue-MC-Acc.: 0.5572
2023-09-09 09:49:56,643 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-09 09:49:56,644 [INFO] - Epoch: 30/130
2023-09-09 09:56:49,770 [INFO] - Training epoch stats:     Loss: 5.3169 - Binary-Cell-Dice: 0.7901 - Binary-Cell-Jacard: 0.7020 - Tissue-MC-Acc.: 0.5256
2023-09-09 10:01:17,183 [INFO] - Validation epoch stats:   Loss: 5.1734 - Binary-Cell-Dice: 0.8057 - Binary-Cell-Jacard: 0.7254 - PQ-Score: 0.6191 - Tissue-MC-Acc.: 0.5889
2023-09-09 10:01:17,192 [INFO] - New best model - save checkpoint
2023-09-09 10:02:47,866 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-09 10:02:47,866 [INFO] - Epoch: 31/130
2023-09-09 10:05:38,044 [INFO] - Training epoch stats:     Loss: 5.2310 - Binary-Cell-Dice: 0.8044 - Binary-Cell-Jacard: 0.7133 - Tissue-MC-Acc.: 0.5466
2023-09-09 10:08:09,974 [INFO] - Validation epoch stats:   Loss: 5.1906 - Binary-Cell-Dice: 0.8029 - Binary-Cell-Jacard: 0.7204 - PQ-Score: 0.6135 - Tissue-MC-Acc.: 0.6028
2023-09-09 10:08:27,518 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-09 10:08:27,519 [INFO] - Epoch: 32/130
2023-09-09 10:12:12,261 [INFO] - Training epoch stats:     Loss: 5.2322 - Binary-Cell-Dice: 0.7938 - Binary-Cell-Jacard: 0.7090 - Tissue-MC-Acc.: 0.5628
2023-09-09 10:14:46,784 [INFO] - Validation epoch stats:   Loss: 5.1836 - Binary-Cell-Dice: 0.8022 - Binary-Cell-Jacard: 0.7196 - PQ-Score: 0.6062 - Tissue-MC-Acc.: 0.5915
2023-09-09 10:15:19,913 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-09 10:15:19,914 [INFO] - Epoch: 33/130
2023-09-09 10:19:29,797 [INFO] - Training epoch stats:     Loss: 5.1565 - Binary-Cell-Dice: 0.8013 - Binary-Cell-Jacard: 0.7188 - Tissue-MC-Acc.: 0.5818
2023-09-09 10:21:44,196 [INFO] - Validation epoch stats:   Loss: 5.0945 - Binary-Cell-Dice: 0.8070 - Binary-Cell-Jacard: 0.7214 - PQ-Score: 0.6203 - Tissue-MC-Acc.: 0.6318
2023-09-09 10:21:44,204 [INFO] - New best model - save checkpoint
2023-09-09 10:23:00,822 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-09 10:23:00,823 [INFO] - Epoch: 34/130
2023-09-09 10:26:03,885 [INFO] - Training epoch stats:     Loss: 5.1455 - Binary-Cell-Dice: 0.8038 - Binary-Cell-Jacard: 0.7163 - Tissue-MC-Acc.: 0.6104
2023-09-09 10:28:13,800 [INFO] - Validation epoch stats:   Loss: 5.1146 - Binary-Cell-Dice: 0.8039 - Binary-Cell-Jacard: 0.7234 - PQ-Score: 0.6157 - Tissue-MC-Acc.: 0.6472
2023-09-09 10:28:55,963 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-09 10:28:55,964 [INFO] - Epoch: 35/130
2023-09-09 10:32:55,114 [INFO] - Training epoch stats:     Loss: 5.0795 - Binary-Cell-Dice: 0.8072 - Binary-Cell-Jacard: 0.7217 - Tissue-MC-Acc.: 0.6425
2023-09-09 10:35:28,389 [INFO] - Validation epoch stats:   Loss: 5.0617 - Binary-Cell-Dice: 0.8079 - Binary-Cell-Jacard: 0.7253 - PQ-Score: 0.6221 - Tissue-MC-Acc.: 0.6559
2023-09-09 10:35:28,393 [INFO] - New best model - save checkpoint
2023-09-09 10:36:03,277 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-09 10:36:03,278 [INFO] - Epoch: 36/130
2023-09-09 10:38:49,714 [INFO] - Training epoch stats:     Loss: 5.0807 - Binary-Cell-Dice: 0.8096 - Binary-Cell-Jacard: 0.7229 - Tissue-MC-Acc.: 0.6631
2023-09-09 10:41:15,873 [INFO] - Validation epoch stats:   Loss: 5.0496 - Binary-Cell-Dice: 0.8091 - Binary-Cell-Jacard: 0.7312 - PQ-Score: 0.6253 - Tissue-MC-Acc.: 0.6706
2023-09-09 10:41:15,881 [INFO] - New best model - save checkpoint
2023-09-09 10:42:29,053 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-09 10:42:29,054 [INFO] - Epoch: 37/130
2023-09-09 10:45:30,400 [INFO] - Training epoch stats:     Loss: 5.0898 - Binary-Cell-Dice: 0.7998 - Binary-Cell-Jacard: 0.7189 - Tissue-MC-Acc.: 0.6901
2023-09-09 10:47:44,064 [INFO] - Validation epoch stats:   Loss: 5.0788 - Binary-Cell-Dice: 0.8081 - Binary-Cell-Jacard: 0.7263 - PQ-Score: 0.6254 - Tissue-MC-Acc.: 0.6792
2023-09-09 10:47:44,138 [INFO] - New best model - save checkpoint
2023-09-09 10:48:59,737 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-09 10:48:59,738 [INFO] - Epoch: 38/130
2023-09-09 10:54:17,590 [INFO] - Training epoch stats:     Loss: 5.0182 - Binary-Cell-Dice: 0.8064 - Binary-Cell-Jacard: 0.7252 - Tissue-MC-Acc.: 0.7000
2023-09-09 10:57:12,984 [INFO] - Validation epoch stats:   Loss: 5.0503 - Binary-Cell-Dice: 0.8066 - Binary-Cell-Jacard: 0.7270 - PQ-Score: 0.6212 - Tissue-MC-Acc.: 0.6965
2023-09-09 10:57:48,030 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-09 10:57:48,031 [INFO] - Epoch: 39/130
2023-09-09 11:01:18,279 [INFO] - Training epoch stats:     Loss: 5.0456 - Binary-Cell-Dice: 0.8051 - Binary-Cell-Jacard: 0.7224 - Tissue-MC-Acc.: 0.7182
2023-09-09 11:03:52,527 [INFO] - Validation epoch stats:   Loss: 5.0829 - Binary-Cell-Dice: 0.8069 - Binary-Cell-Jacard: 0.7240 - PQ-Score: 0.6213 - Tissue-MC-Acc.: 0.6995
2023-09-09 11:04:35,167 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-09 11:04:35,168 [INFO] - Epoch: 40/130
2023-09-09 11:09:14,898 [INFO] - Training epoch stats:     Loss: 4.9971 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7279 - Tissue-MC-Acc.: 0.7444
2023-09-09 11:11:55,286 [INFO] - Validation epoch stats:   Loss: 5.0446 - Binary-Cell-Dice: 0.8074 - Binary-Cell-Jacard: 0.7312 - PQ-Score: 0.6256 - Tissue-MC-Acc.: 0.7048
2023-09-09 11:11:55,390 [INFO] - New best model - save checkpoint
2023-09-09 11:13:27,879 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-09 11:13:27,880 [INFO] - Epoch: 41/130
2023-09-09 11:17:32,011 [INFO] - Training epoch stats:     Loss: 4.9645 - Binary-Cell-Dice: 0.8061 - Binary-Cell-Jacard: 0.7300 - Tissue-MC-Acc.: 0.7523
2023-09-09 11:19:51,396 [INFO] - Validation epoch stats:   Loss: 4.9998 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7339 - PQ-Score: 0.6282 - Tissue-MC-Acc.: 0.7282
2023-09-09 11:19:51,400 [INFO] - New best model - save checkpoint
2023-09-09 11:20:26,549 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-09 11:20:26,550 [INFO] - Epoch: 42/130
2023-09-09 11:23:11,581 [INFO] - Training epoch stats:     Loss: 5.0163 - Binary-Cell-Dice: 0.8041 - Binary-Cell-Jacard: 0.7256 - Tissue-MC-Acc.: 0.7780
2023-09-09 11:25:35,524 [INFO] - Validation epoch stats:   Loss: 5.0029 - Binary-Cell-Dice: 0.8105 - Binary-Cell-Jacard: 0.7294 - PQ-Score: 0.6277 - Tissue-MC-Acc.: 0.7300
2023-09-09 11:26:04,953 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-09 11:26:04,953 [INFO] - Epoch: 43/130
2023-09-09 11:29:20,754 [INFO] - Training epoch stats:     Loss: 4.9494 - Binary-Cell-Dice: 0.8177 - Binary-Cell-Jacard: 0.7351 - Tissue-MC-Acc.: 0.7963
2023-09-09 11:31:43,074 [INFO] - Validation epoch stats:   Loss: 5.0061 - Binary-Cell-Dice: 0.8095 - Binary-Cell-Jacard: 0.7316 - PQ-Score: 0.6280 - Tissue-MC-Acc.: 0.7519
2023-09-09 11:32:01,009 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-09 11:32:01,010 [INFO] - Epoch: 44/130
2023-09-09 11:35:17,465 [INFO] - Training epoch stats:     Loss: 4.9219 - Binary-Cell-Dice: 0.8128 - Binary-Cell-Jacard: 0.7313 - Tissue-MC-Acc.: 0.8137
2023-09-09 11:37:49,176 [INFO] - Validation epoch stats:   Loss: 4.9901 - Binary-Cell-Dice: 0.8097 - Binary-Cell-Jacard: 0.7305 - PQ-Score: 0.6296 - Tissue-MC-Acc.: 0.7632
2023-09-09 11:37:49,188 [INFO] - New best model - save checkpoint
2023-09-09 11:39:14,375 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-09 11:39:14,376 [INFO] - Epoch: 45/130
2023-09-09 11:42:25,718 [INFO] - Training epoch stats:     Loss: 4.9101 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7357 - Tissue-MC-Acc.: 0.8260
2023-09-09 11:44:54,237 [INFO] - Validation epoch stats:   Loss: 5.0039 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7309 - PQ-Score: 0.6268 - Tissue-MC-Acc.: 0.7636
2023-09-09 11:45:11,515 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-09 11:45:11,516 [INFO] - Epoch: 46/130
2023-09-09 11:48:11,121 [INFO] - Training epoch stats:     Loss: 4.9050 - Binary-Cell-Dice: 0.8152 - Binary-Cell-Jacard: 0.7368 - Tissue-MC-Acc.: 0.8371
2023-09-09 11:50:45,690 [INFO] - Validation epoch stats:   Loss: 4.9858 - Binary-Cell-Dice: 0.8089 - Binary-Cell-Jacard: 0.7247 - PQ-Score: 0.6284 - Tissue-MC-Acc.: 0.7771
2023-09-09 11:51:04,124 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-09 11:51:04,125 [INFO] - Epoch: 47/130
2023-09-09 11:53:36,800 [INFO] - Training epoch stats:     Loss: 4.8844 - Binary-Cell-Dice: 0.8169 - Binary-Cell-Jacard: 0.7344 - Tissue-MC-Acc.: 0.8530
2023-09-09 11:55:50,884 [INFO] - Validation epoch stats:   Loss: 4.9996 - Binary-Cell-Dice: 0.8093 - Binary-Cell-Jacard: 0.7291 - PQ-Score: 0.6288 - Tissue-MC-Acc.: 0.7933
2023-09-09 11:56:07,412 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-09 11:56:07,413 [INFO] - Epoch: 48/130
2023-09-09 11:59:30,845 [INFO] - Training epoch stats:     Loss: 4.8608 - Binary-Cell-Dice: 0.8135 - Binary-Cell-Jacard: 0.7370 - Tissue-MC-Acc.: 0.8692
2023-09-09 12:01:54,132 [INFO] - Validation epoch stats:   Loss: 4.9745 - Binary-Cell-Dice: 0.8083 - Binary-Cell-Jacard: 0.7291 - PQ-Score: 0.6255 - Tissue-MC-Acc.: 0.7941
2023-09-09 12:02:10,414 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-09 12:02:10,415 [INFO] - Epoch: 49/130
2023-09-09 12:04:58,610 [INFO] - Training epoch stats:     Loss: 4.8325 - Binary-Cell-Dice: 0.8179 - Binary-Cell-Jacard: 0.7407 - Tissue-MC-Acc.: 0.8783
2023-09-09 12:07:12,543 [INFO] - Validation epoch stats:   Loss: 4.9718 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7340 - PQ-Score: 0.6318 - Tissue-MC-Acc.: 0.8038
2023-09-09 12:07:12,550 [INFO] - New best model - save checkpoint
2023-09-09 12:08:49,101 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-09 12:08:49,104 [INFO] - Epoch: 50/130
2023-09-09 12:11:39,659 [INFO] - Training epoch stats:     Loss: 4.8477 - Binary-Cell-Dice: 0.8184 - Binary-Cell-Jacard: 0.7392 - Tissue-MC-Acc.: 0.8787
2023-09-09 12:13:55,545 [INFO] - Validation epoch stats:   Loss: 4.9505 - Binary-Cell-Dice: 0.8108 - Binary-Cell-Jacard: 0.7342 - PQ-Score: 0.6321 - Tissue-MC-Acc.: 0.8031
2023-09-09 12:13:55,547 [INFO] - New best model - save checkpoint
2023-09-09 12:14:30,036 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-09 12:14:30,036 [INFO] - Epoch: 51/130
2023-09-09 12:17:32,266 [INFO] - Training epoch stats:     Loss: 4.7996 - Binary-Cell-Dice: 0.8172 - Binary-Cell-Jacard: 0.7416 - Tissue-MC-Acc.: 0.8977
2023-09-09 12:19:50,951 [INFO] - Validation epoch stats:   Loss: 4.9644 - Binary-Cell-Dice: 0.8100 - Binary-Cell-Jacard: 0.7324 - PQ-Score: 0.6314 - Tissue-MC-Acc.: 0.8272
2023-09-09 12:20:31,436 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-09 12:20:31,437 [INFO] - Epoch: 52/130
2023-09-09 12:23:26,754 [INFO] - Training epoch stats:     Loss: 4.7987 - Binary-Cell-Dice: 0.8150 - Binary-Cell-Jacard: 0.7444 - Tissue-MC-Acc.: 0.9080
2023-09-09 12:25:47,124 [INFO] - Validation epoch stats:   Loss: 4.9543 - Binary-Cell-Dice: 0.8122 - Binary-Cell-Jacard: 0.7365 - PQ-Score: 0.6333 - Tissue-MC-Acc.: 0.8298
2023-09-09 12:25:47,128 [INFO] - New best model - save checkpoint
2023-09-09 12:26:22,478 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-09 12:26:22,478 [INFO] - Epoch: 53/130
2023-09-09 12:31:25,180 [INFO] - Training epoch stats:     Loss: 4.7527 - Binary-Cell-Dice: 0.8201 - Binary-Cell-Jacard: 0.7431 - Tissue-MC-Acc.: 0.9243
2023-09-09 12:34:25,189 [INFO] - Validation epoch stats:   Loss: 4.9482 - Binary-Cell-Dice: 0.8118 - Binary-Cell-Jacard: 0.7351 - PQ-Score: 0.6326 - Tissue-MC-Acc.: 0.8215
2023-09-09 12:34:42,670 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-09 12:34:42,671 [INFO] - Epoch: 54/130
2023-09-09 12:38:30,374 [INFO] - Training epoch stats:     Loss: 4.7816 - Binary-Cell-Dice: 0.8141 - Binary-Cell-Jacard: 0.7425 - Tissue-MC-Acc.: 0.9310
2023-09-09 12:41:01,363 [INFO] - Validation epoch stats:   Loss: 4.9781 - Binary-Cell-Dice: 0.8096 - Binary-Cell-Jacard: 0.7333 - PQ-Score: 0.6297 - Tissue-MC-Acc.: 0.8362
2023-09-09 12:41:19,517 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-09 12:41:19,517 [INFO] - Epoch: 55/130
2023-09-09 12:46:59,030 [INFO] - Training epoch stats:     Loss: 4.7534 - Binary-Cell-Dice: 0.8180 - Binary-Cell-Jacard: 0.7411 - Tissue-MC-Acc.: 0.9358
2023-09-09 12:49:18,496 [INFO] - Validation epoch stats:   Loss: 4.9505 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7342 - PQ-Score: 0.6330 - Tissue-MC-Acc.: 0.8377
2023-09-09 12:49:58,752 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-09 12:49:58,753 [INFO] - Epoch: 56/130
2023-09-09 12:54:10,496 [INFO] - Training epoch stats:     Loss: 4.7861 - Binary-Cell-Dice: 0.8202 - Binary-Cell-Jacard: 0.7453 - Tissue-MC-Acc.: 0.9338
2023-09-09 12:56:54,039 [INFO] - Validation epoch stats:   Loss: 4.9553 - Binary-Cell-Dice: 0.8105 - Binary-Cell-Jacard: 0.7337 - PQ-Score: 0.6321 - Tissue-MC-Acc.: 0.8415
2023-09-09 12:57:38,508 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-09 12:57:38,509 [INFO] - Epoch: 57/130
2023-09-09 13:02:14,249 [INFO] - Training epoch stats:     Loss: 4.7732 - Binary-Cell-Dice: 0.8161 - Binary-Cell-Jacard: 0.7449 - Tissue-MC-Acc.: 0.9334
2023-09-09 13:04:31,812 [INFO] - Validation epoch stats:   Loss: 4.9392 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7330 - PQ-Score: 0.6309 - Tissue-MC-Acc.: 0.8475
2023-09-09 13:05:07,417 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-09 13:05:07,418 [INFO] - Epoch: 58/130
2023-09-09 13:08:07,852 [INFO] - Training epoch stats:     Loss: 4.7495 - Binary-Cell-Dice: 0.8252 - Binary-Cell-Jacard: 0.7448 - Tissue-MC-Acc.: 0.9350
2023-09-09 13:10:31,786 [INFO] - Validation epoch stats:   Loss: 4.9292 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7351 - PQ-Score: 0.6310 - Tissue-MC-Acc.: 0.8622
2023-09-09 13:10:50,858 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-09 13:10:50,858 [INFO] - Epoch: 59/130
2023-09-09 13:13:32,794 [INFO] - Training epoch stats:     Loss: 4.7099 - Binary-Cell-Dice: 0.8195 - Binary-Cell-Jacard: 0.7472 - Tissue-MC-Acc.: 0.9429
2023-09-09 13:15:50,942 [INFO] - Validation epoch stats:   Loss: 4.9245 - Binary-Cell-Dice: 0.8103 - Binary-Cell-Jacard: 0.7339 - PQ-Score: 0.6320 - Tissue-MC-Acc.: 0.8577
2023-09-09 13:16:23,523 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-09 13:16:23,523 [INFO] - Epoch: 60/130
2023-09-09 13:19:37,169 [INFO] - Training epoch stats:     Loss: 4.7615 - Binary-Cell-Dice: 0.8252 - Binary-Cell-Jacard: 0.7506 - Tissue-MC-Acc.: 0.9552
2023-09-09 13:22:02,687 [INFO] - Validation epoch stats:   Loss: 4.9404 - Binary-Cell-Dice: 0.8114 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6307 - Tissue-MC-Acc.: 0.8727
2023-09-09 13:22:33,161 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-09 13:22:33,162 [INFO] - Epoch: 61/130
2023-09-09 13:25:46,251 [INFO] - Training epoch stats:     Loss: 4.7013 - Binary-Cell-Dice: 0.8263 - Binary-Cell-Jacard: 0.7533 - Tissue-MC-Acc.: 0.9616
2023-09-09 13:28:07,493 [INFO] - Validation epoch stats:   Loss: 4.9274 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6312 - Tissue-MC-Acc.: 0.8660
2023-09-09 13:28:52,562 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-09 13:28:52,563 [INFO] - Epoch: 62/130
2023-09-09 13:31:56,343 [INFO] - Training epoch stats:     Loss: 4.7085 - Binary-Cell-Dice: 0.8219 - Binary-Cell-Jacard: 0.7469 - Tissue-MC-Acc.: 0.9631
2023-09-09 13:34:19,734 [INFO] - Validation epoch stats:   Loss: 4.9237 - Binary-Cell-Dice: 0.8107 - Binary-Cell-Jacard: 0.7359 - PQ-Score: 0.6344 - Tissue-MC-Acc.: 0.8709
2023-09-09 13:34:19,739 [INFO] - New best model - save checkpoint
2023-09-09 13:35:12,025 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-09 13:35:12,026 [INFO] - Epoch: 63/130
2023-09-09 13:39:03,494 [INFO] - Training epoch stats:     Loss: 4.7165 - Binary-Cell-Dice: 0.8248 - Binary-Cell-Jacard: 0.7517 - Tissue-MC-Acc.: 0.9608
2023-09-09 13:41:25,454 [INFO] - Validation epoch stats:   Loss: 4.9271 - Binary-Cell-Dice: 0.8107 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6325 - Tissue-MC-Acc.: 0.8682
2023-09-09 13:42:10,116 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-09 13:42:10,118 [INFO] - Epoch: 64/130
2023-09-09 13:45:45,593 [INFO] - Training epoch stats:     Loss: 4.7098 - Binary-Cell-Dice: 0.8170 - Binary-Cell-Jacard: 0.7525 - Tissue-MC-Acc.: 0.9616
2023-09-09 13:48:01,554 [INFO] - Validation epoch stats:   Loss: 4.9223 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7369 - PQ-Score: 0.6340 - Tissue-MC-Acc.: 0.8791
2023-09-09 13:48:38,179 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-09 13:48:38,180 [INFO] - Epoch: 65/130
2023-09-09 13:52:06,816 [INFO] - Training epoch stats:     Loss: 4.7105 - Binary-Cell-Dice: 0.8277 - Binary-Cell-Jacard: 0.7521 - Tissue-MC-Acc.: 0.9643
2023-09-09 13:54:46,352 [INFO] - Validation epoch stats:   Loss: 4.9385 - Binary-Cell-Dice: 0.8110 - Binary-Cell-Jacard: 0.7366 - PQ-Score: 0.6325 - Tissue-MC-Acc.: 0.8776
2023-09-09 13:55:09,172 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-09 13:55:09,173 [INFO] - Epoch: 66/130
2023-09-09 13:58:11,502 [INFO] - Training epoch stats:     Loss: 4.6414 - Binary-Cell-Dice: 0.8290 - Binary-Cell-Jacard: 0.7572 - Tissue-MC-Acc.: 0.9667
2023-09-09 14:00:30,142 [INFO] - Validation epoch stats:   Loss: 4.9359 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7353 - PQ-Score: 0.6328 - Tissue-MC-Acc.: 0.8761
2023-09-09 14:01:07,835 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-09 14:01:07,836 [INFO] - Epoch: 67/130
2023-09-09 14:04:53,978 [INFO] - Training epoch stats:     Loss: 4.6936 - Binary-Cell-Dice: 0.8253 - Binary-Cell-Jacard: 0.7503 - Tissue-MC-Acc.: 0.9719
2023-09-09 14:07:39,267 [INFO] - Validation epoch stats:   Loss: 4.9147 - Binary-Cell-Dice: 0.8110 - Binary-Cell-Jacard: 0.7357 - PQ-Score: 0.6328 - Tissue-MC-Acc.: 0.8863
2023-09-09 14:07:56,837 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-09 14:07:56,837 [INFO] - Epoch: 68/130
2023-09-09 14:11:25,575 [INFO] - Training epoch stats:     Loss: 4.6484 - Binary-Cell-Dice: 0.8238 - Binary-Cell-Jacard: 0.7561 - Tissue-MC-Acc.: 0.9727
2023-09-09 14:13:59,999 [INFO] - Validation epoch stats:   Loss: 4.9261 - Binary-Cell-Dice: 0.8110 - Binary-Cell-Jacard: 0.7346 - PQ-Score: 0.6333 - Tissue-MC-Acc.: 0.8776
2023-09-09 14:14:42,818 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-09 14:14:42,819 [INFO] - Epoch: 69/130
2023-09-09 14:18:10,286 [INFO] - Training epoch stats:     Loss: 4.6885 - Binary-Cell-Dice: 0.8144 - Binary-Cell-Jacard: 0.7477 - Tissue-MC-Acc.: 0.9770
2023-09-09 14:20:31,571 [INFO] - Validation epoch stats:   Loss: 4.9243 - Binary-Cell-Dice: 0.8100 - Binary-Cell-Jacard: 0.7361 - PQ-Score: 0.6319 - Tissue-MC-Acc.: 0.8855
2023-09-09 14:21:02,073 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-09 14:21:02,074 [INFO] - Epoch: 70/130
2023-09-09 14:24:16,257 [INFO] - Training epoch stats:     Loss: 4.6337 - Binary-Cell-Dice: 0.8266 - Binary-Cell-Jacard: 0.7589 - Tissue-MC-Acc.: 0.9810
2023-09-09 14:26:46,768 [INFO] - Validation epoch stats:   Loss: 4.9273 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7359 - PQ-Score: 0.6341 - Tissue-MC-Acc.: 0.8784
2023-09-09 14:27:17,427 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-09 14:27:17,428 [INFO] - Epoch: 71/130
2023-09-09 14:30:54,398 [INFO] - Training epoch stats:     Loss: 4.6641 - Binary-Cell-Dice: 0.8247 - Binary-Cell-Jacard: 0.7532 - Tissue-MC-Acc.: 0.9774
2023-09-09 14:33:47,288 [INFO] - Validation epoch stats:   Loss: 4.9130 - Binary-Cell-Dice: 0.8120 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6363 - Tissue-MC-Acc.: 0.8825
2023-09-09 14:33:47,298 [INFO] - New best model - save checkpoint
2023-09-09 14:35:12,714 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-09 14:35:12,718 [INFO] - Epoch: 72/130
2023-09-09 14:38:17,507 [INFO] - Training epoch stats:     Loss: 4.6887 - Binary-Cell-Dice: 0.8254 - Binary-Cell-Jacard: 0.7505 - Tissue-MC-Acc.: 0.9711
2023-09-09 14:40:26,381 [INFO] - Validation epoch stats:   Loss: 4.9136 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7385 - PQ-Score: 0.6349 - Tissue-MC-Acc.: 0.8855
2023-09-09 14:40:52,515 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-09 14:40:52,516 [INFO] - Epoch: 73/130
2023-09-09 14:44:00,254 [INFO] - Training epoch stats:     Loss: 4.6417 - Binary-Cell-Dice: 0.8237 - Binary-Cell-Jacard: 0.7539 - Tissue-MC-Acc.: 0.9794
2023-09-09 14:46:34,806 [INFO] - Validation epoch stats:   Loss: 4.9239 - Binary-Cell-Dice: 0.8117 - Binary-Cell-Jacard: 0.7379 - PQ-Score: 0.6351 - Tissue-MC-Acc.: 0.8840
2023-09-09 14:46:52,414 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 14:46:52,415 [INFO] - Epoch: 74/130
2023-09-09 14:50:08,653 [INFO] - Training epoch stats:     Loss: 4.6497 - Binary-Cell-Dice: 0.8330 - Binary-Cell-Jacard: 0.7606 - Tissue-MC-Acc.: 0.9754
2023-09-09 14:52:28,521 [INFO] - Validation epoch stats:   Loss: 4.9195 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7365 - PQ-Score: 0.6340 - Tissue-MC-Acc.: 0.8852
2023-09-09 14:52:51,764 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-09 14:52:51,765 [INFO] - Epoch: 75/130
2023-09-09 14:56:08,061 [INFO] - Training epoch stats:     Loss: 4.6600 - Binary-Cell-Dice: 0.8316 - Binary-Cell-Jacard: 0.7547 - Tissue-MC-Acc.: 0.9794
2023-09-09 14:58:55,225 [INFO] - Validation epoch stats:   Loss: 4.9132 - Binary-Cell-Dice: 0.8121 - Binary-Cell-Jacard: 0.7380 - PQ-Score: 0.6364 - Tissue-MC-Acc.: 0.8874
2023-09-09 14:58:55,231 [INFO] - New best model - save checkpoint
2023-09-09 15:00:05,997 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-09 15:00:05,998 [INFO] - Epoch: 76/130
2023-09-09 15:03:02,265 [INFO] - Training epoch stats:     Loss: 4.6254 - Binary-Cell-Dice: 0.8319 - Binary-Cell-Jacard: 0.7625 - Tissue-MC-Acc.: 0.9826
2023-09-09 15:05:36,839 [INFO] - Validation epoch stats:   Loss: 4.9147 - Binary-Cell-Dice: 0.8108 - Binary-Cell-Jacard: 0.7370 - PQ-Score: 0.6349 - Tissue-MC-Acc.: 0.8919
2023-09-09 15:06:20,710 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 15:06:20,710 [INFO] - Epoch: 77/130
2023-09-09 15:09:06,954 [INFO] - Training epoch stats:     Loss: 4.6123 - Binary-Cell-Dice: 0.8208 - Binary-Cell-Jacard: 0.7539 - Tissue-MC-Acc.: 0.9845
2023-09-09 15:11:43,918 [INFO] - Validation epoch stats:   Loss: 4.9248 - Binary-Cell-Dice: 0.8106 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6349 - Tissue-MC-Acc.: 0.8927
2023-09-09 15:12:21,677 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-09 15:12:21,678 [INFO] - Epoch: 78/130
2023-09-09 15:16:38,448 [INFO] - Training epoch stats:     Loss: 4.6312 - Binary-Cell-Dice: 0.8229 - Binary-Cell-Jacard: 0.7583 - Tissue-MC-Acc.: 0.9798
2023-09-09 15:18:52,629 [INFO] - Validation epoch stats:   Loss: 4.9339 - Binary-Cell-Dice: 0.8107 - Binary-Cell-Jacard: 0.7353 - PQ-Score: 0.6352 - Tissue-MC-Acc.: 0.8878
2023-09-09 15:19:28,250 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-09 15:19:28,251 [INFO] - Epoch: 79/130
2023-09-09 15:22:17,997 [INFO] - Training epoch stats:     Loss: 4.6342 - Binary-Cell-Dice: 0.8258 - Binary-Cell-Jacard: 0.7553 - Tissue-MC-Acc.: 0.9758
2023-09-09 15:24:36,548 [INFO] - Validation epoch stats:   Loss: 4.9280 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7360 - PQ-Score: 0.6344 - Tissue-MC-Acc.: 0.8934
2023-09-09 15:25:09,664 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 15:25:09,665 [INFO] - Epoch: 80/130
2023-09-09 15:28:02,612 [INFO] - Training epoch stats:     Loss: 4.5892 - Binary-Cell-Dice: 0.8292 - Binary-Cell-Jacard: 0.7593 - Tissue-MC-Acc.: 0.9853
2023-09-09 15:30:34,191 [INFO] - Validation epoch stats:   Loss: 4.9257 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7382 - PQ-Score: 0.6359 - Tissue-MC-Acc.: 0.8893
2023-09-09 15:31:21,054 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 15:31:21,055 [INFO] - Epoch: 81/130
2023-09-09 15:34:11,724 [INFO] - Training epoch stats:     Loss: 4.5898 - Binary-Cell-Dice: 0.8309 - Binary-Cell-Jacard: 0.7618 - Tissue-MC-Acc.: 0.9786
2023-09-09 15:36:23,727 [INFO] - Validation epoch stats:   Loss: 4.9156 - Binary-Cell-Dice: 0.8108 - Binary-Cell-Jacard: 0.7351 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.8946
2023-09-09 15:37:21,666 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-09 15:37:21,667 [INFO] - Epoch: 82/130
2023-09-09 15:39:58,465 [INFO] - Training epoch stats:     Loss: 4.6167 - Binary-Cell-Dice: 0.8244 - Binary-Cell-Jacard: 0.7589 - Tissue-MC-Acc.: 0.9794
2023-09-09 15:42:16,591 [INFO] - Validation epoch stats:   Loss: 4.9166 - Binary-Cell-Dice: 0.8122 - Binary-Cell-Jacard: 0.7369 - PQ-Score: 0.6365 - Tissue-MC-Acc.: 0.8919
2023-09-09 15:42:16,599 [INFO] - New best model - save checkpoint
2023-09-09 15:43:17,928 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-09 15:43:17,928 [INFO] - Epoch: 83/130
2023-09-09 15:48:01,419 [INFO] - Training epoch stats:     Loss: 4.6010 - Binary-Cell-Dice: 0.8334 - Binary-Cell-Jacard: 0.7613 - Tissue-MC-Acc.: 0.9869
2023-09-09 15:50:27,008 [INFO] - Validation epoch stats:   Loss: 4.9180 - Binary-Cell-Dice: 0.8104 - Binary-Cell-Jacard: 0.7357 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.8968
2023-09-09 15:51:07,492 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 15:51:07,494 [INFO] - Epoch: 84/130
2023-09-09 15:54:14,093 [INFO] - Training epoch stats:     Loss: 4.6292 - Binary-Cell-Dice: 0.8331 - Binary-Cell-Jacard: 0.7591 - Tissue-MC-Acc.: 0.9849
2023-09-09 16:01:39,177 [INFO] - Validation epoch stats:   Loss: 4.9177 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7372 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.8957
2023-09-09 16:02:19,015 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 16:02:19,017 [INFO] - Epoch: 85/130
2023-09-09 16:07:37,279 [INFO] - Training epoch stats:     Loss: 4.6125 - Binary-Cell-Dice: 0.8270 - Binary-Cell-Jacard: 0.7600 - Tissue-MC-Acc.: 0.9845
2023-09-09 16:10:08,379 [INFO] - Validation epoch stats:   Loss: 4.9254 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6357 - Tissue-MC-Acc.: 0.8991
2023-09-09 16:10:49,510 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 16:10:49,511 [INFO] - Epoch: 86/130
2023-09-09 16:15:11,303 [INFO] - Training epoch stats:     Loss: 4.6084 - Binary-Cell-Dice: 0.8231 - Binary-Cell-Jacard: 0.7599 - Tissue-MC-Acc.: 0.9810
2023-09-09 16:18:09,769 [INFO] - Validation epoch stats:   Loss: 4.9293 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7352 - PQ-Score: 0.6347 - Tissue-MC-Acc.: 0.8995
2023-09-09 16:19:01,562 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-09 16:19:01,563 [INFO] - Epoch: 87/130
2023-09-09 16:21:52,926 [INFO] - Training epoch stats:     Loss: 4.5957 - Binary-Cell-Dice: 0.8279 - Binary-Cell-Jacard: 0.7624 - Tissue-MC-Acc.: 0.9865
2023-09-09 16:24:08,666 [INFO] - Validation epoch stats:   Loss: 4.9210 - Binary-Cell-Dice: 0.8121 - Binary-Cell-Jacard: 0.7375 - PQ-Score: 0.6366 - Tissue-MC-Acc.: 0.8965
2023-09-09 16:24:08,671 [INFO] - New best model - save checkpoint
2023-09-09 16:25:21,782 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-09 16:25:21,787 [INFO] - Epoch: 88/130
2023-09-09 16:29:23,450 [INFO] - Training epoch stats:     Loss: 4.5858 - Binary-Cell-Dice: 0.8268 - Binary-Cell-Jacard: 0.7581 - Tissue-MC-Acc.: 0.9857
2023-09-09 16:31:41,700 [INFO] - Validation epoch stats:   Loss: 4.9195 - Binary-Cell-Dice: 0.8111 - Binary-Cell-Jacard: 0.7365 - PQ-Score: 0.6354 - Tissue-MC-Acc.: 0.8961
2023-09-09 16:32:07,518 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 16:32:07,520 [INFO] - Epoch: 89/130
2023-09-09 16:36:50,996 [INFO] - Training epoch stats:     Loss: 4.5905 - Binary-Cell-Dice: 0.8264 - Binary-Cell-Jacard: 0.7604 - Tissue-MC-Acc.: 0.9806
2023-09-09 16:39:21,160 [INFO] - Validation epoch stats:   Loss: 4.9227 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7361 - PQ-Score: 0.6344 - Tissue-MC-Acc.: 0.8946
2023-09-09 16:39:58,535 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 16:39:58,536 [INFO] - Epoch: 90/130
2023-09-09 16:48:44,190 [INFO] - Training epoch stats:     Loss: 4.5684 - Binary-Cell-Dice: 0.8327 - Binary-Cell-Jacard: 0.7627 - Tissue-MC-Acc.: 0.9913
2023-09-09 16:51:24,806 [INFO] - Validation epoch stats:   Loss: 4.9212 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6350 - Tissue-MC-Acc.: 0.8987
2023-09-09 16:52:02,509 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 16:52:02,510 [INFO] - Epoch: 91/130
2023-09-09 16:55:21,274 [INFO] - Training epoch stats:     Loss: 4.6071 - Binary-Cell-Dice: 0.8196 - Binary-Cell-Jacard: 0.7595 - Tissue-MC-Acc.: 0.9865
2023-09-09 17:00:07,953 [INFO] - Validation epoch stats:   Loss: 4.9175 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7369 - PQ-Score: 0.6362 - Tissue-MC-Acc.: 0.8946
2023-09-09 17:00:38,010 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 17:00:38,010 [INFO] - Epoch: 92/130
2023-09-09 17:03:48,743 [INFO] - Training epoch stats:     Loss: 4.5970 - Binary-Cell-Dice: 0.8304 - Binary-Cell-Jacard: 0.7624 - Tissue-MC-Acc.: 0.9893
2023-09-09 17:07:28,627 [INFO] - Validation epoch stats:   Loss: 4.9336 - Binary-Cell-Dice: 0.8110 - Binary-Cell-Jacard: 0.7361 - PQ-Score: 0.6367 - Tissue-MC-Acc.: 0.8972
2023-09-09 17:07:28,630 [INFO] - New best model - save checkpoint
2023-09-09 17:08:04,879 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 17:08:04,880 [INFO] - Epoch: 93/130
2023-09-09 17:13:59,981 [INFO] - Training epoch stats:     Loss: 4.5701 - Binary-Cell-Dice: 0.8247 - Binary-Cell-Jacard: 0.7613 - Tissue-MC-Acc.: 0.9885
2023-09-09 17:18:52,290 [INFO] - Validation epoch stats:   Loss: 4.9232 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7361 - PQ-Score: 0.6357 - Tissue-MC-Acc.: 0.8987
2023-09-09 17:19:33,015 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-09 17:19:33,016 [INFO] - Epoch: 94/130
2023-09-09 17:24:16,689 [INFO] - Training epoch stats:     Loss: 4.5885 - Binary-Cell-Dice: 0.8319 - Binary-Cell-Jacard: 0.7597 - Tissue-MC-Acc.: 0.9865
2023-09-09 17:26:38,397 [INFO] - Validation epoch stats:   Loss: 4.9183 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7369 - PQ-Score: 0.6357 - Tissue-MC-Acc.: 0.8983
2023-09-09 17:27:20,066 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-09 17:27:20,067 [INFO] - Epoch: 95/130
2023-09-09 17:30:16,152 [INFO] - Training epoch stats:     Loss: 4.6463 - Binary-Cell-Dice: 0.8276 - Binary-Cell-Jacard: 0.7566 - Tissue-MC-Acc.: 0.9837
2023-09-09 17:34:15,467 [INFO] - Validation epoch stats:   Loss: 4.9294 - Binary-Cell-Dice: 0.8102 - Binary-Cell-Jacard: 0.7366 - PQ-Score: 0.6347 - Tissue-MC-Acc.: 0.8968
2023-09-09 17:35:10,479 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 17:35:10,480 [INFO] - Epoch: 96/130
2023-09-09 17:38:09,963 [INFO] - Training epoch stats:     Loss: 4.6296 - Binary-Cell-Dice: 0.8275 - Binary-Cell-Jacard: 0.7612 - Tissue-MC-Acc.: 0.9889
2023-09-09 17:40:18,623 [INFO] - Validation epoch stats:   Loss: 4.9207 - Binary-Cell-Dice: 0.8107 - Binary-Cell-Jacard: 0.7366 - PQ-Score: 0.6348 - Tissue-MC-Acc.: 0.8953
2023-09-09 17:41:00,342 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 17:41:00,343 [INFO] - Epoch: 97/130
2023-09-09 17:43:41,853 [INFO] - Training epoch stats:     Loss: 4.5501 - Binary-Cell-Dice: 0.8306 - Binary-Cell-Jacard: 0.7644 - Tissue-MC-Acc.: 0.9885
2023-09-09 17:45:50,203 [INFO] - Validation epoch stats:   Loss: 4.9149 - Binary-Cell-Dice: 0.8117 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6365 - Tissue-MC-Acc.: 0.8968
2023-09-09 17:46:40,186 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 17:46:40,187 [INFO] - Epoch: 98/130
2023-09-09 17:49:33,038 [INFO] - Training epoch stats:     Loss: 4.5941 - Binary-Cell-Dice: 0.8314 - Binary-Cell-Jacard: 0.7604 - Tissue-MC-Acc.: 0.9881
2023-09-09 17:51:44,354 [INFO] - Validation epoch stats:   Loss: 4.9285 - Binary-Cell-Dice: 0.8117 - Binary-Cell-Jacard: 0.7362 - PQ-Score: 0.6370 - Tissue-MC-Acc.: 0.8998
2023-09-09 17:51:44,358 [INFO] - New best model - save checkpoint
2023-09-09 17:52:29,307 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 17:52:29,307 [INFO] - Epoch: 99/130
2023-09-09 17:55:14,794 [INFO] - Training epoch stats:     Loss: 4.5728 - Binary-Cell-Dice: 0.8283 - Binary-Cell-Jacard: 0.7631 - Tissue-MC-Acc.: 0.9889
2023-09-09 17:57:42,108 [INFO] - Validation epoch stats:   Loss: 4.9213 - Binary-Cell-Dice: 0.8119 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6375 - Tissue-MC-Acc.: 0.8961
2023-09-09 17:57:42,115 [INFO] - New best model - save checkpoint
2023-09-09 17:59:06,852 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 17:59:06,953 [INFO] - Epoch: 100/130
2023-09-09 18:02:14,733 [INFO] - Training epoch stats:     Loss: 4.5776 - Binary-Cell-Dice: 0.8300 - Binary-Cell-Jacard: 0.7631 - Tissue-MC-Acc.: 0.9861
2023-09-09 18:04:41,519 [INFO] - Validation epoch stats:   Loss: 4.9208 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7373 - PQ-Score: 0.6363 - Tissue-MC-Acc.: 0.8980
2023-09-09 18:04:59,141 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 18:04:59,141 [INFO] - Epoch: 101/130
2023-09-09 18:11:16,550 [INFO] - Training epoch stats:     Loss: 4.6000 - Binary-Cell-Dice: 0.8313 - Binary-Cell-Jacard: 0.7615 - Tissue-MC-Acc.: 0.9873
2023-09-09 18:14:52,988 [INFO] - Validation epoch stats:   Loss: 4.9224 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7363 - PQ-Score: 0.6353 - Tissue-MC-Acc.: 0.8991
2023-09-09 18:15:34,511 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 18:15:34,513 [INFO] - Epoch: 102/130
2023-09-09 18:28:25,197 [INFO] - Training epoch stats:     Loss: 4.6101 - Binary-Cell-Dice: 0.8296 - Binary-Cell-Jacard: 0.7651 - Tissue-MC-Acc.: 0.9857
2023-09-09 18:32:59,941 [INFO] - Validation epoch stats:   Loss: 4.9201 - Binary-Cell-Dice: 0.8114 - Binary-Cell-Jacard: 0.7363 - PQ-Score: 0.6368 - Tissue-MC-Acc.: 0.8957
2023-09-09 18:33:42,651 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 18:33:42,652 [INFO] - Epoch: 103/130
2023-09-09 18:36:28,028 [INFO] - Training epoch stats:     Loss: 4.5892 - Binary-Cell-Dice: 0.8313 - Binary-Cell-Jacard: 0.7626 - Tissue-MC-Acc.: 0.9889
2023-09-09 18:38:45,888 [INFO] - Validation epoch stats:   Loss: 4.9227 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7362 - PQ-Score: 0.6356 - Tissue-MC-Acc.: 0.8953
2023-09-09 18:39:09,546 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-09 18:39:09,547 [INFO] - Epoch: 104/130
2023-09-09 18:42:19,088 [INFO] - Training epoch stats:     Loss: 4.5999 - Binary-Cell-Dice: 0.8302 - Binary-Cell-Jacard: 0.7620 - Tissue-MC-Acc.: 0.9873
2023-09-09 18:44:46,945 [INFO] - Validation epoch stats:   Loss: 4.9196 - Binary-Cell-Dice: 0.8111 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6360 - Tissue-MC-Acc.: 0.8961
2023-09-09 18:45:28,640 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-09 18:45:28,640 [INFO] - Epoch: 105/130
2023-09-09 18:48:45,410 [INFO] - Training epoch stats:     Loss: 4.5319 - Binary-Cell-Dice: 0.8329 - Binary-Cell-Jacard: 0.7655 - Tissue-MC-Acc.: 0.9921
2023-09-09 18:51:20,256 [INFO] - Validation epoch stats:   Loss: 4.9202 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7368 - PQ-Score: 0.6357 - Tissue-MC-Acc.: 0.8965
2023-09-09 18:51:52,322 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 18:51:52,323 [INFO] - Epoch: 106/130
2023-09-09 18:55:12,676 [INFO] - Training epoch stats:     Loss: 4.5431 - Binary-Cell-Dice: 0.8333 - Binary-Cell-Jacard: 0.7649 - Tissue-MC-Acc.: 0.9873
2023-09-09 18:57:43,438 [INFO] - Validation epoch stats:   Loss: 4.9231 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7366 - PQ-Score: 0.6355 - Tissue-MC-Acc.: 0.8983
2023-09-09 18:58:07,457 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 18:58:07,458 [INFO] - Epoch: 107/130
2023-09-09 19:02:41,537 [INFO] - Training epoch stats:     Loss: 4.5879 - Binary-Cell-Dice: 0.8366 - Binary-Cell-Jacard: 0.7641 - Tissue-MC-Acc.: 0.9845
2023-09-09 19:05:01,625 [INFO] - Validation epoch stats:   Loss: 4.9284 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7369 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.8980
2023-09-09 19:05:45,928 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:05:45,929 [INFO] - Epoch: 108/130
2023-09-09 19:08:33,889 [INFO] - Training epoch stats:     Loss: 4.6010 - Binary-Cell-Dice: 0.8277 - Binary-Cell-Jacard: 0.7635 - Tissue-MC-Acc.: 0.9873
2023-09-09 19:10:56,022 [INFO] - Validation epoch stats:   Loss: 4.9245 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6361 - Tissue-MC-Acc.: 0.9002
2023-09-09 19:11:26,967 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:11:26,968 [INFO] - Epoch: 109/130
2023-09-09 19:14:55,699 [INFO] - Training epoch stats:     Loss: 4.6028 - Binary-Cell-Dice: 0.8321 - Binary-Cell-Jacard: 0.7587 - Tissue-MC-Acc.: 0.9893
2023-09-09 19:17:17,532 [INFO] - Validation epoch stats:   Loss: 4.9227 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7361 - PQ-Score: 0.6366 - Tissue-MC-Acc.: 0.9002
2023-09-09 19:17:59,985 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:17:59,986 [INFO] - Epoch: 110/130
2023-09-09 19:21:22,423 [INFO] - Training epoch stats:     Loss: 4.5702 - Binary-Cell-Dice: 0.8257 - Binary-Cell-Jacard: 0.7614 - Tissue-MC-Acc.: 0.9901
2023-09-09 19:23:56,207 [INFO] - Validation epoch stats:   Loss: 4.9219 - Binary-Cell-Dice: 0.8114 - Binary-Cell-Jacard: 0.7373 - PQ-Score: 0.6363 - Tissue-MC-Acc.: 0.8991
2023-09-09 19:24:13,419 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:24:13,419 [INFO] - Epoch: 111/130
2023-09-09 19:27:21,399 [INFO] - Training epoch stats:     Loss: 4.5779 - Binary-Cell-Dice: 0.8283 - Binary-Cell-Jacard: 0.7603 - Tissue-MC-Acc.: 0.9885
2023-09-09 19:29:45,112 [INFO] - Validation epoch stats:   Loss: 4.9194 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7373 - PQ-Score: 0.6365 - Tissue-MC-Acc.: 0.9014
2023-09-09 19:30:26,673 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:30:26,674 [INFO] - Epoch: 112/130
2023-09-09 19:33:58,873 [INFO] - Training epoch stats:     Loss: 4.5508 - Binary-Cell-Dice: 0.8370 - Binary-Cell-Jacard: 0.7650 - Tissue-MC-Acc.: 0.9885
2023-09-09 19:36:24,195 [INFO] - Validation epoch stats:   Loss: 4.9268 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7368 - PQ-Score: 0.6356 - Tissue-MC-Acc.: 0.9006
2023-09-09 19:37:08,308 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:37:08,309 [INFO] - Epoch: 113/130
2023-09-09 19:40:37,217 [INFO] - Training epoch stats:     Loss: 4.5794 - Binary-Cell-Dice: 0.8295 - Binary-Cell-Jacard: 0.7601 - Tissue-MC-Acc.: 0.9869
2023-09-09 19:42:47,998 [INFO] - Validation epoch stats:   Loss: 4.9235 - Binary-Cell-Dice: 0.8116 - Binary-Cell-Jacard: 0.7370 - PQ-Score: 0.6357 - Tissue-MC-Acc.: 0.8998
2023-09-09 19:43:06,500 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:43:06,501 [INFO] - Epoch: 114/130
2023-09-09 19:48:00,482 [INFO] - Training epoch stats:     Loss: 4.5164 - Binary-Cell-Dice: 0.8268 - Binary-Cell-Jacard: 0.7654 - Tissue-MC-Acc.: 0.9873
2023-09-09 19:50:36,108 [INFO] - Validation epoch stats:   Loss: 4.9326 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7367 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.9021
2023-09-09 19:51:21,925 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:51:21,925 [INFO] - Epoch: 115/130
2023-09-09 19:55:06,165 [INFO] - Training epoch stats:     Loss: 4.5370 - Binary-Cell-Dice: 0.8270 - Binary-Cell-Jacard: 0.7631 - Tissue-MC-Acc.: 0.9897
2023-09-09 19:57:30,882 [INFO] - Validation epoch stats:   Loss: 4.9266 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7373 - PQ-Score: 0.6363 - Tissue-MC-Acc.: 0.9006
2023-09-09 19:57:48,091 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 19:57:48,092 [INFO] - Epoch: 116/130
2023-09-09 20:01:46,103 [INFO] - Training epoch stats:     Loss: 4.5832 - Binary-Cell-Dice: 0.8297 - Binary-Cell-Jacard: 0.7595 - Tissue-MC-Acc.: 0.9873
2023-09-09 20:04:24,466 [INFO] - Validation epoch stats:   Loss: 4.9194 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7366 - PQ-Score: 0.6360 - Tissue-MC-Acc.: 0.9014
2023-09-09 20:05:06,738 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:05:06,738 [INFO] - Epoch: 117/130
2023-09-09 20:08:30,920 [INFO] - Training epoch stats:     Loss: 4.5744 - Binary-Cell-Dice: 0.8293 - Binary-Cell-Jacard: 0.7632 - Tissue-MC-Acc.: 0.9873
2023-09-09 20:10:51,128 [INFO] - Validation epoch stats:   Loss: 4.9245 - Binary-Cell-Dice: 0.8109 - Binary-Cell-Jacard: 0.7370 - PQ-Score: 0.6354 - Tissue-MC-Acc.: 0.9002
2023-09-09 20:11:11,421 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:11:11,422 [INFO] - Epoch: 118/130
2023-09-09 20:13:59,546 [INFO] - Training epoch stats:     Loss: 4.5682 - Binary-Cell-Dice: 0.8321 - Binary-Cell-Jacard: 0.7636 - Tissue-MC-Acc.: 0.9909
2023-09-09 20:16:44,397 [INFO] - Validation epoch stats:   Loss: 4.9251 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6356 - Tissue-MC-Acc.: 0.9006
2023-09-09 20:17:07,632 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:17:07,633 [INFO] - Epoch: 119/130
2023-09-09 20:20:55,716 [INFO] - Training epoch stats:     Loss: 4.5639 - Binary-Cell-Dice: 0.8283 - Binary-Cell-Jacard: 0.7650 - Tissue-MC-Acc.: 0.9893
2023-09-09 20:23:23,573 [INFO] - Validation epoch stats:   Loss: 4.9189 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7375 - PQ-Score: 0.6362 - Tissue-MC-Acc.: 0.9010
2023-09-09 20:24:05,055 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:24:05,055 [INFO] - Epoch: 120/130
2023-09-09 20:26:51,209 [INFO] - Training epoch stats:     Loss: 4.5475 - Binary-Cell-Dice: 0.8342 - Binary-Cell-Jacard: 0.7668 - Tissue-MC-Acc.: 0.9897
2023-09-09 20:29:12,892 [INFO] - Validation epoch stats:   Loss: 4.9254 - Binary-Cell-Dice: 0.8107 - Binary-Cell-Jacard: 0.7374 - PQ-Score: 0.6358 - Tissue-MC-Acc.: 0.9017
2023-09-09 20:29:31,265 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:29:31,265 [INFO] - Epoch: 121/130
2023-09-09 20:32:44,568 [INFO] - Training epoch stats:     Loss: 4.5517 - Binary-Cell-Dice: 0.8285 - Binary-Cell-Jacard: 0.7650 - Tissue-MC-Acc.: 0.9901
2023-09-09 20:35:28,071 [INFO] - Validation epoch stats:   Loss: 4.9274 - Binary-Cell-Dice: 0.8115 - Binary-Cell-Jacard: 0.7371 - PQ-Score: 0.6348 - Tissue-MC-Acc.: 0.9010
2023-09-09 20:36:25,423 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:36:25,424 [INFO] - Epoch: 122/130
2023-09-09 20:42:05,064 [INFO] - Training epoch stats:     Loss: 4.5679 - Binary-Cell-Dice: 0.8347 - Binary-Cell-Jacard: 0.7650 - Tissue-MC-Acc.: 0.9881
2023-09-09 20:44:17,164 [INFO] - Validation epoch stats:   Loss: 4.9259 - Binary-Cell-Dice: 0.8111 - Binary-Cell-Jacard: 0.7364 - PQ-Score: 0.6356 - Tissue-MC-Acc.: 0.9010
2023-09-09 20:44:51,010 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:44:51,011 [INFO] - Epoch: 123/130
2023-09-09 20:47:32,925 [INFO] - Training epoch stats:     Loss: 4.5578 - Binary-Cell-Dice: 0.8294 - Binary-Cell-Jacard: 0.7612 - Tissue-MC-Acc.: 0.9909
2023-09-09 20:50:11,691 [INFO] - Validation epoch stats:   Loss: 4.9204 - Binary-Cell-Dice: 0.8119 - Binary-Cell-Jacard: 0.7378 - PQ-Score: 0.6356 - Tissue-MC-Acc.: 0.9002
2023-09-09 20:50:29,686 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:50:29,687 [INFO] - Epoch: 124/130
2023-09-09 20:53:57,545 [INFO] - Training epoch stats:     Loss: 4.6039 - Binary-Cell-Dice: 0.8292 - Binary-Cell-Jacard: 0.7627 - Tissue-MC-Acc.: 0.9881
2023-09-09 20:56:30,616 [INFO] - Validation epoch stats:   Loss: 4.9199 - Binary-Cell-Dice: 0.8106 - Binary-Cell-Jacard: 0.7376 - PQ-Score: 0.6360 - Tissue-MC-Acc.: 0.8983
2023-09-09 20:57:18,503 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-09 20:57:18,504 [INFO] - Epoch: 125/130
2023-09-09 20:59:55,916 [INFO] - Training epoch stats:     Loss: 4.5953 - Binary-Cell-Dice: 0.8248 - Binary-Cell-Jacard: 0.7622 - Tissue-MC-Acc.: 0.9822
2023-09-09 21:02:04,241 [INFO] - Validation epoch stats:   Loss: 4.9270 - Binary-Cell-Dice: 0.8104 - Binary-Cell-Jacard: 0.7373 - PQ-Score: 0.6361 - Tissue-MC-Acc.: 0.9006
2023-09-09 21:02:53,200 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-09 21:02:53,201 [INFO] - Epoch: 126/130
2023-09-09 21:05:38,433 [INFO] - Training epoch stats:     Loss: 4.5907 - Binary-Cell-Dice: 0.8300 - Binary-Cell-Jacard: 0.7629 - Tissue-MC-Acc.: 0.9901
2023-09-09 21:08:03,449 [INFO] - Validation epoch stats:   Loss: 4.9235 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7381 - PQ-Score: 0.6349 - Tissue-MC-Acc.: 0.9010
2023-09-09 21:08:47,851 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 21:08:47,852 [INFO] - Epoch: 127/130
2023-09-09 21:11:28,290 [INFO] - Training epoch stats:     Loss: 4.5952 - Binary-Cell-Dice: 0.8335 - Binary-Cell-Jacard: 0.7659 - Tissue-MC-Acc.: 0.9881
2023-09-09 21:13:46,605 [INFO] - Validation epoch stats:   Loss: 4.9269 - Binary-Cell-Dice: 0.8111 - Binary-Cell-Jacard: 0.7374 - PQ-Score: 0.6361 - Tissue-MC-Acc.: 0.9010
2023-09-09 21:14:15,137 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 21:14:15,137 [INFO] - Epoch: 128/130
2023-09-09 21:17:31,281 [INFO] - Training epoch stats:     Loss: 4.5651 - Binary-Cell-Dice: 0.8334 - Binary-Cell-Jacard: 0.7655 - Tissue-MC-Acc.: 0.9905
2023-09-09 21:19:56,034 [INFO] - Validation epoch stats:   Loss: 4.9155 - Binary-Cell-Dice: 0.8118 - Binary-Cell-Jacard: 0.7383 - PQ-Score: 0.6361 - Tissue-MC-Acc.: 0.9014
2023-09-09 21:20:35,341 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 21:20:35,342 [INFO] - Epoch: 129/130
2023-09-09 21:23:38,388 [INFO] - Training epoch stats:     Loss: 4.5728 - Binary-Cell-Dice: 0.8321 - Binary-Cell-Jacard: 0.7666 - Tissue-MC-Acc.: 0.9885
2023-09-09 21:25:53,065 [INFO] - Validation epoch stats:   Loss: 4.9221 - Binary-Cell-Dice: 0.8112 - Binary-Cell-Jacard: 0.7374 - PQ-Score: 0.6365 - Tissue-MC-Acc.: 0.9010
2023-09-09 21:26:10,620 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 21:26:10,621 [INFO] - Epoch: 130/130
2023-09-09 21:29:09,801 [INFO] - Training epoch stats:     Loss: 4.5719 - Binary-Cell-Dice: 0.8285 - Binary-Cell-Jacard: 0.7616 - Tissue-MC-Acc.: 0.9913
2023-09-09 21:31:48,304 [INFO] - Validation epoch stats:   Loss: 4.9281 - Binary-Cell-Dice: 0.8113 - Binary-Cell-Jacard: 0.7372 - PQ-Score: 0.6367 - Tissue-MC-Acc.: 0.9014
2023-09-09 21:32:27,717 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-09 21:32:27,723 [INFO] -
