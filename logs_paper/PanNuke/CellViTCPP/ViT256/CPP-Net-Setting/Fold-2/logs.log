2023-09-23 10:19:50,426 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-23 10:19:50,489 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7f84c3afb850>]
2023-09-23 10:19:50,489 [INFO] - Using GPU: cuda:0
2023-09-23 10:19:50,490 [INFO] - Using device: cuda:0
2023-09-23 10:19:50,491 [INFO] - Loss functions:
2023-09-23 10:19:50,491 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'stardist_map_refined': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': DiceLoss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-23 10:20:12,487 [INFO] - Loaded CellVit256 model
2023-09-23 10:20:12,490 [INFO] -
Model: CellViT256CPP(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (stardist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (dist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (stardist_head): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (dist_head): Conv2d(32, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (type_head): Conv2d(32, 6, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
  (conv_0_confidence): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (conv_1_confidence): Conv2d(6, 6, kernel_size=(1, 1), stride=(1, 1))
  (sampling_features): SamplingFeatures()
  (final_activation_ray): ReLU(inplace=True)
)
2023-09-23 10:20:13,941 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256CPP                                 [1, 19]                   6,802,595
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 32, 256, 256]         2,080
├─Sequential: 1-27                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 32, 256, 256]         2,080
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-20                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          (recursive)
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          (recursive)
├─Sequential: 1-22                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          (recursive)
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        (recursive)
├─Sequential: 1-24                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        (recursive)
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         (recursive)
├─Sequential: 1-26                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 32, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2d: 3-76                      [1, 32, 256, 256]         (recursive)
├─Conv2d: 1-28                                [1, 32, 256, 256]         1,024
├─Conv2d: 1-29                                [1, 1, 256, 256]          32
├─Conv2d: 1-30                                [1, 6, 256, 256]          192
├─Conv2d: 1-31                                [1, 32, 256, 256]         1,024
├─SamplingFeatures: 1-32                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-33                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-34                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-35                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-36                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-37                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-38                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-39                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-40                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-41                      [1, 32, 256, 256]         --
├─Conv2d: 1-42                                [32, 6, 256, 256]         42
├─ReLU: 1-43                                  [1, 32, 256, 256]         --
===============================================================================================
Total params: 46,763,136
Trainable params: 25,097,472
Non-trainable params: 21,665,664
Total mult-adds (G): 133.49
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1855.47
Params size (MB): 159.54
Estimated Total Size (MB): 2015.80
===============================================================================================
2023-09-23 10:20:24,197 [INFO] - Loaded Adam Optimizer with following hyperparameters:
2023-09-23 10:20:24,199 [INFO] - {'lr': 0.0001}
2023-09-23 10:20:24,199 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-23 10:20:25,567 [INFO] - Using RandomSampler
2023-09-23 10:20:25,567 [INFO] - Instantiate Trainer
2023-09-23 10:20:25,568 [INFO] - Calling Trainer Fit
2023-09-23 10:20:25,568 [INFO] - Starting training, total number of epochs: 130
2023-09-23 10:20:25,568 [INFO] - Epoch: 1/130
2023-09-23 10:23:27,956 [INFO] - Training epoch stats:     Loss: 5.1279 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0674
2023-09-23 10:29:15,088 [INFO] - Validation epoch stats:   Loss: 4.6261 - Binary-Cell-Dice: 0.1212 - Binary-Cell-Jacard: 0.0801 - bPQ-Score: 0.0002 - mPQ-Score: 0.0000 - Tissue-MC-Acc.: 0.1589
2023-09-23 10:29:15,098 [INFO] - New best model - save checkpoint
2023-09-23 10:29:44,124 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:29:44,124 [INFO] - Epoch: 2/130
2023-09-23 10:32:33,531 [INFO] - Training epoch stats:     Loss: 4.3185 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0598
2023-09-23 10:35:32,470 [INFO] - Validation epoch stats:   Loss: 4.0426 - Binary-Cell-Dice: 0.3602 - Binary-Cell-Jacard: 0.2640 - bPQ-Score: 0.1609 - mPQ-Score: 0.0662 - Tissue-MC-Acc.: 0.0282
2023-09-23 10:35:32,480 [INFO] - New best model - save checkpoint
2023-09-23 10:36:02,531 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:36:02,532 [INFO] - Epoch: 3/130
2023-09-23 10:38:50,697 [INFO] - Training epoch stats:     Loss: 3.9498 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0380
2023-09-23 10:41:39,716 [INFO] - Validation epoch stats:   Loss: 3.9306 - Binary-Cell-Dice: 0.2600 - Binary-Cell-Jacard: 0.1780 - bPQ-Score: 0.1405 - mPQ-Score: 0.0614 - Tissue-MC-Acc.: 0.0294
2023-09-23 10:41:54,998 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:41:54,998 [INFO] - Epoch: 4/130
2023-09-23 10:44:46,518 [INFO] - Training epoch stats:     Loss: 3.7959 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 10:47:32,072 [INFO] - Validation epoch stats:   Loss: 3.7443 - Binary-Cell-Dice: 0.3113 - Binary-Cell-Jacard: 0.2211 - bPQ-Score: 0.1953 - mPQ-Score: 0.1119 - Tissue-MC-Acc.: 0.0215
2023-09-23 10:47:32,083 [INFO] - New best model - save checkpoint
2023-09-23 10:47:59,304 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:47:59,305 [INFO] - Epoch: 5/130
2023-09-23 10:50:38,075 [INFO] - Training epoch stats:     Loss: 3.7030 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0337
2023-09-23 10:53:38,228 [INFO] - Validation epoch stats:   Loss: 3.6512 - Binary-Cell-Dice: 0.4711 - Binary-Cell-Jacard: 0.3527 - bPQ-Score: 0.2917 - mPQ-Score: 0.1687 - Tissue-MC-Acc.: 0.0245
2023-09-23 10:53:38,327 [INFO] - New best model - save checkpoint
2023-09-23 10:54:08,047 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:54:08,048 [INFO] - Epoch: 6/130
2023-09-23 10:56:54,346 [INFO] - Training epoch stats:     Loss: 3.6595 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-23 11:00:11,656 [INFO] - Validation epoch stats:   Loss: 3.6208 - Binary-Cell-Dice: 0.5619 - Binary-Cell-Jacard: 0.4421 - bPQ-Score: 0.3738 - mPQ-Score: 0.2256 - Tissue-MC-Acc.: 0.0320
2023-09-23 11:00:11,741 [INFO] - New best model - save checkpoint
2023-09-23 11:00:38,041 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:00:38,042 [INFO] - Epoch: 7/130
2023-09-23 11:03:14,260 [INFO] - Training epoch stats:     Loss: 3.6293 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0329
2023-09-23 11:06:21,915 [INFO] - Validation epoch stats:   Loss: 3.6437 - Binary-Cell-Dice: 0.5769 - Binary-Cell-Jacard: 0.4507 - bPQ-Score: 0.3803 - mPQ-Score: 0.2206 - Tissue-MC-Acc.: 0.0222
2023-09-23 11:06:21,920 [INFO] - New best model - save checkpoint
2023-09-23 11:06:58,313 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:06:58,314 [INFO] - Epoch: 8/130
2023-09-23 11:09:52,398 [INFO] - Training epoch stats:     Loss: 3.5945 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0262
2023-09-23 11:12:54,059 [INFO] - Validation epoch stats:   Loss: 3.5673 - Binary-Cell-Dice: 0.5925 - Binary-Cell-Jacard: 0.4711 - bPQ-Score: 0.3915 - mPQ-Score: 0.2476 - Tissue-MC-Acc.: 0.0301
2023-09-23 11:12:54,062 [INFO] - New best model - save checkpoint
2023-09-23 11:13:05,985 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:13:05,985 [INFO] - Epoch: 9/130
2023-09-23 11:15:47,723 [INFO] - Training epoch stats:     Loss: 3.5647 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-23 11:19:14,101 [INFO] - Validation epoch stats:   Loss: 3.6188 - Binary-Cell-Dice: 0.6816 - Binary-Cell-Jacard: 0.5619 - bPQ-Score: 0.4595 - mPQ-Score: 0.2737 - Tissue-MC-Acc.: 0.0309
2023-09-23 11:19:14,110 [INFO] - New best model - save checkpoint
2023-09-23 11:19:52,189 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:19:52,189 [INFO] - Epoch: 10/130
2023-09-23 11:22:36,602 [INFO] - Training epoch stats:     Loss: 3.5538 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-23 11:25:41,893 [INFO] - Validation epoch stats:   Loss: 3.5305 - Binary-Cell-Dice: 0.6063 - Binary-Cell-Jacard: 0.4798 - bPQ-Score: 0.4115 - mPQ-Score: 0.2647 - Tissue-MC-Acc.: 0.0290
2023-09-23 11:25:49,813 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:25:49,813 [INFO] - Epoch: 11/130
2023-09-23 11:28:30,437 [INFO] - Training epoch stats:     Loss: 3.5186 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-23 11:31:59,395 [INFO] - Validation epoch stats:   Loss: 3.5088 - Binary-Cell-Dice: 0.6015 - Binary-Cell-Jacard: 0.4669 - bPQ-Score: 0.3436 - mPQ-Score: 0.2323 - Tissue-MC-Acc.: 0.0316
2023-09-23 11:32:14,210 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:32:14,211 [INFO] - Epoch: 12/130
2023-09-23 11:34:59,952 [INFO] - Training epoch stats:     Loss: 3.4976 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0254
2023-09-23 11:38:41,107 [INFO] - Validation epoch stats:   Loss: 3.5206 - Binary-Cell-Dice: 0.7156 - Binary-Cell-Jacard: 0.6038 - bPQ-Score: 0.4814 - mPQ-Score: 0.3213 - Tissue-MC-Acc.: 0.0297
2023-09-23 11:38:41,112 [INFO] - New best model - save checkpoint
2023-09-23 11:39:10,768 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:39:10,769 [INFO] - Epoch: 13/130
2023-09-23 11:41:54,769 [INFO] - Training epoch stats:     Loss: 3.5023 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0246
2023-09-23 11:45:18,613 [INFO] - Validation epoch stats:   Loss: 3.4871 - Binary-Cell-Dice: 0.6876 - Binary-Cell-Jacard: 0.5778 - bPQ-Score: 0.4792 - mPQ-Score: 0.3172 - Tissue-MC-Acc.: 0.0339
2023-09-23 11:45:33,297 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:45:33,298 [INFO] - Epoch: 14/130
2023-09-23 11:48:22,035 [INFO] - Training epoch stats:     Loss: 3.4894 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-23 11:51:44,310 [INFO] - Validation epoch stats:   Loss: 3.4991 - Binary-Cell-Dice: 0.6807 - Binary-Cell-Jacard: 0.5665 - bPQ-Score: 0.4751 - mPQ-Score: 0.3210 - Tissue-MC-Acc.: 0.0286
2023-09-23 11:51:59,363 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:51:59,364 [INFO] - Epoch: 15/130
2023-09-23 11:54:45,643 [INFO] - Training epoch stats:     Loss: 3.4883 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0214
2023-09-23 11:58:14,873 [INFO] - Validation epoch stats:   Loss: 3.5061 - Binary-Cell-Dice: 0.7002 - Binary-Cell-Jacard: 0.5823 - bPQ-Score: 0.4771 - mPQ-Score: 0.3217 - Tissue-MC-Acc.: 0.0286
2023-09-23 11:58:29,976 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:58:29,976 [INFO] - Epoch: 16/130
2023-09-23 12:01:14,410 [INFO] - Training epoch stats:     Loss: 3.4749 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-23 12:04:35,540 [INFO] - Validation epoch stats:   Loss: 3.4759 - Binary-Cell-Dice: 0.7058 - Binary-Cell-Jacard: 0.5929 - bPQ-Score: 0.4794 - mPQ-Score: 0.3309 - Tissue-MC-Acc.: 0.0294
2023-09-23 12:04:49,999 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:04:50,000 [INFO] - Epoch: 17/130
2023-09-23 12:07:35,807 [INFO] - Training epoch stats:     Loss: 3.4830 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-23 12:11:13,662 [INFO] - Validation epoch stats:   Loss: 3.4645 - Binary-Cell-Dice: 0.7219 - Binary-Cell-Jacard: 0.6112 - bPQ-Score: 0.5053 - mPQ-Score: 0.3524 - Tissue-MC-Acc.: 0.0294
2023-09-23 12:11:13,670 [INFO] - New best model - save checkpoint
2023-09-23 12:11:38,766 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:11:38,767 [INFO] - Epoch: 18/130
2023-09-23 12:14:25,178 [INFO] - Training epoch stats:     Loss: 3.4546 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0258
2023-09-23 12:17:52,325 [INFO] - Validation epoch stats:   Loss: 3.4821 - Binary-Cell-Dice: 0.6509 - Binary-Cell-Jacard: 0.5241 - bPQ-Score: 0.3988 - mPQ-Score: 0.2711 - Tissue-MC-Acc.: 0.0335
2023-09-23 12:17:58,229 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:17:58,230 [INFO] - Epoch: 19/130
2023-09-23 12:20:42,235 [INFO] - Training epoch stats:     Loss: 3.4595 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0198
2023-09-23 12:24:03,680 [INFO] - Validation epoch stats:   Loss: 3.4771 - Binary-Cell-Dice: 0.7127 - Binary-Cell-Jacard: 0.6023 - bPQ-Score: 0.4891 - mPQ-Score: 0.3345 - Tissue-MC-Acc.: 0.0433
2023-09-23 12:24:10,721 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:24:10,721 [INFO] - Epoch: 20/130
2023-09-23 12:26:51,842 [INFO] - Training epoch stats:     Loss: 3.4450 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0194
2023-09-23 12:30:15,244 [INFO] - Validation epoch stats:   Loss: 3.4641 - Binary-Cell-Dice: 0.7175 - Binary-Cell-Jacard: 0.6106 - bPQ-Score: 0.5155 - mPQ-Score: 0.3458 - Tissue-MC-Acc.: 0.0301
2023-09-23 12:30:15,255 [INFO] - New best model - save checkpoint
2023-09-23 12:30:43,154 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:30:43,155 [INFO] - Epoch: 21/130
2023-09-23 12:33:30,034 [INFO] - Training epoch stats:     Loss: 3.4334 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-23 12:37:00,867 [INFO] - Validation epoch stats:   Loss: 3.4613 - Binary-Cell-Dice: 0.7315 - Binary-Cell-Jacard: 0.6224 - bPQ-Score: 0.5073 - mPQ-Score: 0.3398 - Tissue-MC-Acc.: 0.0215
2023-09-23 12:37:15,686 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:37:15,687 [INFO] - Epoch: 22/130
2023-09-23 12:40:04,316 [INFO] - Training epoch stats:     Loss: 3.4242 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0281
2023-09-23 12:43:42,648 [INFO] - Validation epoch stats:   Loss: 3.4337 - Binary-Cell-Dice: 0.7377 - Binary-Cell-Jacard: 0.6328 - bPQ-Score: 0.5130 - mPQ-Score: 0.3570 - Tissue-MC-Acc.: 0.0403
2023-09-23 12:43:55,989 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:43:55,989 [INFO] - Epoch: 23/130
2023-09-23 12:46:32,698 [INFO] - Training epoch stats:     Loss: 3.4252 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-23 12:50:17,164 [INFO] - Validation epoch stats:   Loss: 3.4356 - Binary-Cell-Dice: 0.7330 - Binary-Cell-Jacard: 0.6280 - bPQ-Score: 0.5135 - mPQ-Score: 0.3616 - Tissue-MC-Acc.: 0.0226
2023-09-23 12:50:23,200 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:50:23,201 [INFO] - Epoch: 24/130
2023-09-23 12:53:03,617 [INFO] - Training epoch stats:     Loss: 3.4144 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0194
2023-09-23 12:56:44,559 [INFO] - Validation epoch stats:   Loss: 3.4209 - Binary-Cell-Dice: 0.7094 - Binary-Cell-Jacard: 0.5968 - bPQ-Score: 0.4824 - mPQ-Score: 0.3364 - Tissue-MC-Acc.: 0.0384
2023-09-23 12:57:00,885 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:57:00,886 [INFO] - Epoch: 25/130
2023-09-23 12:59:38,220 [INFO] - Training epoch stats:     Loss: 3.4095 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-23 13:03:07,623 [INFO] - Validation epoch stats:   Loss: 3.4226 - Binary-Cell-Dice: 0.7251 - Binary-Cell-Jacard: 0.6166 - bPQ-Score: 0.5101 - mPQ-Score: 0.3497 - Tissue-MC-Acc.: 0.0275
2023-09-23 13:03:23,573 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:03:23,573 [INFO] - Epoch: 26/130
2023-09-23 13:06:10,830 [INFO] - Training epoch stats:     Loss: 3.4126 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0242
2023-09-23 13:09:35,298 [INFO] - Validation epoch stats:   Loss: 3.4113 - Binary-Cell-Dice: 0.7174 - Binary-Cell-Jacard: 0.6072 - bPQ-Score: 0.5071 - mPQ-Score: 0.3561 - Tissue-MC-Acc.: 0.0267
2023-09-23 13:09:41,233 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:09:41,234 [INFO] - Epoch: 27/130
2023-09-23 13:12:22,119 [INFO] - Training epoch stats:     Loss: 3.4171 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0246
2023-09-23 13:15:48,528 [INFO] - Validation epoch stats:   Loss: 3.4044 - Binary-Cell-Dice: 0.7299 - Binary-Cell-Jacard: 0.6231 - bPQ-Score: 0.5024 - mPQ-Score: 0.3653 - Tissue-MC-Acc.: 0.0358
2023-09-23 13:16:05,141 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:16:05,142 [INFO] - Epoch: 28/130
2023-09-23 13:18:50,411 [INFO] - Training epoch stats:     Loss: 3.4019 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0266
2023-09-23 13:22:15,510 [INFO] - Validation epoch stats:   Loss: 3.4164 - Binary-Cell-Dice: 0.7292 - Binary-Cell-Jacard: 0.6223 - bPQ-Score: 0.5160 - mPQ-Score: 0.3647 - Tissue-MC-Acc.: 0.0282
2023-09-23 13:22:15,516 [INFO] - New best model - save checkpoint
2023-09-23 13:22:40,229 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:22:40,229 [INFO] - Epoch: 29/130
2023-09-23 13:25:25,340 [INFO] - Training epoch stats:     Loss: 3.3822 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0238
2023-09-23 13:29:05,008 [INFO] - Validation epoch stats:   Loss: 3.4206 - Binary-Cell-Dice: 0.7472 - Binary-Cell-Jacard: 0.6446 - bPQ-Score: 0.5351 - mPQ-Score: 0.3616 - Tissue-MC-Acc.: 0.0264
2023-09-23 13:29:05,010 [INFO] - New best model - save checkpoint
2023-09-23 13:29:22,048 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:29:22,049 [INFO] - Epoch: 30/130
2023-09-23 13:32:00,541 [INFO] - Training epoch stats:     Loss: 3.3950 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0254
2023-09-23 13:35:38,629 [INFO] - Validation epoch stats:   Loss: 3.4201 - Binary-Cell-Dice: 0.7462 - Binary-Cell-Jacard: 0.6402 - bPQ-Score: 0.5282 - mPQ-Score: 0.3641 - Tissue-MC-Acc.: 0.0305
2023-09-23 13:35:52,777 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:35:52,778 [INFO] - Epoch: 31/130
2023-09-23 13:38:42,043 [INFO] - Training epoch stats:     Loss: 3.3716 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0182
2023-09-23 13:42:11,193 [INFO] - Validation epoch stats:   Loss: 3.4082 - Binary-Cell-Dice: 0.7442 - Binary-Cell-Jacard: 0.6400 - bPQ-Score: 0.5190 - mPQ-Score: 0.3714 - Tissue-MC-Acc.: 0.0233
2023-09-23 13:42:25,131 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:42:25,132 [INFO] - Epoch: 32/130
2023-09-23 13:45:13,676 [INFO] - Training epoch stats:     Loss: 3.3944 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0131
2023-09-23 13:48:38,984 [INFO] - Validation epoch stats:   Loss: 3.3790 - Binary-Cell-Dice: 0.7362 - Binary-Cell-Jacard: 0.6275 - bPQ-Score: 0.5179 - mPQ-Score: 0.3848 - Tissue-MC-Acc.: 0.0169
2023-09-23 13:48:54,304 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:48:54,304 [INFO] - Epoch: 33/130
2023-09-23 13:51:45,693 [INFO] - Training epoch stats:     Loss: 3.3701 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0186
2023-09-23 13:55:25,219 [INFO] - Validation epoch stats:   Loss: 3.4111 - Binary-Cell-Dice: 0.7561 - Binary-Cell-Jacard: 0.6534 - bPQ-Score: 0.5293 - mPQ-Score: 0.3819 - Tissue-MC-Acc.: 0.0230
2023-09-23 13:55:31,494 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:55:31,494 [INFO] - Epoch: 34/130
2023-09-23 13:58:15,224 [INFO] - Training epoch stats:     Loss: 3.3658 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-23 14:01:54,825 [INFO] - Validation epoch stats:   Loss: 3.3792 - Binary-Cell-Dice: 0.7245 - Binary-Cell-Jacard: 0.6108 - bPQ-Score: 0.4869 - mPQ-Score: 0.3600 - Tissue-MC-Acc.: 0.0305
2023-09-23 14:02:00,737 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:02:00,738 [INFO] - Epoch: 35/130
2023-09-23 14:04:39,058 [INFO] - Training epoch stats:     Loss: 3.3590 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0222
2023-09-23 14:08:20,669 [INFO] - Validation epoch stats:   Loss: 3.4143 - Binary-Cell-Dice: 0.7476 - Binary-Cell-Jacard: 0.6431 - bPQ-Score: 0.5185 - mPQ-Score: 0.3642 - Tissue-MC-Acc.: 0.0267
2023-09-23 14:08:36,687 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:08:36,687 [INFO] - Epoch: 36/130
2023-09-23 14:11:24,583 [INFO] - Training epoch stats:     Loss: 3.3473 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0226
2023-09-23 14:14:47,834 [INFO] - Validation epoch stats:   Loss: 3.4040 - Binary-Cell-Dice: 0.7436 - Binary-Cell-Jacard: 0.6432 - bPQ-Score: 0.5260 - mPQ-Score: 0.3806 - Tissue-MC-Acc.: 0.0177
2023-09-23 14:14:53,837 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:14:53,837 [INFO] - Epoch: 37/130
2023-09-23 14:17:35,306 [INFO] - Training epoch stats:     Loss: 3.3487 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0186
2023-09-23 14:21:06,689 [INFO] - Validation epoch stats:   Loss: 3.4242 - Binary-Cell-Dice: 0.7409 - Binary-Cell-Jacard: 0.6364 - bPQ-Score: 0.5299 - mPQ-Score: 0.3832 - Tissue-MC-Acc.: 0.0248
2023-09-23 14:21:12,615 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:21:12,616 [INFO] - Epoch: 38/130
2023-09-23 14:23:48,708 [INFO] - Training epoch stats:     Loss: 3.3458 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-23 14:27:35,178 [INFO] - Validation epoch stats:   Loss: 3.3878 - Binary-Cell-Dice: 0.7589 - Binary-Cell-Jacard: 0.6589 - bPQ-Score: 0.5398 - mPQ-Score: 0.3874 - Tissue-MC-Acc.: 0.0192
2023-09-23 14:27:35,188 [INFO] - New best model - save checkpoint
2023-09-23 14:28:01,300 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:28:01,300 [INFO] - Epoch: 39/130
2023-09-23 14:30:41,755 [INFO] - Training epoch stats:     Loss: 3.3448 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0155
2023-09-23 14:34:21,953 [INFO] - Validation epoch stats:   Loss: 3.3989 - Binary-Cell-Dice: 0.7381 - Binary-Cell-Jacard: 0.6330 - bPQ-Score: 0.4931 - mPQ-Score: 0.3532 - Tissue-MC-Acc.: 0.0226
2023-09-23 14:34:27,906 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:34:27,906 [INFO] - Epoch: 40/130
2023-09-23 14:37:07,604 [INFO] - Training epoch stats:     Loss: 3.3390 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0222
2023-09-23 14:40:44,845 [INFO] - Validation epoch stats:   Loss: 3.3884 - Binary-Cell-Dice: 0.7427 - Binary-Cell-Jacard: 0.6420 - bPQ-Score: 0.5327 - mPQ-Score: 0.3799 - Tissue-MC-Acc.: 0.0218
2023-09-23 14:40:50,802 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:40:50,803 [INFO] - Epoch: 41/130
2023-09-23 14:43:33,098 [INFO] - Training epoch stats:     Loss: 3.3293 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0186
2023-09-23 14:46:59,984 [INFO] - Validation epoch stats:   Loss: 3.3789 - Binary-Cell-Dice: 0.7337 - Binary-Cell-Jacard: 0.6327 - bPQ-Score: 0.5358 - mPQ-Score: 0.3903 - Tissue-MC-Acc.: 0.0256
2023-09-23 14:47:14,486 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:47:14,487 [INFO] - Epoch: 42/130
2023-09-23 14:49:57,402 [INFO] - Training epoch stats:     Loss: 3.3514 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0202
2023-09-23 14:53:28,172 [INFO] - Validation epoch stats:   Loss: 3.3681 - Binary-Cell-Dice: 0.7408 - Binary-Cell-Jacard: 0.6405 - bPQ-Score: 0.5334 - mPQ-Score: 0.3843 - Tissue-MC-Acc.: 0.0192
2023-09-23 14:53:34,126 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:53:34,127 [INFO] - Epoch: 43/130
2023-09-23 14:56:17,754 [INFO] - Training epoch stats:     Loss: 3.3262 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0147
2023-09-23 14:59:56,374 [INFO] - Validation epoch stats:   Loss: 3.3553 - Binary-Cell-Dice: 0.7621 - Binary-Cell-Jacard: 0.6662 - bPQ-Score: 0.5483 - mPQ-Score: 0.4021 - Tissue-MC-Acc.: 0.0192
2023-09-23 14:59:56,384 [INFO] - New best model - save checkpoint
2023-09-23 15:00:12,693 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:00:12,694 [INFO] - Epoch: 44/130
2023-09-23 15:02:56,509 [INFO] - Training epoch stats:     Loss: 3.3099 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-23 15:06:22,469 [INFO] - Validation epoch stats:   Loss: 3.3631 - Binary-Cell-Dice: 0.7291 - Binary-Cell-Jacard: 0.6223 - bPQ-Score: 0.5287 - mPQ-Score: 0.3849 - Tissue-MC-Acc.: 0.0301
2023-09-23 15:06:30,676 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:06:30,677 [INFO] - Epoch: 45/130
2023-09-23 15:09:12,658 [INFO] - Training epoch stats:     Loss: 3.3305 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0163
2023-09-23 15:12:50,074 [INFO] - Validation epoch stats:   Loss: 3.3677 - Binary-Cell-Dice: 0.7454 - Binary-Cell-Jacard: 0.6445 - bPQ-Score: 0.5361 - mPQ-Score: 0.3845 - Tissue-MC-Acc.: 0.0215
2023-09-23 15:13:16,477 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:13:16,477 [INFO] - Epoch: 46/130
2023-09-23 15:15:57,167 [INFO] - Training epoch stats:     Loss: 3.3173 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0174
2023-09-23 15:19:24,500 [INFO] - Validation epoch stats:   Loss: 3.3429 - Binary-Cell-Dice: 0.7450 - Binary-Cell-Jacard: 0.6452 - bPQ-Score: 0.5388 - mPQ-Score: 0.3909 - Tissue-MC-Acc.: 0.0282
2023-09-23 15:19:44,321 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:19:44,322 [INFO] - Epoch: 47/130
2023-09-23 15:22:30,196 [INFO] - Training epoch stats:     Loss: 3.2991 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0210
2023-09-23 15:26:07,306 [INFO] - Validation epoch stats:   Loss: 3.3483 - Binary-Cell-Dice: 0.7575 - Binary-Cell-Jacard: 0.6594 - bPQ-Score: 0.5545 - mPQ-Score: 0.3908 - Tissue-MC-Acc.: 0.0271
2023-09-23 15:26:07,308 [INFO] - New best model - save checkpoint
2023-09-23 15:26:19,250 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:26:19,250 [INFO] - Epoch: 48/130
2023-09-23 15:29:02,461 [INFO] - Training epoch stats:     Loss: 3.3055 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0206
2023-09-23 15:32:39,346 [INFO] - Validation epoch stats:   Loss: 3.3474 - Binary-Cell-Dice: 0.7481 - Binary-Cell-Jacard: 0.6511 - bPQ-Score: 0.5445 - mPQ-Score: 0.3850 - Tissue-MC-Acc.: 0.0328
2023-09-23 15:32:45,262 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:32:45,263 [INFO] - Epoch: 49/130
2023-09-23 15:35:28,263 [INFO] - Training epoch stats:     Loss: 3.3007 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-23 15:39:02,277 [INFO] - Validation epoch stats:   Loss: 3.3467 - Binary-Cell-Dice: 0.7599 - Binary-Cell-Jacard: 0.6625 - bPQ-Score: 0.5479 - mPQ-Score: 0.3977 - Tissue-MC-Acc.: 0.0200
2023-09-23 15:39:15,193 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:39:15,193 [INFO] - Epoch: 50/130
2023-09-23 15:41:59,182 [INFO] - Training epoch stats:     Loss: 3.2947 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-23 15:45:40,729 [INFO] - Validation epoch stats:   Loss: 3.3589 - Binary-Cell-Dice: 0.7525 - Binary-Cell-Jacard: 0.6551 - bPQ-Score: 0.5555 - mPQ-Score: 0.4007 - Tissue-MC-Acc.: 0.0237
2023-09-23 15:45:40,768 [INFO] - New best model - save checkpoint
2023-09-23 15:45:56,002 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:45:56,002 [INFO] - Epoch: 51/130
2023-09-23 15:48:39,323 [INFO] - Training epoch stats:     Loss: 3.3018 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-23 15:52:14,694 [INFO] - Validation epoch stats:   Loss: 3.3460 - Binary-Cell-Dice: 0.7559 - Binary-Cell-Jacard: 0.6595 - bPQ-Score: 0.5535 - mPQ-Score: 0.4014 - Tissue-MC-Acc.: 0.0576
2023-09-23 15:52:29,380 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:52:29,380 [INFO] - Epoch: 52/130
2023-09-23 15:55:14,176 [INFO] - Training epoch stats:     Loss: 3.2778 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-23 15:58:42,369 [INFO] - Validation epoch stats:   Loss: 3.3576 - Binary-Cell-Dice: 0.7474 - Binary-Cell-Jacard: 0.6484 - bPQ-Score: 0.5511 - mPQ-Score: 0.3920 - Tissue-MC-Acc.: 0.0388
2023-09-23 15:58:54,444 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:58:54,445 [INFO] - Epoch: 53/130
2023-09-23 16:01:37,421 [INFO] - Training epoch stats:     Loss: 3.2845 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0289
2023-09-23 16:05:20,836 [INFO] - Validation epoch stats:   Loss: 3.3497 - Binary-Cell-Dice: 0.7599 - Binary-Cell-Jacard: 0.6652 - bPQ-Score: 0.5545 - mPQ-Score: 0.4073 - Tissue-MC-Acc.: 0.0290
2023-09-23 16:05:26,968 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:05:26,969 [INFO] - Epoch: 54/130
2023-09-23 16:08:10,158 [INFO] - Training epoch stats:     Loss: 3.2817 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0293
2023-09-23 16:11:44,758 [INFO] - Validation epoch stats:   Loss: 3.3397 - Binary-Cell-Dice: 0.7351 - Binary-Cell-Jacard: 0.6318 - bPQ-Score: 0.5293 - mPQ-Score: 0.3852 - Tissue-MC-Acc.: 0.0312
2023-09-23 16:11:59,322 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:11:59,322 [INFO] - Epoch: 55/130
2023-09-23 16:14:45,824 [INFO] - Training epoch stats:     Loss: 3.2894 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0285
2023-09-23 16:18:26,818 [INFO] - Validation epoch stats:   Loss: 3.3336 - Binary-Cell-Dice: 0.7551 - Binary-Cell-Jacard: 0.6578 - bPQ-Score: 0.5583 - mPQ-Score: 0.4076 - Tissue-MC-Acc.: 0.0297
2023-09-23 16:18:26,895 [INFO] - New best model - save checkpoint
2023-09-23 16:18:46,562 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:18:46,562 [INFO] - Epoch: 56/130
2023-09-23 16:21:30,377 [INFO] - Training epoch stats:     Loss: 3.2788 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0345
2023-09-23 16:25:09,810 [INFO] - Validation epoch stats:   Loss: 3.3336 - Binary-Cell-Dice: 0.7550 - Binary-Cell-Jacard: 0.6579 - bPQ-Score: 0.5592 - mPQ-Score: 0.4064 - Tissue-MC-Acc.: 0.0354
2023-09-23 16:25:09,817 [INFO] - New best model - save checkpoint
2023-09-23 16:25:47,981 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:25:47,982 [INFO] - Epoch: 57/130
2023-09-23 16:28:29,527 [INFO] - Training epoch stats:     Loss: 3.2809 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-23 16:32:06,590 [INFO] - Validation epoch stats:   Loss: 3.3216 - Binary-Cell-Dice: 0.7541 - Binary-Cell-Jacard: 0.6564 - bPQ-Score: 0.5452 - mPQ-Score: 0.3978 - Tissue-MC-Acc.: 0.0557
2023-09-23 16:32:27,945 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:32:27,946 [INFO] - Epoch: 58/130
2023-09-23 16:35:10,916 [INFO] - Training epoch stats:     Loss: 3.2771 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0503
2023-09-23 16:38:43,697 [INFO] - Validation epoch stats:   Loss: 3.3385 - Binary-Cell-Dice: 0.7568 - Binary-Cell-Jacard: 0.6598 - bPQ-Score: 0.5540 - mPQ-Score: 0.4013 - Tissue-MC-Acc.: 0.0350
2023-09-23 16:38:49,634 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:38:49,635 [INFO] - Epoch: 59/130
2023-09-23 16:41:34,853 [INFO] - Training epoch stats:     Loss: 3.2821 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-23 16:45:11,062 [INFO] - Validation epoch stats:   Loss: 3.3541 - Binary-Cell-Dice: 0.7593 - Binary-Cell-Jacard: 0.6627 - bPQ-Score: 0.5551 - mPQ-Score: 0.4033 - Tissue-MC-Acc.: 0.0218
2023-09-23 16:45:31,414 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:45:31,415 [INFO] - Epoch: 60/130
2023-09-23 16:48:15,942 [INFO] - Training epoch stats:     Loss: 3.2679 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0377
2023-09-23 16:51:48,608 [INFO] - Validation epoch stats:   Loss: 3.3251 - Binary-Cell-Dice: 0.7531 - Binary-Cell-Jacard: 0.6575 - bPQ-Score: 0.5614 - mPQ-Score: 0.4021 - Tissue-MC-Acc.: 0.0267
2023-09-23 16:51:48,610 [INFO] - New best model - save checkpoint
2023-09-23 16:52:00,501 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:52:00,502 [INFO] - Epoch: 61/130
2023-09-23 16:54:45,110 [INFO] - Training epoch stats:     Loss: 3.2621 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0313
2023-09-23 16:58:15,924 [INFO] - Validation epoch stats:   Loss: 3.3269 - Binary-Cell-Dice: 0.7500 - Binary-Cell-Jacard: 0.6514 - bPQ-Score: 0.5531 - mPQ-Score: 0.4053 - Tissue-MC-Acc.: 0.0233
2023-09-23 16:58:22,053 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:58:22,054 [INFO] - Epoch: 62/130
2023-09-23 17:01:04,864 [INFO] - Training epoch stats:     Loss: 3.2833 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-23 17:04:42,107 [INFO] - Validation epoch stats:   Loss: 3.3905 - Binary-Cell-Dice: 0.7635 - Binary-Cell-Jacard: 0.6673 - bPQ-Score: 0.5621 - mPQ-Score: 0.3674 - Tissue-MC-Acc.: 0.0395
2023-09-23 17:04:42,147 [INFO] - New best model - save checkpoint
2023-09-23 17:04:58,731 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:04:58,731 [INFO] - Epoch: 63/130
2023-09-23 17:07:42,690 [INFO] - Training epoch stats:     Loss: 3.2649 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0420
2023-09-23 17:11:14,116 [INFO] - Validation epoch stats:   Loss: 3.3219 - Binary-Cell-Dice: 0.7545 - Binary-Cell-Jacard: 0.6569 - bPQ-Score: 0.5580 - mPQ-Score: 0.4074 - Tissue-MC-Acc.: 0.0602
2023-09-23 17:11:29,215 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:11:29,216 [INFO] - Epoch: 64/130
2023-09-23 17:14:12,782 [INFO] - Training epoch stats:     Loss: 3.2570 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0634
2023-09-23 17:17:54,965 [INFO] - Validation epoch stats:   Loss: 3.3066 - Binary-Cell-Dice: 0.7627 - Binary-Cell-Jacard: 0.6710 - bPQ-Score: 0.5557 - mPQ-Score: 0.4170 - Tissue-MC-Acc.: 0.0328
2023-09-23 17:18:06,668 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:18:06,669 [INFO] - Epoch: 65/130
2023-09-23 17:20:47,957 [INFO] - Training epoch stats:     Loss: 3.2635 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-23 17:24:24,373 [INFO] - Validation epoch stats:   Loss: 3.3195 - Binary-Cell-Dice: 0.7627 - Binary-Cell-Jacard: 0.6662 - bPQ-Score: 0.5635 - mPQ-Score: 0.4171 - Tissue-MC-Acc.: 0.0392
2023-09-23 17:24:24,401 [INFO] - New best model - save checkpoint
2023-09-23 17:24:40,282 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:24:40,283 [INFO] - Epoch: 66/130
2023-09-23 17:27:18,688 [INFO] - Training epoch stats:     Loss: 3.2712 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0495
2023-09-23 17:31:05,484 [INFO] - Validation epoch stats:   Loss: 3.3210 - Binary-Cell-Dice: 0.7562 - Binary-Cell-Jacard: 0.6595 - bPQ-Score: 0.5546 - mPQ-Score: 0.4147 - Tissue-MC-Acc.: 0.0312
2023-09-23 17:31:22,929 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:31:22,930 [INFO] - Epoch: 67/130
2023-09-23 17:34:04,277 [INFO] - Training epoch stats:     Loss: 3.2733 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0361
2023-09-23 17:37:41,864 [INFO] - Validation epoch stats:   Loss: 3.3443 - Binary-Cell-Dice: 0.7446 - Binary-Cell-Jacard: 0.6461 - bPQ-Score: 0.5448 - mPQ-Score: 0.3948 - Tissue-MC-Acc.: 0.0271
2023-09-23 17:37:57,478 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:37:57,479 [INFO] - Epoch: 68/130
2023-09-23 17:40:39,809 [INFO] - Training epoch stats:     Loss: 3.2558 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0349
2023-09-23 17:44:11,518 [INFO] - Validation epoch stats:   Loss: 3.3139 - Binary-Cell-Dice: 0.7588 - Binary-Cell-Jacard: 0.6622 - bPQ-Score: 0.5616 - mPQ-Score: 0.4096 - Tissue-MC-Acc.: 0.0245
2023-09-23 17:44:17,435 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:44:17,436 [INFO] - Epoch: 69/130
2023-09-23 17:46:58,660 [INFO] - Training epoch stats:     Loss: 3.2417 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0341
2023-09-23 17:50:31,520 [INFO] - Validation epoch stats:   Loss: 3.3262 - Binary-Cell-Dice: 0.7546 - Binary-Cell-Jacard: 0.6587 - bPQ-Score: 0.5658 - mPQ-Score: 0.4035 - Tissue-MC-Acc.: 0.0527
2023-09-23 17:50:31,523 [INFO] - New best model - save checkpoint
2023-09-23 17:50:43,489 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:50:43,489 [INFO] - Epoch: 70/130
2023-09-23 17:53:22,755 [INFO] - Training epoch stats:     Loss: 3.2508 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-23 17:57:02,287 [INFO] - Validation epoch stats:   Loss: 3.3415 - Binary-Cell-Dice: 0.7697 - Binary-Cell-Jacard: 0.6753 - bPQ-Score: 0.5560 - mPQ-Score: 0.4135 - Tissue-MC-Acc.: 0.0407
2023-09-23 17:57:15,191 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:57:15,192 [INFO] - Epoch: 71/130
2023-09-23 18:00:14,892 [INFO] - Training epoch stats:     Loss: 3.2454 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-23 18:03:46,244 [INFO] - Validation epoch stats:   Loss: 3.3270 - Binary-Cell-Dice: 0.7468 - Binary-Cell-Jacard: 0.6496 - bPQ-Score: 0.5528 - mPQ-Score: 0.3994 - Tissue-MC-Acc.: 0.0294
2023-09-23 18:03:54,806 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:03:54,806 [INFO] - Epoch: 72/130
2023-09-23 18:06:38,245 [INFO] - Training epoch stats:     Loss: 3.2482 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0349
2023-09-23 18:10:20,538 [INFO] - Validation epoch stats:   Loss: 3.3134 - Binary-Cell-Dice: 0.7488 - Binary-Cell-Jacard: 0.6495 - bPQ-Score: 0.5543 - mPQ-Score: 0.4083 - Tissue-MC-Acc.: 0.0275
2023-09-23 18:10:42,606 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:10:42,607 [INFO] - Epoch: 73/130
2023-09-23 18:13:35,344 [INFO] - Training epoch stats:     Loss: 3.2533 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0234
2023-09-23 18:17:07,635 [INFO] - Validation epoch stats:   Loss: 3.3040 - Binary-Cell-Dice: 0.7543 - Binary-Cell-Jacard: 0.6559 - bPQ-Score: 0.5532 - mPQ-Score: 0.4078 - Tissue-MC-Acc.: 0.0328
2023-09-23 18:17:13,582 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:17:13,582 [INFO] - Epoch: 74/130
2023-09-23 18:19:58,322 [INFO] - Training epoch stats:     Loss: 3.2460 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0341
2023-09-23 18:23:31,613 [INFO] - Validation epoch stats:   Loss: 3.3318 - Binary-Cell-Dice: 0.7535 - Binary-Cell-Jacard: 0.6562 - bPQ-Score: 0.5481 - mPQ-Score: 0.4011 - Tissue-MC-Acc.: 0.0267
2023-09-23 18:23:37,564 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:23:37,564 [INFO] - Epoch: 75/130
2023-09-23 18:26:24,966 [INFO] - Training epoch stats:     Loss: 3.2396 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0337
2023-09-23 18:29:57,863 [INFO] - Validation epoch stats:   Loss: 3.3352 - Binary-Cell-Dice: 0.7585 - Binary-Cell-Jacard: 0.6633 - bPQ-Score: 0.5651 - mPQ-Score: 0.4113 - Tissue-MC-Acc.: 0.0267
2023-09-23 18:30:18,388 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:30:18,388 [INFO] - Epoch: 76/130
2023-09-23 18:33:06,734 [INFO] - Training epoch stats:     Loss: 3.2479 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0337
2023-09-23 18:36:28,119 [INFO] - Validation epoch stats:   Loss: 3.3457 - Binary-Cell-Dice: 0.7455 - Binary-Cell-Jacard: 0.6504 - bPQ-Score: 0.5582 - mPQ-Score: 0.3954 - Tissue-MC-Acc.: 0.0395
2023-09-23 18:36:34,089 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:36:34,090 [INFO] - Epoch: 77/130
2023-09-23 18:39:13,466 [INFO] - Training epoch stats:     Loss: 3.2405 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-23 18:42:53,773 [INFO] - Validation epoch stats:   Loss: 3.3165 - Binary-Cell-Dice: 0.7618 - Binary-Cell-Jacard: 0.6696 - bPQ-Score: 0.5695 - mPQ-Score: 0.4167 - Tissue-MC-Acc.: 0.0335
2023-09-23 18:42:53,775 [INFO] - New best model - save checkpoint
2023-09-23 18:43:05,694 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:43:05,694 [INFO] - Epoch: 78/130
2023-09-23 18:45:45,785 [INFO] - Training epoch stats:     Loss: 3.2526 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-23 18:49:14,428 [INFO] - Validation epoch stats:   Loss: 3.3038 - Binary-Cell-Dice: 0.7649 - Binary-Cell-Jacard: 0.6724 - bPQ-Score: 0.5734 - mPQ-Score: 0.4147 - Tissue-MC-Acc.: 0.0248
2023-09-23 18:49:14,431 [INFO] - New best model - save checkpoint
2023-09-23 18:49:26,346 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:49:26,346 [INFO] - Epoch: 79/130
2023-09-23 18:52:08,844 [INFO] - Training epoch stats:     Loss: 3.2134 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-23 18:55:53,298 [INFO] - Validation epoch stats:   Loss: 3.3057 - Binary-Cell-Dice: 0.7693 - Binary-Cell-Jacard: 0.6775 - bPQ-Score: 0.5710 - mPQ-Score: 0.4177 - Tissue-MC-Acc.: 0.0271
2023-09-23 18:55:58,683 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 18:55:58,683 [INFO] - Epoch: 80/130
2023-09-23 18:58:40,467 [INFO] - Training epoch stats:     Loss: 3.2230 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0341
2023-09-23 19:02:22,469 [INFO] - Validation epoch stats:   Loss: 3.3711 - Binary-Cell-Dice: 0.7624 - Binary-Cell-Jacard: 0.6660 - bPQ-Score: 0.5439 - mPQ-Score: 0.4020 - Tissue-MC-Acc.: 0.0267
2023-09-23 19:02:42,048 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:02:42,049 [INFO] - Epoch: 81/130
2023-09-23 19:05:30,239 [INFO] - Training epoch stats:     Loss: 3.2459 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0329
2023-09-23 19:09:07,078 [INFO] - Validation epoch stats:   Loss: 3.3172 - Binary-Cell-Dice: 0.7616 - Binary-Cell-Jacard: 0.6677 - bPQ-Score: 0.5525 - mPQ-Score: 0.4098 - Tissue-MC-Acc.: 0.0377
2023-09-23 19:09:12,994 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:09:12,995 [INFO] - Epoch: 82/130
2023-09-23 19:11:53,253 [INFO] - Training epoch stats:     Loss: 3.2444 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0321
2023-09-23 19:15:40,817 [INFO] - Validation epoch stats:   Loss: 3.2990 - Binary-Cell-Dice: 0.7621 - Binary-Cell-Jacard: 0.6692 - bPQ-Score: 0.5676 - mPQ-Score: 0.4188 - Tissue-MC-Acc.: 0.0237
2023-09-23 19:16:09,939 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:16:09,940 [INFO] - Epoch: 83/130
2023-09-23 19:19:00,038 [INFO] - Training epoch stats:     Loss: 3.2278 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0325
2023-09-23 19:22:42,560 [INFO] - Validation epoch stats:   Loss: 3.2832 - Binary-Cell-Dice: 0.7630 - Binary-Cell-Jacard: 0.6701 - bPQ-Score: 0.5668 - mPQ-Score: 0.4248 - Tissue-MC-Acc.: 0.0271
2023-09-23 19:23:11,172 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:23:11,173 [INFO] - Epoch: 84/130
2023-09-23 19:25:55,815 [INFO] - Training epoch stats:     Loss: 3.2211 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 19:29:40,385 [INFO] - Validation epoch stats:   Loss: 3.3150 - Binary-Cell-Dice: 0.7618 - Binary-Cell-Jacard: 0.6699 - bPQ-Score: 0.5645 - mPQ-Score: 0.4147 - Tissue-MC-Acc.: 0.0237
2023-09-23 19:29:49,776 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:29:49,777 [INFO] - Epoch: 85/130
2023-09-23 19:32:31,263 [INFO] - Training epoch stats:     Loss: 3.2391 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-23 19:36:24,865 [INFO] - Validation epoch stats:   Loss: 3.3034 - Binary-Cell-Dice: 0.7702 - Binary-Cell-Jacard: 0.6790 - bPQ-Score: 0.5629 - mPQ-Score: 0.4243 - Tissue-MC-Acc.: 0.0286
2023-09-23 19:36:50,754 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:36:50,755 [INFO] - Epoch: 86/130
2023-09-23 19:39:33,084 [INFO] - Training epoch stats:     Loss: 3.2288 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0333
2023-09-23 19:43:12,801 [INFO] - Validation epoch stats:   Loss: 3.3066 - Binary-Cell-Dice: 0.7582 - Binary-Cell-Jacard: 0.6624 - bPQ-Score: 0.5619 - mPQ-Score: 0.4214 - Tissue-MC-Acc.: 0.0237
2023-09-23 19:43:24,131 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:43:24,131 [INFO] - Epoch: 87/130
2023-09-23 19:46:14,491 [INFO] - Training epoch stats:     Loss: 3.2225 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0384
2023-09-23 19:49:56,965 [INFO] - Validation epoch stats:   Loss: 3.3319 - Binary-Cell-Dice: 0.7580 - Binary-Cell-Jacard: 0.6612 - bPQ-Score: 0.5376 - mPQ-Score: 0.4059 - Tissue-MC-Acc.: 0.0271
2023-09-23 19:50:29,303 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:50:29,304 [INFO] - Epoch: 88/130
2023-09-23 19:53:19,562 [INFO] - Training epoch stats:     Loss: 3.2265 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 19:57:02,663 [INFO] - Validation epoch stats:   Loss: 3.2936 - Binary-Cell-Dice: 0.7657 - Binary-Cell-Jacard: 0.6734 - bPQ-Score: 0.5703 - mPQ-Score: 0.4279 - Tissue-MC-Acc.: 0.0301
2023-09-23 19:57:30,658 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 19:57:30,658 [INFO] - Epoch: 89/130
2023-09-23 20:00:20,730 [INFO] - Training epoch stats:     Loss: 3.2338 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0353
2023-09-23 20:04:23,986 [INFO] - Validation epoch stats:   Loss: 3.2975 - Binary-Cell-Dice: 0.7674 - Binary-Cell-Jacard: 0.6753 - bPQ-Score: 0.5684 - mPQ-Score: 0.4271 - Tissue-MC-Acc.: 0.0226
2023-09-23 20:04:31,719 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 20:04:31,720 [INFO] - Epoch: 90/130
2023-09-23 20:07:11,520 [INFO] - Training epoch stats:     Loss: 3.2060 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-23 20:11:08,974 [INFO] - Validation epoch stats:   Loss: 3.2972 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6692 - bPQ-Score: 0.5560 - mPQ-Score: 0.4143 - Tissue-MC-Acc.: 0.0312
2023-09-23 20:11:35,861 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 20:11:35,862 [INFO] - Epoch: 91/130
2023-09-23 20:14:26,951 [INFO] - Training epoch stats:     Loss: 3.2091 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0396
2023-09-23 20:18:09,881 [INFO] - Validation epoch stats:   Loss: 3.3164 - Binary-Cell-Dice: 0.7639 - Binary-Cell-Jacard: 0.6697 - bPQ-Score: 0.5627 - mPQ-Score: 0.4232 - Tissue-MC-Acc.: 0.0252
2023-09-23 20:18:19,616 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 20:18:19,616 [INFO] - Epoch: 92/130
2023-09-23 20:21:03,390 [INFO] - Training epoch stats:     Loss: 3.2174 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-23 20:24:47,827 [INFO] - Validation epoch stats:   Loss: 3.2959 - Binary-Cell-Dice: 0.7602 - Binary-Cell-Jacard: 0.6661 - bPQ-Score: 0.5651 - mPQ-Score: 0.4160 - Tissue-MC-Acc.: 0.0328
2023-09-23 20:25:10,678 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 20:25:10,679 [INFO] - Epoch: 93/130
2023-09-23 20:28:02,025 [INFO] - Training epoch stats:     Loss: 3.2243 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-23 20:31:59,731 [INFO] - Validation epoch stats:   Loss: 3.2922 - Binary-Cell-Dice: 0.7714 - Binary-Cell-Jacard: 0.6792 - bPQ-Score: 0.5648 - mPQ-Score: 0.4226 - Tissue-MC-Acc.: 0.0309
2023-09-23 20:32:08,436 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 20:32:08,437 [INFO] - Epoch: 94/130
2023-09-23 20:34:54,597 [INFO] - Training epoch stats:     Loss: 3.2062 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-23 20:38:42,133 [INFO] - Validation epoch stats:   Loss: 3.3235 - Binary-Cell-Dice: 0.7585 - Binary-Cell-Jacard: 0.6625 - bPQ-Score: 0.5537 - mPQ-Score: 0.4040 - Tissue-MC-Acc.: 0.0512
2023-09-23 20:38:54,310 [DEBUG] - Old lr: 0.000100 - New lr: 0.000050
2023-09-23 20:38:54,311 [INFO] - Epoch: 95/130
2023-09-23 20:41:40,826 [INFO] - Training epoch stats:     Loss: 3.1923 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0464
2023-09-23 20:45:31,466 [INFO] - Validation epoch stats:   Loss: 3.2778 - Binary-Cell-Dice: 0.7697 - Binary-Cell-Jacard: 0.6777 - bPQ-Score: 0.5735 - mPQ-Score: 0.4351 - Tissue-MC-Acc.: 0.0403
2023-09-23 20:45:31,570 [INFO] - New best model - save checkpoint
2023-09-23 20:46:08,803 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 20:46:08,803 [INFO] - Epoch: 96/130
2023-09-23 20:48:56,613 [INFO] - Training epoch stats:     Loss: 3.1715 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0420
2023-09-23 20:52:47,629 [INFO] - Validation epoch stats:   Loss: 3.2795 - Binary-Cell-Dice: 0.7688 - Binary-Cell-Jacard: 0.6771 - bPQ-Score: 0.5699 - mPQ-Score: 0.4300 - Tissue-MC-Acc.: 0.0399
2023-09-23 20:52:54,845 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 20:52:54,846 [INFO] - Epoch: 97/130
2023-09-23 20:55:39,124 [INFO] - Training epoch stats:     Loss: 3.1749 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-23 20:59:32,598 [INFO] - Validation epoch stats:   Loss: 3.2738 - Binary-Cell-Dice: 0.7638 - Binary-Cell-Jacard: 0.6717 - bPQ-Score: 0.5740 - mPQ-Score: 0.4235 - Tissue-MC-Acc.: 0.0384
2023-09-23 20:59:32,626 [INFO] - New best model - save checkpoint
2023-09-23 21:00:01,369 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:00:01,369 [INFO] - Epoch: 98/130
2023-09-23 21:02:46,282 [INFO] - Training epoch stats:     Loss: 3.1742 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-23 21:06:33,603 [INFO] - Validation epoch stats:   Loss: 3.2793 - Binary-Cell-Dice: 0.7627 - Binary-Cell-Jacard: 0.6684 - bPQ-Score: 0.5693 - mPQ-Score: 0.4241 - Tissue-MC-Acc.: 0.0335
2023-09-23 21:06:46,916 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:06:46,916 [INFO] - Epoch: 99/130
2023-09-23 21:09:37,874 [INFO] - Training epoch stats:     Loss: 3.1650 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0384
2023-09-23 21:13:37,502 [INFO] - Validation epoch stats:   Loss: 3.2686 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6760 - bPQ-Score: 0.5789 - mPQ-Score: 0.4317 - Tissue-MC-Acc.: 0.0343
2023-09-23 21:13:37,537 [INFO] - New best model - save checkpoint
2023-09-23 21:13:56,100 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:13:56,101 [INFO] - Epoch: 100/130
2023-09-23 21:16:36,433 [INFO] - Training epoch stats:     Loss: 3.1721 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0440
2023-09-23 21:20:25,519 [INFO] - Validation epoch stats:   Loss: 3.2703 - Binary-Cell-Dice: 0.7731 - Binary-Cell-Jacard: 0.6813 - bPQ-Score: 0.5818 - mPQ-Score: 0.4382 - Tissue-MC-Acc.: 0.0324
2023-09-23 21:20:25,771 [INFO] - New best model - save checkpoint
2023-09-23 21:21:07,123 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:21:07,124 [INFO] - Epoch: 101/130
2023-09-23 21:23:45,917 [INFO] - Training epoch stats:     Loss: 3.1538 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-23 21:27:39,549 [INFO] - Validation epoch stats:   Loss: 3.2737 - Binary-Cell-Dice: 0.7671 - Binary-Cell-Jacard: 0.6742 - bPQ-Score: 0.5761 - mPQ-Score: 0.4355 - Tissue-MC-Acc.: 0.0312
2023-09-23 21:27:48,444 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:27:48,444 [INFO] - Epoch: 102/130
2023-09-23 21:30:35,117 [INFO] - Training epoch stats:     Loss: 3.1494 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0373
2023-09-23 21:34:30,763 [INFO] - Validation epoch stats:   Loss: 3.2733 - Binary-Cell-Dice: 0.7722 - Binary-Cell-Jacard: 0.6822 - bPQ-Score: 0.5801 - mPQ-Score: 0.4355 - Tissue-MC-Acc.: 0.0245
2023-09-23 21:35:00,123 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:35:00,124 [INFO] - Epoch: 103/130
2023-09-23 21:38:00,985 [INFO] - Training epoch stats:     Loss: 3.1636 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0345
2023-09-23 21:41:52,551 [INFO] - Validation epoch stats:   Loss: 3.2636 - Binary-Cell-Dice: 0.7707 - Binary-Cell-Jacard: 0.6806 - bPQ-Score: 0.5747 - mPQ-Score: 0.4368 - Tissue-MC-Acc.: 0.0316
2023-09-23 21:42:03,434 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:42:03,435 [INFO] - Epoch: 104/130
2023-09-23 21:44:46,116 [INFO] - Training epoch stats:     Loss: 3.1602 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-23 21:48:41,842 [INFO] - Validation epoch stats:   Loss: 3.2588 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6801 - bPQ-Score: 0.5737 - mPQ-Score: 0.4339 - Tissue-MC-Acc.: 0.0294
2023-09-23 21:49:00,153 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:49:00,154 [INFO] - Epoch: 105/130
2023-09-23 21:51:48,281 [INFO] - Training epoch stats:     Loss: 3.1522 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0353
2023-09-23 21:55:38,764 [INFO] - Validation epoch stats:   Loss: 3.2828 - Binary-Cell-Dice: 0.7637 - Binary-Cell-Jacard: 0.6692 - bPQ-Score: 0.5765 - mPQ-Score: 0.4303 - Tissue-MC-Acc.: 0.0260
2023-09-23 21:55:48,470 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 21:55:48,471 [INFO] - Epoch: 106/130
2023-09-23 21:58:36,507 [INFO] - Training epoch stats:     Loss: 3.1487 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0428
2023-09-23 22:02:29,600 [INFO] - Validation epoch stats:   Loss: 3.2721 - Binary-Cell-Dice: 0.7630 - Binary-Cell-Jacard: 0.6713 - bPQ-Score: 0.5743 - mPQ-Score: 0.4286 - Tissue-MC-Acc.: 0.0264
2023-09-23 22:02:39,055 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:02:39,056 [INFO] - Epoch: 107/130
2023-09-23 22:05:24,521 [INFO] - Training epoch stats:     Loss: 3.1577 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-23 22:09:11,466 [INFO] - Validation epoch stats:   Loss: 3.2634 - Binary-Cell-Dice: 0.7679 - Binary-Cell-Jacard: 0.6774 - bPQ-Score: 0.5818 - mPQ-Score: 0.4361 - Tissue-MC-Acc.: 0.0233
2023-09-23 22:09:44,014 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:09:44,015 [INFO] - Epoch: 108/130
2023-09-23 22:12:34,040 [INFO] - Training epoch stats:     Loss: 3.1536 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0353
2023-09-23 22:16:24,688 [INFO] - Validation epoch stats:   Loss: 3.2646 - Binary-Cell-Dice: 0.7717 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.5775 - mPQ-Score: 0.4366 - Tissue-MC-Acc.: 0.0309
2023-09-23 22:16:36,322 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:16:36,322 [INFO] - Epoch: 109/130
2023-09-23 22:19:21,477 [INFO] - Training epoch stats:     Loss: 3.1626 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-23 22:23:16,041 [INFO] - Validation epoch stats:   Loss: 3.2701 - Binary-Cell-Dice: 0.7740 - Binary-Cell-Jacard: 0.6830 - bPQ-Score: 0.5772 - mPQ-Score: 0.4349 - Tissue-MC-Acc.: 0.0282
2023-09-23 22:23:28,821 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:23:28,822 [INFO] - Epoch: 110/130
2023-09-23 22:26:16,683 [INFO] - Training epoch stats:     Loss: 3.1487 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-23 22:30:15,283 [INFO] - Validation epoch stats:   Loss: 3.2706 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6770 - bPQ-Score: 0.5711 - mPQ-Score: 0.4315 - Tissue-MC-Acc.: 0.0279
2023-09-23 22:30:24,390 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:30:24,390 [INFO] - Epoch: 111/130
2023-09-23 22:33:11,883 [INFO] - Training epoch stats:     Loss: 3.1393 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0377
2023-09-23 22:36:58,911 [INFO] - Validation epoch stats:   Loss: 3.2668 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6762 - bPQ-Score: 0.5774 - mPQ-Score: 0.4338 - Tissue-MC-Acc.: 0.0316
2023-09-23 22:37:07,969 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:37:07,969 [INFO] - Epoch: 112/130
2023-09-23 22:39:51,656 [INFO] - Training epoch stats:     Loss: 3.1482 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0432
2023-09-23 22:43:38,884 [INFO] - Validation epoch stats:   Loss: 3.2625 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6766 - bPQ-Score: 0.5750 - mPQ-Score: 0.4392 - Tissue-MC-Acc.: 0.0256
2023-09-23 22:44:03,070 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:44:03,070 [INFO] - Epoch: 113/130
2023-09-23 22:46:45,834 [INFO] - Training epoch stats:     Loss: 3.1529 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-23 22:50:39,095 [INFO] - Validation epoch stats:   Loss: 3.2712 - Binary-Cell-Dice: 0.7719 - Binary-Cell-Jacard: 0.6810 - bPQ-Score: 0.5805 - mPQ-Score: 0.4361 - Tissue-MC-Acc.: 0.0297
2023-09-23 22:50:46,997 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:50:46,998 [INFO] - Epoch: 114/130
2023-09-23 22:53:34,655 [INFO] - Training epoch stats:     Loss: 3.1506 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-23 22:57:25,715 [INFO] - Validation epoch stats:   Loss: 3.2707 - Binary-Cell-Dice: 0.7683 - Binary-Cell-Jacard: 0.6767 - bPQ-Score: 0.5743 - mPQ-Score: 0.4326 - Tissue-MC-Acc.: 0.0335
2023-09-23 22:57:44,386 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 22:57:44,387 [INFO] - Epoch: 115/130
2023-09-23 23:00:31,287 [INFO] - Training epoch stats:     Loss: 3.1349 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0396
2023-09-23 23:04:12,304 [INFO] - Validation epoch stats:   Loss: 3.2669 - Binary-Cell-Dice: 0.7625 - Binary-Cell-Jacard: 0.6688 - bPQ-Score: 0.5688 - mPQ-Score: 0.4283 - Tissue-MC-Acc.: 0.0358
2023-09-23 23:04:45,673 [DEBUG] - Old lr: 0.000050 - New lr: 0.000025
2023-09-23 23:04:45,674 [INFO] - Epoch: 116/130
2023-09-23 23:07:55,880 [INFO] - Training epoch stats:     Loss: 3.1251 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-23 23:11:46,442 [INFO] - Validation epoch stats:   Loss: 3.2522 - Binary-Cell-Dice: 0.7684 - Binary-Cell-Jacard: 0.6782 - bPQ-Score: 0.5758 - mPQ-Score: 0.4345 - Tissue-MC-Acc.: 0.0331
2023-09-23 23:11:54,370 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:11:54,371 [INFO] - Epoch: 117/130
2023-09-23 23:14:37,616 [INFO] - Training epoch stats:     Loss: 3.1216 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0464
2023-09-23 23:18:31,088 [INFO] - Validation epoch stats:   Loss: 3.2570 - Binary-Cell-Dice: 0.7695 - Binary-Cell-Jacard: 0.6787 - bPQ-Score: 0.5800 - mPQ-Score: 0.4385 - Tissue-MC-Acc.: 0.0312
2023-09-23 23:18:58,431 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:18:58,432 [INFO] - Epoch: 118/130
2023-09-23 23:21:45,686 [INFO] - Training epoch stats:     Loss: 3.1262 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-23 23:25:28,206 [INFO] - Validation epoch stats:   Loss: 3.2555 - Binary-Cell-Dice: 0.7696 - Binary-Cell-Jacard: 0.6800 - bPQ-Score: 0.5806 - mPQ-Score: 0.4374 - Tissue-MC-Acc.: 0.0380
2023-09-23 23:25:43,107 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:25:43,107 [INFO] - Epoch: 119/130
2023-09-23 23:28:27,312 [INFO] - Training epoch stats:     Loss: 3.1223 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0428
2023-09-23 23:32:16,533 [INFO] - Validation epoch stats:   Loss: 3.2576 - Binary-Cell-Dice: 0.7751 - Binary-Cell-Jacard: 0.6874 - bPQ-Score: 0.5856 - mPQ-Score: 0.4447 - Tissue-MC-Acc.: 0.0369
2023-09-23 23:32:16,554 [INFO] - New best model - save checkpoint
2023-09-23 23:33:17,348 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:33:17,349 [INFO] - Epoch: 120/130
2023-09-23 23:36:04,448 [INFO] - Training epoch stats:     Loss: 3.1261 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0488
2023-09-23 23:39:43,622 [INFO] - Validation epoch stats:   Loss: 3.2551 - Binary-Cell-Dice: 0.7705 - Binary-Cell-Jacard: 0.6797 - bPQ-Score: 0.5754 - mPQ-Score: 0.4409 - Tissue-MC-Acc.: 0.0395
2023-09-23 23:40:06,342 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:40:06,342 [INFO] - Epoch: 121/130
2023-09-23 23:43:02,246 [INFO] - Training epoch stats:     Loss: 3.1167 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-23 23:46:42,353 [INFO] - Validation epoch stats:   Loss: 3.2503 - Binary-Cell-Dice: 0.7669 - Binary-Cell-Jacard: 0.6764 - bPQ-Score: 0.5759 - mPQ-Score: 0.4346 - Tissue-MC-Acc.: 0.0463
2023-09-23 23:46:55,699 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:46:55,700 [INFO] - Epoch: 122/130
2023-09-23 23:49:36,250 [INFO] - Training epoch stats:     Loss: 3.1115 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0440
2023-09-23 23:53:22,052 [INFO] - Validation epoch stats:   Loss: 3.2521 - Binary-Cell-Dice: 0.7707 - Binary-Cell-Jacard: 0.6806 - bPQ-Score: 0.5770 - mPQ-Score: 0.4412 - Tissue-MC-Acc.: 0.0448
2023-09-23 23:53:28,546 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:53:28,546 [INFO] - Epoch: 123/130
2023-09-23 23:56:15,000 [INFO] - Training epoch stats:     Loss: 3.1157 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0495
2023-09-23 23:59:59,512 [INFO] - Validation epoch stats:   Loss: 3.2505 - Binary-Cell-Dice: 0.7675 - Binary-Cell-Jacard: 0.6781 - bPQ-Score: 0.5811 - mPQ-Score: 0.4433 - Tissue-MC-Acc.: 0.0403
2023-09-24 00:00:26,973 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:00:26,974 [INFO] - Epoch: 124/130
2023-09-24 00:03:13,045 [INFO] - Training epoch stats:     Loss: 3.1093 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0476
2023-09-24 00:06:55,929 [INFO] - Validation epoch stats:   Loss: 3.2543 - Binary-Cell-Dice: 0.7713 - Binary-Cell-Jacard: 0.6812 - bPQ-Score: 0.5772 - mPQ-Score: 0.4449 - Tissue-MC-Acc.: 0.0429
2023-09-24 00:07:02,611 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:07:02,612 [INFO] - Epoch: 125/130
2023-09-24 00:09:45,143 [INFO] - Training epoch stats:     Loss: 3.1086 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-24 00:13:28,087 [INFO] - Validation epoch stats:   Loss: 3.2556 - Binary-Cell-Dice: 0.7666 - Binary-Cell-Jacard: 0.6767 - bPQ-Score: 0.5771 - mPQ-Score: 0.4409 - Tissue-MC-Acc.: 0.0377
2023-09-24 00:13:37,275 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:13:37,276 [INFO] - Epoch: 126/130
2023-09-24 00:16:21,290 [INFO] - Training epoch stats:     Loss: 3.1097 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-24 00:20:04,219 [INFO] - Validation epoch stats:   Loss: 3.2566 - Binary-Cell-Dice: 0.7710 - Binary-Cell-Jacard: 0.6823 - bPQ-Score: 0.5836 - mPQ-Score: 0.4471 - Tissue-MC-Acc.: 0.0388
2023-09-24 00:20:26,692 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:20:26,692 [INFO] - Epoch: 127/130
2023-09-24 00:23:08,688 [INFO] - Training epoch stats:     Loss: 3.1074 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0484
2023-09-24 00:26:59,223 [INFO] - Validation epoch stats:   Loss: 3.2551 - Binary-Cell-Dice: 0.7741 - Binary-Cell-Jacard: 0.6853 - bPQ-Score: 0.5855 - mPQ-Score: 0.4464 - Tissue-MC-Acc.: 0.0425
2023-09-24 00:27:08,050 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:27:08,050 [INFO] - Epoch: 128/130
2023-09-24 00:29:51,070 [INFO] - Training epoch stats:     Loss: 3.1127 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0499
2023-09-24 00:33:40,317 [INFO] - Validation epoch stats:   Loss: 3.2615 - Binary-Cell-Dice: 0.7713 - Binary-Cell-Jacard: 0.6822 - bPQ-Score: 0.5844 - mPQ-Score: 0.4434 - Tissue-MC-Acc.: 0.0410
2023-09-24 00:33:49,318 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:33:49,318 [INFO] - Epoch: 129/130
2023-09-24 00:36:32,316 [INFO] - Training epoch stats:     Loss: 3.1158 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0460
2023-09-24 00:40:20,550 [INFO] - Validation epoch stats:   Loss: 3.2546 - Binary-Cell-Dice: 0.7692 - Binary-Cell-Jacard: 0.6782 - bPQ-Score: 0.5766 - mPQ-Score: 0.4392 - Tissue-MC-Acc.: 0.0437
2023-09-24 00:40:45,786 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:40:45,787 [INFO] - Epoch: 130/130
2023-09-24 00:43:28,261 [INFO] - Training epoch stats:     Loss: 3.1160 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0507
2023-09-24 00:47:15,265 [INFO] - Validation epoch stats:   Loss: 3.2524 - Binary-Cell-Dice: 0.7733 - Binary-Cell-Jacard: 0.6842 - bPQ-Score: 0.5857 - mPQ-Score: 0.4446 - Tissue-MC-Acc.: 0.0527
2023-09-24 00:47:15,323 [INFO] - New best model - save checkpoint
2023-09-24 00:47:31,596 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:47:31,597 [INFO] -
