2023-09-23 10:19:50,425 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-23 10:19:50,490 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7fbb94cfbc40>]
2023-09-23 10:19:50,490 [INFO] - Using GPU: cuda:0
2023-09-23 10:19:50,491 [INFO] - Using device: cuda:0
2023-09-23 10:19:50,492 [INFO] - Loss functions:
2023-09-23 10:19:50,492 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'stardist_map_refined': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': XentropyLoss(), 'weight': 0.5}, 'dice': {'loss_fn': DiceLoss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-23 10:20:12,463 [INFO] - Loaded CellVit256 model
2023-09-23 10:20:12,468 [INFO] -
Model: CellViT256CPP(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (stardist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (dist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (stardist_head): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (dist_head): Conv2d(32, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (type_head): Conv2d(32, 6, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
  (conv_0_confidence): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (conv_1_confidence): Conv2d(6, 6, kernel_size=(1, 1), stride=(1, 1))
  (sampling_features): SamplingFeatures()
  (final_activation_ray): ReLU(inplace=True)
)
2023-09-23 10:20:13,715 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256CPP                                 [1, 19]                   6,802,595
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 32, 256, 256]         2,080
├─Sequential: 1-27                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 32, 256, 256]         2,080
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-20                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          (recursive)
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          (recursive)
├─Sequential: 1-22                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          (recursive)
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        (recursive)
├─Sequential: 1-24                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        (recursive)
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         (recursive)
├─Sequential: 1-26                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 32, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2d: 3-76                      [1, 32, 256, 256]         (recursive)
├─Conv2d: 1-28                                [1, 32, 256, 256]         1,024
├─Conv2d: 1-29                                [1, 1, 256, 256]          32
├─Conv2d: 1-30                                [1, 6, 256, 256]          192
├─Conv2d: 1-31                                [1, 32, 256, 256]         1,024
├─SamplingFeatures: 1-32                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-33                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-34                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-35                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-36                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-37                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-38                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-39                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-40                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-41                      [1, 32, 256, 256]         --
├─Conv2d: 1-42                                [32, 6, 256, 256]         42
├─ReLU: 1-43                                  [1, 32, 256, 256]         --
===============================================================================================
Total params: 46,763,136
Trainable params: 25,097,472
Non-trainable params: 21,665,664
Total mult-adds (G): 133.49
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1855.47
Params size (MB): 159.54
Estimated Total Size (MB): 2015.80
===============================================================================================
2023-09-23 10:20:24,338 [INFO] - Loaded Adam Optimizer with following hyperparameters:
2023-09-23 10:20:24,339 [INFO] - {'lr': 0.0001}
2023-09-23 10:20:24,340 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-23 10:20:25,564 [INFO] - Using RandomSampler
2023-09-23 10:20:25,564 [INFO] - Instantiate Trainer
2023-09-23 10:20:25,564 [INFO] - Calling Trainer Fit
2023-09-23 10:20:25,565 [INFO] - Starting training, total number of epochs: 130
2023-09-23 10:20:25,565 [INFO] - Epoch: 1/130
2023-09-23 10:23:36,728 [INFO] - Training epoch stats:     Loss: 5.1285 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0230
2023-09-23 10:27:56,459 [INFO] - Validation epoch stats:   Loss: 4.5596 - Binary-Cell-Dice: 0.1660 - Binary-Cell-Jacard: 0.1121 - bPQ-Score: 0.0056 - mPQ-Score: 0.0013 - Tissue-MC-Acc.: 0.0281
2023-09-23 10:27:56,468 [INFO] - New best model - save checkpoint
2023-09-23 10:28:22,599 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:28:22,600 [INFO] - Epoch: 2/130
2023-09-23 10:31:19,458 [INFO] - Training epoch stats:     Loss: 4.2884 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0207
2023-09-23 10:34:27,999 [INFO] - Validation epoch stats:   Loss: 3.9566 - Binary-Cell-Dice: 0.3856 - Binary-Cell-Jacard: 0.2837 - bPQ-Score: 0.1960 - mPQ-Score: 0.0837 - Tissue-MC-Acc.: 0.0345
2023-09-23 10:34:28,001 [INFO] - New best model - save checkpoint
2023-09-23 10:34:39,974 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:34:39,975 [INFO] - Epoch: 3/130
2023-09-23 10:37:23,691 [INFO] - Training epoch stats:     Loss: 3.9532 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0218
2023-09-23 10:40:22,237 [INFO] - Validation epoch stats:   Loss: 3.7607 - Binary-Cell-Dice: 0.4872 - Binary-Cell-Jacard: 0.3761 - bPQ-Score: 0.3000 - mPQ-Score: 0.1565 - Tissue-MC-Acc.: 0.0345
2023-09-23 10:40:22,247 [INFO] - New best model - save checkpoint
2023-09-23 10:40:51,124 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:40:51,124 [INFO] - Epoch: 4/130
2023-09-23 10:43:48,364 [INFO] - Training epoch stats:     Loss: 3.8062 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0267
2023-09-23 10:46:54,797 [INFO] - Validation epoch stats:   Loss: 3.6854 - Binary-Cell-Dice: 0.4961 - Binary-Cell-Jacard: 0.3775 - bPQ-Score: 0.3085 - mPQ-Score: 0.1677 - Tissue-MC-Acc.: 0.0345
2023-09-23 10:46:54,808 [INFO] - New best model - save checkpoint
2023-09-23 10:47:19,247 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:47:19,248 [INFO] - Epoch: 5/130
2023-09-23 10:50:16,849 [INFO] - Training epoch stats:     Loss: 3.7163 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0267
2023-09-23 10:53:22,046 [INFO] - Validation epoch stats:   Loss: 3.5857 - Binary-Cell-Dice: 0.5560 - Binary-Cell-Jacard: 0.4390 - bPQ-Score: 0.3664 - mPQ-Score: 0.2199 - Tissue-MC-Acc.: 0.0238
2023-09-23 10:53:22,056 [INFO] - New best model - save checkpoint
2023-09-23 10:53:48,562 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 10:53:48,563 [INFO] - Epoch: 6/130
2023-09-23 10:56:59,819 [INFO] - Training epoch stats:     Loss: 3.6625 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 10:59:59,588 [INFO] - Validation epoch stats:   Loss: 3.5702 - Binary-Cell-Dice: 0.5183 - Binary-Cell-Jacard: 0.3927 - bPQ-Score: 0.3444 - mPQ-Score: 0.2117 - Tissue-MC-Acc.: 0.0254
2023-09-23 11:00:20,549 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:00:20,550 [INFO] - Epoch: 7/130
2023-09-23 11:03:22,922 [INFO] - Training epoch stats:     Loss: 3.6240 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0279
2023-09-23 11:06:30,096 [INFO] - Validation epoch stats:   Loss: 3.5240 - Binary-Cell-Dice: 0.6138 - Binary-Cell-Jacard: 0.4955 - bPQ-Score: 0.4293 - mPQ-Score: 0.2651 - Tissue-MC-Acc.: 0.0266
2023-09-23 11:06:30,215 [INFO] - New best model - save checkpoint
2023-09-23 11:07:08,106 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:07:08,107 [INFO] - Epoch: 8/130
2023-09-23 11:10:04,581 [INFO] - Training epoch stats:     Loss: 3.5956 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 11:13:18,597 [INFO] - Validation epoch stats:   Loss: 3.5575 - Binary-Cell-Dice: 0.6407 - Binary-Cell-Jacard: 0.5254 - bPQ-Score: 0.4472 - mPQ-Score: 0.2718 - Tissue-MC-Acc.: 0.0254
2023-09-23 11:13:18,599 [INFO] - New best model - save checkpoint
2023-09-23 11:13:30,524 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:13:30,525 [INFO] - Epoch: 9/130
2023-09-23 11:16:24,626 [INFO] - Training epoch stats:     Loss: 3.5705 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0305
2023-09-23 11:19:29,562 [INFO] - Validation epoch stats:   Loss: 3.5526 - Binary-Cell-Dice: 0.5875 - Binary-Cell-Jacard: 0.4707 - bPQ-Score: 0.4160 - mPQ-Score: 0.2434 - Tissue-MC-Acc.: 0.0222
2023-09-23 11:19:53,483 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:19:53,484 [INFO] - Epoch: 10/130
2023-09-23 11:22:47,693 [INFO] - Training epoch stats:     Loss: 3.5531 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0305
2023-09-23 11:26:15,475 [INFO] - Validation epoch stats:   Loss: 3.4751 - Binary-Cell-Dice: 0.7033 - Binary-Cell-Jacard: 0.5908 - bPQ-Score: 0.4757 - mPQ-Score: 0.3157 - Tissue-MC-Acc.: 0.0214
2023-09-23 11:26:15,477 [INFO] - New best model - save checkpoint
2023-09-23 11:26:27,380 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:26:27,380 [INFO] - Epoch: 11/130
2023-09-23 11:29:19,132 [INFO] - Training epoch stats:     Loss: 3.5285 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0339
2023-09-23 11:32:39,450 [INFO] - Validation epoch stats:   Loss: 3.4610 - Binary-Cell-Dice: 0.6945 - Binary-Cell-Jacard: 0.5823 - bPQ-Score: 0.4856 - mPQ-Score: 0.3228 - Tissue-MC-Acc.: 0.0230
2023-09-23 11:32:39,464 [INFO] - New best model - save checkpoint
2023-09-23 11:33:08,682 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:33:08,683 [INFO] - Epoch: 12/130
2023-09-23 11:36:03,295 [INFO] - Training epoch stats:     Loss: 3.5310 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0343
2023-09-23 11:39:35,418 [INFO] - Validation epoch stats:   Loss: 3.4389 - Binary-Cell-Dice: 0.6760 - Binary-Cell-Jacard: 0.5663 - bPQ-Score: 0.4861 - mPQ-Score: 0.3238 - Tissue-MC-Acc.: 0.0222
2023-09-23 11:39:35,426 [INFO] - New best model - save checkpoint
2023-09-23 11:40:03,662 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:40:03,662 [INFO] - Epoch: 13/130
2023-09-23 11:42:52,840 [INFO] - Training epoch stats:     Loss: 3.5003 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 11:46:15,482 [INFO] - Validation epoch stats:   Loss: 3.4662 - Binary-Cell-Dice: 0.6875 - Binary-Cell-Jacard: 0.5814 - bPQ-Score: 0.4902 - mPQ-Score: 0.3231 - Tissue-MC-Acc.: 0.0250
2023-09-23 11:46:15,490 [INFO] - New best model - save checkpoint
2023-09-23 11:46:43,546 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:46:43,546 [INFO] - Epoch: 14/130
2023-09-23 11:49:38,126 [INFO] - Training epoch stats:     Loss: 3.5061 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0324
2023-09-23 11:53:15,571 [INFO] - Validation epoch stats:   Loss: 3.4589 - Binary-Cell-Dice: 0.7151 - Binary-Cell-Jacard: 0.6109 - bPQ-Score: 0.5061 - mPQ-Score: 0.3395 - Tissue-MC-Acc.: 0.0226
2023-09-23 11:53:15,578 [INFO] - New best model - save checkpoint
2023-09-23 11:53:44,392 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 11:53:44,393 [INFO] - Epoch: 15/130
2023-09-23 11:56:36,578 [INFO] - Training epoch stats:     Loss: 3.4789 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0282
2023-09-23 12:00:01,853 [INFO] - Validation epoch stats:   Loss: 3.4348 - Binary-Cell-Dice: 0.7063 - Binary-Cell-Jacard: 0.5992 - bPQ-Score: 0.4958 - mPQ-Score: 0.3329 - Tissue-MC-Acc.: 0.0218
2023-09-23 12:00:18,193 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:00:18,194 [INFO] - Epoch: 16/130
2023-09-23 12:03:16,303 [INFO] - Training epoch stats:     Loss: 3.4733 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0358
2023-09-23 12:06:48,282 [INFO] - Validation epoch stats:   Loss: 3.4768 - Binary-Cell-Dice: 0.7170 - Binary-Cell-Jacard: 0.6078 - bPQ-Score: 0.4970 - mPQ-Score: 0.3195 - Tissue-MC-Acc.: 0.0329
2023-09-23 12:06:53,850 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:06:53,851 [INFO] - Epoch: 17/130
2023-09-23 12:09:39,268 [INFO] - Training epoch stats:     Loss: 3.4790 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0486
2023-09-23 12:13:07,358 [INFO] - Validation epoch stats:   Loss: 3.4422 - Binary-Cell-Dice: 0.6998 - Binary-Cell-Jacard: 0.5958 - bPQ-Score: 0.4956 - mPQ-Score: 0.3360 - Tissue-MC-Acc.: 0.0935
2023-09-23 12:13:19,823 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:13:19,824 [INFO] - Epoch: 18/130
2023-09-23 12:16:16,866 [INFO] - Training epoch stats:     Loss: 3.4645 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0685
2023-09-23 12:19:43,786 [INFO] - Validation epoch stats:   Loss: 3.4211 - Binary-Cell-Dice: 0.7159 - Binary-Cell-Jacard: 0.6089 - bPQ-Score: 0.4985 - mPQ-Score: 0.3388 - Tissue-MC-Acc.: 0.0967
2023-09-23 12:19:54,699 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:19:54,699 [INFO] - Epoch: 19/130
2023-09-23 12:22:52,583 [INFO] - Training epoch stats:     Loss: 3.4433 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0441
2023-09-23 12:26:26,382 [INFO] - Validation epoch stats:   Loss: 3.3962 - Binary-Cell-Dice: 0.6979 - Binary-Cell-Jacard: 0.5918 - bPQ-Score: 0.4932 - mPQ-Score: 0.3360 - Tissue-MC-Acc.: 0.0369
2023-09-23 12:26:32,303 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:26:32,303 [INFO] - Epoch: 20/130
2023-09-23 12:29:25,776 [INFO] - Training epoch stats:     Loss: 3.4383 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0463
2023-09-23 12:32:49,819 [INFO] - Validation epoch stats:   Loss: 3.4035 - Binary-Cell-Dice: 0.6942 - Binary-Cell-Jacard: 0.5872 - bPQ-Score: 0.4952 - mPQ-Score: 0.3366 - Tissue-MC-Acc.: 0.0456
2023-09-23 12:33:05,533 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:33:05,534 [INFO] - Epoch: 21/130
2023-09-23 12:36:02,114 [INFO] - Training epoch stats:     Loss: 3.4442 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0444
2023-09-23 12:39:36,328 [INFO] - Validation epoch stats:   Loss: 3.4153 - Binary-Cell-Dice: 0.7266 - Binary-Cell-Jacard: 0.6254 - bPQ-Score: 0.5213 - mPQ-Score: 0.3497 - Tissue-MC-Acc.: 0.0416
2023-09-23 12:39:36,338 [INFO] - New best model - save checkpoint
2023-09-23 12:40:04,071 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:40:04,071 [INFO] - Epoch: 22/130
2023-09-23 12:42:58,909 [INFO] - Training epoch stats:     Loss: 3.4325 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0437
2023-09-23 12:46:32,312 [INFO] - Validation epoch stats:   Loss: 3.4111 - Binary-Cell-Dice: 0.7253 - Binary-Cell-Jacard: 0.6224 - bPQ-Score: 0.5188 - mPQ-Score: 0.3476 - Tissue-MC-Acc.: 0.0384
2023-09-23 12:46:48,044 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:46:48,045 [INFO] - Epoch: 23/130
2023-09-23 12:49:45,441 [INFO] - Training epoch stats:     Loss: 3.4353 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0425
2023-09-23 12:53:20,003 [INFO] - Validation epoch stats:   Loss: 3.4361 - Binary-Cell-Dice: 0.7288 - Binary-Cell-Jacard: 0.6265 - bPQ-Score: 0.5167 - mPQ-Score: 0.3580 - Tissue-MC-Acc.: 0.0511
2023-09-23 12:53:34,964 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 12:53:34,965 [INFO] - Epoch: 24/130
2023-09-23 12:56:30,508 [INFO] - Training epoch stats:     Loss: 3.4168 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0482
2023-09-23 13:00:14,515 [INFO] - Validation epoch stats:   Loss: 3.3763 - Binary-Cell-Dice: 0.7219 - Binary-Cell-Jacard: 0.6175 - bPQ-Score: 0.4900 - mPQ-Score: 0.3451 - Tissue-MC-Acc.: 0.0662
2023-09-23 13:00:28,973 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:00:28,973 [INFO] - Epoch: 25/130
2023-09-23 13:03:24,374 [INFO] - Training epoch stats:     Loss: 3.4163 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0663
2023-09-23 13:06:56,217 [INFO] - Validation epoch stats:   Loss: 3.3745 - Binary-Cell-Dice: 0.7053 - Binary-Cell-Jacard: 0.5972 - bPQ-Score: 0.5043 - mPQ-Score: 0.3538 - Tissue-MC-Acc.: 0.0935
2023-09-23 13:07:07,932 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:07:07,933 [INFO] - Epoch: 26/130
2023-09-23 13:10:04,073 [INFO] - Training epoch stats:     Loss: 3.4128 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0715
2023-09-23 13:13:17,732 [INFO] - Validation epoch stats:   Loss: 3.3756 - Binary-Cell-Dice: 0.7064 - Binary-Cell-Jacard: 0.6029 - bPQ-Score: 0.5107 - mPQ-Score: 0.3544 - Tissue-MC-Acc.: 0.0345
2023-09-23 13:13:30,771 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:13:30,771 [INFO] - Epoch: 27/130
2023-09-23 13:16:27,183 [INFO] - Training epoch stats:     Loss: 3.4036 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0523
2023-09-23 13:19:47,970 [INFO] - Validation epoch stats:   Loss: 3.3542 - Binary-Cell-Dice: 0.7259 - Binary-Cell-Jacard: 0.6243 - bPQ-Score: 0.5210 - mPQ-Score: 0.3658 - Tissue-MC-Acc.: 0.0285
2023-09-23 13:19:53,919 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:19:53,919 [INFO] - Epoch: 28/130
2023-09-23 13:22:43,873 [INFO] - Training epoch stats:     Loss: 3.3989 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-23 13:26:05,229 [INFO] - Validation epoch stats:   Loss: 3.3754 - Binary-Cell-Dice: 0.7380 - Binary-Cell-Jacard: 0.6363 - bPQ-Score: 0.5285 - mPQ-Score: 0.3655 - Tissue-MC-Acc.: 0.0388
2023-09-23 13:26:05,235 [INFO] - New best model - save checkpoint
2023-09-23 13:26:32,733 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:26:32,734 [INFO] - Epoch: 29/130
2023-09-23 13:29:30,118 [INFO] - Training epoch stats:     Loss: 3.3967 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0493
2023-09-23 13:32:54,273 [INFO] - Validation epoch stats:   Loss: 3.3628 - Binary-Cell-Dice: 0.7292 - Binary-Cell-Jacard: 0.6236 - bPQ-Score: 0.5068 - mPQ-Score: 0.3547 - Tissue-MC-Acc.: 0.0388
2023-09-23 13:33:07,732 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:33:07,733 [INFO] - Epoch: 30/130
2023-09-23 13:36:07,206 [INFO] - Training epoch stats:     Loss: 3.3991 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0489
2023-09-23 13:39:26,766 [INFO] - Validation epoch stats:   Loss: 3.3537 - Binary-Cell-Dice: 0.7323 - Binary-Cell-Jacard: 0.6299 - bPQ-Score: 0.5171 - mPQ-Score: 0.3714 - Tissue-MC-Acc.: 0.0400
2023-09-23 13:39:32,709 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:39:32,710 [INFO] - Epoch: 31/130
2023-09-23 13:42:25,563 [INFO] - Training epoch stats:     Loss: 3.3755 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0527
2023-09-23 13:45:48,937 [INFO] - Validation epoch stats:   Loss: 3.3495 - Binary-Cell-Dice: 0.7182 - Binary-Cell-Jacard: 0.6119 - bPQ-Score: 0.5180 - mPQ-Score: 0.3662 - Tissue-MC-Acc.: 0.0575
2023-09-23 13:46:02,800 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:46:02,801 [INFO] - Epoch: 32/130
2023-09-23 13:48:57,483 [INFO] - Training epoch stats:     Loss: 3.3583 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0474
2023-09-23 13:53:24,588 [INFO] - Validation epoch stats:   Loss: 3.3402 - Binary-Cell-Dice: 0.7298 - Binary-Cell-Jacard: 0.6218 - bPQ-Score: 0.4406 - mPQ-Score: 0.3617 - Tissue-MC-Acc.: 0.0357
2023-09-23 13:53:36,765 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 13:53:36,765 [INFO] - Epoch: 33/130
2023-09-23 13:56:37,019 [INFO] - Training epoch stats:     Loss: 3.3599 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0520
2023-09-23 14:00:08,513 [INFO] - Validation epoch stats:   Loss: 3.3426 - Binary-Cell-Dice: 0.7195 - Binary-Cell-Jacard: 0.6173 - bPQ-Score: 0.5177 - mPQ-Score: 0.3610 - Tissue-MC-Acc.: 0.0606
2023-09-23 14:00:14,952 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:00:14,953 [INFO] - Epoch: 34/130
2023-09-23 14:03:04,433 [INFO] - Training epoch stats:     Loss: 3.3569 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0425
2023-09-23 14:06:41,083 [INFO] - Validation epoch stats:   Loss: 3.3531 - Binary-Cell-Dice: 0.7427 - Binary-Cell-Jacard: 0.6451 - bPQ-Score: 0.5283 - mPQ-Score: 0.3754 - Tissue-MC-Acc.: 0.0313
2023-09-23 14:06:56,581 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:06:56,581 [INFO] - Epoch: 35/130
2023-09-23 14:09:55,060 [INFO] - Training epoch stats:     Loss: 3.3642 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0418
2023-09-23 14:13:28,568 [INFO] - Validation epoch stats:   Loss: 3.3483 - Binary-Cell-Dice: 0.7460 - Binary-Cell-Jacard: 0.6486 - bPQ-Score: 0.4914 - mPQ-Score: 0.3743 - Tissue-MC-Acc.: 0.0591
2023-09-23 14:13:43,723 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:13:43,724 [INFO] - Epoch: 36/130
2023-09-23 14:16:36,815 [INFO] - Training epoch stats:     Loss: 3.3537 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0437
2023-09-23 14:20:11,298 [INFO] - Validation epoch stats:   Loss: 3.3462 - Binary-Cell-Dice: 0.7423 - Binary-Cell-Jacard: 0.6431 - bPQ-Score: 0.5277 - mPQ-Score: 0.3814 - Tissue-MC-Acc.: 0.0507
2023-09-23 14:20:17,225 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:20:17,226 [INFO] - Epoch: 37/130
2023-09-23 14:23:09,406 [INFO] - Training epoch stats:     Loss: 3.3570 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0399
2023-09-23 14:26:31,507 [INFO] - Validation epoch stats:   Loss: 3.3329 - Binary-Cell-Dice: 0.7363 - Binary-Cell-Jacard: 0.6359 - bPQ-Score: 0.5298 - mPQ-Score: 0.3775 - Tissue-MC-Acc.: 0.0309
2023-09-23 14:26:31,512 [INFO] - New best model - save checkpoint
2023-09-23 14:26:43,598 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:26:43,598 [INFO] - Epoch: 38/130
2023-09-23 14:29:40,226 [INFO] - Training epoch stats:     Loss: 3.3382 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-23 14:33:14,309 [INFO] - Validation epoch stats:   Loss: 3.3590 - Binary-Cell-Dice: 0.7297 - Binary-Cell-Jacard: 0.6301 - bPQ-Score: 0.5038 - mPQ-Score: 0.3660 - Tissue-MC-Acc.: 0.0384
2023-09-23 14:33:26,991 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:33:26,991 [INFO] - Epoch: 39/130
2023-09-23 14:36:24,463 [INFO] - Training epoch stats:     Loss: 3.3462 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0482
2023-09-23 14:39:51,938 [INFO] - Validation epoch stats:   Loss: 3.3327 - Binary-Cell-Dice: 0.7393 - Binary-Cell-Jacard: 0.6396 - bPQ-Score: 0.5308 - mPQ-Score: 0.3758 - Tissue-MC-Acc.: 0.0404
2023-09-23 14:39:51,943 [INFO] - New best model - save checkpoint
2023-09-23 14:40:03,876 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:40:03,877 [INFO] - Epoch: 40/130
2023-09-23 14:42:55,534 [INFO] - Training epoch stats:     Loss: 3.3295 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0520
2023-09-23 14:46:16,852 [INFO] - Validation epoch stats:   Loss: 3.3467 - Binary-Cell-Dice: 0.7440 - Binary-Cell-Jacard: 0.6492 - bPQ-Score: 0.5392 - mPQ-Score: 0.3778 - Tissue-MC-Acc.: 0.0468
2023-09-23 14:46:16,862 [INFO] - New best model - save checkpoint
2023-09-23 14:46:46,652 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:46:46,653 [INFO] - Epoch: 41/130
2023-09-23 14:49:40,790 [INFO] - Training epoch stats:     Loss: 3.3379 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0399
2023-09-23 14:53:15,444 [INFO] - Validation epoch stats:   Loss: 3.3166 - Binary-Cell-Dice: 0.7356 - Binary-Cell-Jacard: 0.6381 - bPQ-Score: 0.5341 - mPQ-Score: 0.3829 - Tissue-MC-Acc.: 0.0507
2023-09-23 14:53:21,805 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:53:21,805 [INFO] - Epoch: 42/130
2023-09-23 14:56:13,086 [INFO] - Training epoch stats:     Loss: 3.3276 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0467
2023-09-23 14:59:41,721 [INFO] - Validation epoch stats:   Loss: 3.3048 - Binary-Cell-Dice: 0.7380 - Binary-Cell-Jacard: 0.6396 - bPQ-Score: 0.5299 - mPQ-Score: 0.3790 - Tissue-MC-Acc.: 0.0293
2023-09-23 14:59:53,074 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 14:59:53,075 [INFO] - Epoch: 43/130
2023-09-23 15:02:52,115 [INFO] - Training epoch stats:     Loss: 3.3169 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0433
2023-09-23 15:06:14,350 [INFO] - Validation epoch stats:   Loss: 3.3255 - Binary-Cell-Dice: 0.7476 - Binary-Cell-Jacard: 0.6495 - bPQ-Score: 0.5433 - mPQ-Score: 0.3899 - Tissue-MC-Acc.: 0.0432
2023-09-23 15:06:14,354 [INFO] - New best model - save checkpoint
2023-09-23 15:06:28,417 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:06:28,418 [INFO] - Epoch: 44/130
2023-09-23 15:09:19,567 [INFO] - Training epoch stats:     Loss: 3.3145 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0395
2023-09-23 15:12:48,494 [INFO] - Validation epoch stats:   Loss: 3.3250 - Binary-Cell-Dice: 0.7474 - Binary-Cell-Jacard: 0.6520 - bPQ-Score: 0.5425 - mPQ-Score: 0.3904 - Tissue-MC-Acc.: 0.0293
2023-09-23 15:13:08,755 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:13:08,756 [INFO] - Epoch: 45/130
2023-09-23 15:16:09,074 [INFO] - Training epoch stats:     Loss: 3.3416 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0343
2023-09-23 15:19:31,193 [INFO] - Validation epoch stats:   Loss: 3.3076 - Binary-Cell-Dice: 0.7333 - Binary-Cell-Jacard: 0.6369 - bPQ-Score: 0.5447 - mPQ-Score: 0.3899 - Tissue-MC-Acc.: 0.0365
2023-09-23 15:19:31,323 [INFO] - New best model - save checkpoint
2023-09-23 15:19:54,200 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:19:54,201 [INFO] - Epoch: 46/130
2023-09-23 15:22:43,518 [INFO] - Training epoch stats:     Loss: 3.3047 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0346
2023-09-23 15:25:58,454 [INFO] - Validation epoch stats:   Loss: 3.3392 - Binary-Cell-Dice: 0.7252 - Binary-Cell-Jacard: 0.6261 - bPQ-Score: 0.5311 - mPQ-Score: 0.3534 - Tissue-MC-Acc.: 0.0404
2023-09-23 15:26:04,392 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:26:04,393 [INFO] - Epoch: 47/130
2023-09-23 15:28:57,462 [INFO] - Training epoch stats:     Loss: 3.3097 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0474
2023-09-23 15:32:23,493 [INFO] - Validation epoch stats:   Loss: 3.3008 - Binary-Cell-Dice: 0.7431 - Binary-Cell-Jacard: 0.6470 - bPQ-Score: 0.5437 - mPQ-Score: 0.3907 - Tissue-MC-Acc.: 0.0452
2023-09-23 15:32:28,993 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:32:28,994 [INFO] - Epoch: 48/130
2023-09-23 15:35:21,709 [INFO] - Training epoch stats:     Loss: 3.2968 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-23 15:38:40,729 [INFO] - Validation epoch stats:   Loss: 3.2902 - Binary-Cell-Dice: 0.7471 - Binary-Cell-Jacard: 0.6526 - bPQ-Score: 0.5501 - mPQ-Score: 0.3989 - Tissue-MC-Acc.: 0.0503
2023-09-23 15:38:40,738 [INFO] - New best model - save checkpoint
2023-09-23 15:39:03,295 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:39:03,296 [INFO] - Epoch: 49/130
2023-09-23 15:42:07,587 [INFO] - Training epoch stats:     Loss: 3.2974 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0441
2023-09-23 15:45:39,792 [INFO] - Validation epoch stats:   Loss: 3.3064 - Binary-Cell-Dice: 0.7408 - Binary-Cell-Jacard: 0.6457 - bPQ-Score: 0.5380 - mPQ-Score: 0.3811 - Tissue-MC-Acc.: 0.0357
2023-09-23 15:45:48,950 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:45:48,950 [INFO] - Epoch: 50/130
2023-09-23 15:48:41,610 [INFO] - Training epoch stats:     Loss: 3.3019 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-23 15:51:58,092 [INFO] - Validation epoch stats:   Loss: 3.2897 - Binary-Cell-Dice: 0.7327 - Binary-Cell-Jacard: 0.6348 - bPQ-Score: 0.5428 - mPQ-Score: 0.3807 - Tissue-MC-Acc.: 0.0293
2023-09-23 15:52:07,647 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:52:07,648 [INFO] - Epoch: 51/130
2023-09-23 15:55:08,784 [INFO] - Training epoch stats:     Loss: 3.3074 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-23 15:58:32,741 [INFO] - Validation epoch stats:   Loss: 3.3088 - Binary-Cell-Dice: 0.7486 - Binary-Cell-Jacard: 0.6569 - bPQ-Score: 0.5514 - mPQ-Score: 0.3884 - Tissue-MC-Acc.: 0.0404
2023-09-23 15:58:32,743 [INFO] - New best model - save checkpoint
2023-09-23 15:58:48,034 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 15:58:48,035 [INFO] - Epoch: 52/130
2023-09-23 16:01:49,481 [INFO] - Training epoch stats:     Loss: 3.2990 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0414
2023-09-23 16:05:16,294 [INFO] - Validation epoch stats:   Loss: 3.2917 - Binary-Cell-Dice: 0.7396 - Binary-Cell-Jacard: 0.6456 - bPQ-Score: 0.5484 - mPQ-Score: 0.3861 - Tissue-MC-Acc.: 0.0377
2023-09-23 16:05:22,315 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:05:22,316 [INFO] - Epoch: 53/130
2023-09-23 16:08:16,492 [INFO] - Training epoch stats:     Loss: 3.2663 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0486
2023-09-23 16:11:32,749 [INFO] - Validation epoch stats:   Loss: 3.3166 - Binary-Cell-Dice: 0.7419 - Binary-Cell-Jacard: 0.6467 - bPQ-Score: 0.5489 - mPQ-Score: 0.3837 - Tissue-MC-Acc.: 0.0507
2023-09-23 16:11:50,406 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:11:50,406 [INFO] - Epoch: 54/130
2023-09-23 16:14:51,778 [INFO] - Training epoch stats:     Loss: 3.2870 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-23 16:18:20,854 [INFO] - Validation epoch stats:   Loss: 3.3146 - Binary-Cell-Dice: 0.7515 - Binary-Cell-Jacard: 0.6600 - bPQ-Score: 0.5592 - mPQ-Score: 0.3997 - Tissue-MC-Acc.: 0.0353
2023-09-23 16:18:20,856 [INFO] - New best model - save checkpoint
2023-09-23 16:18:43,682 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:18:43,682 [INFO] - Epoch: 55/130
2023-09-23 16:21:37,593 [INFO] - Training epoch stats:     Loss: 3.2749 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0418
2023-09-23 16:25:16,420 [INFO] - Validation epoch stats:   Loss: 3.2827 - Binary-Cell-Dice: 0.7497 - Binary-Cell-Jacard: 0.6570 - bPQ-Score: 0.5384 - mPQ-Score: 0.3923 - Tissue-MC-Acc.: 0.0420
2023-09-23 16:25:39,338 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:25:39,338 [INFO] - Epoch: 56/130
2023-09-23 16:28:35,645 [INFO] - Training epoch stats:     Loss: 3.2841 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0463
2023-09-23 16:32:02,900 [INFO] - Validation epoch stats:   Loss: 3.2938 - Binary-Cell-Dice: 0.7338 - Binary-Cell-Jacard: 0.6398 - bPQ-Score: 0.5468 - mPQ-Score: 0.3847 - Tissue-MC-Acc.: 0.0495
2023-09-23 16:32:24,195 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:32:24,196 [INFO] - Epoch: 57/130
2023-09-23 16:35:21,962 [INFO] - Training epoch stats:     Loss: 3.2804 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0459
2023-09-23 16:38:49,027 [INFO] - Validation epoch stats:   Loss: 3.2839 - Binary-Cell-Dice: 0.7483 - Binary-Cell-Jacard: 0.6562 - bPQ-Score: 0.5531 - mPQ-Score: 0.3991 - Tissue-MC-Acc.: 0.0456
2023-09-23 16:38:55,498 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:38:55,499 [INFO] - Epoch: 58/130
2023-09-23 16:41:44,106 [INFO] - Training epoch stats:     Loss: 3.2664 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0471
2023-09-23 16:45:02,136 [INFO] - Validation epoch stats:   Loss: 3.2746 - Binary-Cell-Dice: 0.7445 - Binary-Cell-Jacard: 0.6477 - bPQ-Score: 0.5511 - mPQ-Score: 0.3991 - Tissue-MC-Acc.: 0.0420
2023-09-23 16:45:21,023 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:45:21,023 [INFO] - Epoch: 59/130
2023-09-23 16:48:29,094 [INFO] - Training epoch stats:     Loss: 3.2923 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-23 16:52:01,178 [INFO] - Validation epoch stats:   Loss: 3.2620 - Binary-Cell-Dice: 0.7542 - Binary-Cell-Jacard: 0.6614 - bPQ-Score: 0.5585 - mPQ-Score: 0.4099 - Tissue-MC-Acc.: 0.0416
2023-09-23 16:52:07,390 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:52:07,391 [INFO] - Epoch: 60/130
2023-09-23 16:54:58,738 [INFO] - Training epoch stats:     Loss: 3.2637 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0489
2023-09-23 16:58:24,132 [INFO] - Validation epoch stats:   Loss: 3.2940 - Binary-Cell-Dice: 0.7458 - Binary-Cell-Jacard: 0.6511 - bPQ-Score: 0.5514 - mPQ-Score: 0.3981 - Tissue-MC-Acc.: 0.0638
2023-09-23 16:58:30,069 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 16:58:30,070 [INFO] - Epoch: 61/130
2023-09-23 17:01:19,873 [INFO] - Training epoch stats:     Loss: 3.2573 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0508
2023-09-23 17:04:37,422 [INFO] - Validation epoch stats:   Loss: 3.2692 - Binary-Cell-Dice: 0.7445 - Binary-Cell-Jacard: 0.6514 - bPQ-Score: 0.5552 - mPQ-Score: 0.4139 - Tissue-MC-Acc.: 0.0448
2023-09-23 17:04:43,457 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:04:43,457 [INFO] - Epoch: 62/130
2023-09-23 17:07:35,422 [INFO] - Training epoch stats:     Loss: 3.2646 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0407
2023-09-23 17:11:08,056 [INFO] - Validation epoch stats:   Loss: 3.2774 - Binary-Cell-Dice: 0.7511 - Binary-Cell-Jacard: 0.6608 - bPQ-Score: 0.5585 - mPQ-Score: 0.4079 - Tissue-MC-Acc.: 0.0353
2023-09-23 17:11:24,289 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:11:24,289 [INFO] - Epoch: 63/130
2023-09-23 17:14:23,086 [INFO] - Training epoch stats:     Loss: 3.2722 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-23 17:17:51,641 [INFO] - Validation epoch stats:   Loss: 3.2895 - Binary-Cell-Dice: 0.7431 - Binary-Cell-Jacard: 0.6454 - bPQ-Score: 0.5525 - mPQ-Score: 0.4002 - Tissue-MC-Acc.: 0.0392
2023-09-23 17:18:07,500 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:18:07,501 [INFO] - Epoch: 64/130
2023-09-23 17:21:01,658 [INFO] - Training epoch stats:     Loss: 3.2614 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0403
2023-09-23 17:24:23,226 [INFO] - Validation epoch stats:   Loss: 3.2769 - Binary-Cell-Dice: 0.7370 - Binary-Cell-Jacard: 0.6415 - bPQ-Score: 0.5487 - mPQ-Score: 0.3940 - Tissue-MC-Acc.: 0.0456
2023-09-23 17:24:32,491 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:24:32,491 [INFO] - Epoch: 65/130
2023-09-23 17:27:29,564 [INFO] - Training epoch stats:     Loss: 3.2707 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0328
2023-09-23 17:31:05,582 [INFO] - Validation epoch stats:   Loss: 3.2786 - Binary-Cell-Dice: 0.7448 - Binary-Cell-Jacard: 0.6493 - bPQ-Score: 0.5363 - mPQ-Score: 0.3927 - Tissue-MC-Acc.: 0.0345
2023-09-23 17:31:20,953 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:31:20,954 [INFO] - Epoch: 66/130
2023-09-23 17:34:13,421 [INFO] - Training epoch stats:     Loss: 3.2642 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-23 17:37:30,320 [INFO] - Validation epoch stats:   Loss: 3.2973 - Binary-Cell-Dice: 0.7349 - Binary-Cell-Jacard: 0.6414 - bPQ-Score: 0.5473 - mPQ-Score: 0.3927 - Tissue-MC-Acc.: 0.0277
2023-09-23 17:37:52,643 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:37:52,644 [INFO] - Epoch: 67/130
2023-09-23 17:40:48,549 [INFO] - Training epoch stats:     Loss: 3.2608 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0380
2023-09-23 17:44:19,620 [INFO] - Validation epoch stats:   Loss: 3.2697 - Binary-Cell-Dice: 0.7544 - Binary-Cell-Jacard: 0.6636 - bPQ-Score: 0.5560 - mPQ-Score: 0.4141 - Tissue-MC-Acc.: 0.0361
2023-09-23 17:44:25,572 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:44:25,572 [INFO] - Epoch: 68/130
2023-09-23 17:47:17,643 [INFO] - Training epoch stats:     Loss: 3.2640 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0380
2023-09-23 17:50:49,737 [INFO] - Validation epoch stats:   Loss: 3.2680 - Binary-Cell-Dice: 0.7496 - Binary-Cell-Jacard: 0.6568 - bPQ-Score: 0.5592 - mPQ-Score: 0.4038 - Tissue-MC-Acc.: 0.0353
2023-09-23 17:50:49,739 [INFO] - New best model - save checkpoint
2023-09-23 17:51:01,853 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:51:01,854 [INFO] - Epoch: 69/130
2023-09-23 17:53:48,470 [INFO] - Training epoch stats:     Loss: 3.2591 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-23 17:57:14,133 [INFO] - Validation epoch stats:   Loss: 3.2776 - Binary-Cell-Dice: 0.7480 - Binary-Cell-Jacard: 0.6541 - bPQ-Score: 0.5535 - mPQ-Score: 0.4121 - Tissue-MC-Acc.: 0.0293
2023-09-23 17:57:28,932 [DEBUG] - Old lr: 0.000100 - New lr: 0.000100
2023-09-23 17:57:28,933 [INFO] - Epoch: 70/130
2023-09-23 18:00:21,491 [INFO] - Training epoch stats:     Loss: 3.2480 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0354
2023-09-23 18:03:47,753 [INFO] - Validation epoch stats:   Loss: 3.2679 - Binary-Cell-Dice: 0.7463 - Binary-Cell-Jacard: 0.6556 - bPQ-Score: 0.5644 - mPQ-Score: 0.4072 - Tissue-MC-Acc.: 0.0262
2023-09-23 18:03:47,792 [INFO] - New best model - save checkpoint
2023-09-23 18:04:02,515 [DEBUG] - Old lr: 0.000100 - New lr: 0.000050
2023-09-23 18:04:02,515 [INFO] - Epoch: 71/130
2023-09-23 18:06:51,799 [INFO] - Training epoch stats:     Loss: 3.2187 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0264
2023-09-23 18:10:16,612 [INFO] - Validation epoch stats:   Loss: 3.2455 - Binary-Cell-Dice: 0.7547 - Binary-Cell-Jacard: 0.6659 - bPQ-Score: 0.5707 - mPQ-Score: 0.4219 - Tissue-MC-Acc.: 0.0254
2023-09-23 18:10:16,621 [INFO] - New best model - save checkpoint
2023-09-23 18:10:53,050 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:10:53,051 [INFO] - Epoch: 72/130
2023-09-23 18:13:48,371 [INFO] - Training epoch stats:     Loss: 3.1968 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0271
2023-09-23 18:17:13,361 [INFO] - Validation epoch stats:   Loss: 3.2414 - Binary-Cell-Dice: 0.7528 - Binary-Cell-Jacard: 0.6636 - bPQ-Score: 0.5685 - mPQ-Score: 0.4224 - Tissue-MC-Acc.: 0.0285
2023-09-23 18:17:19,438 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:17:19,439 [INFO] - Epoch: 73/130
2023-09-23 18:20:14,201 [INFO] - Training epoch stats:     Loss: 3.2005 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0350
2023-09-23 18:23:38,763 [INFO] - Validation epoch stats:   Loss: 3.2427 - Binary-Cell-Dice: 0.7554 - Binary-Cell-Jacard: 0.6645 - bPQ-Score: 0.5680 - mPQ-Score: 0.4143 - Tissue-MC-Acc.: 0.0262
2023-09-23 18:23:44,740 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:23:44,741 [INFO] - Epoch: 74/130
2023-09-23 18:26:32,461 [INFO] - Training epoch stats:     Loss: 3.1994 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0286
2023-09-23 18:29:56,335 [INFO] - Validation epoch stats:   Loss: 3.2380 - Binary-Cell-Dice: 0.7534 - Binary-Cell-Jacard: 0.6639 - bPQ-Score: 0.5676 - mPQ-Score: 0.4245 - Tissue-MC-Acc.: 0.0266
2023-09-23 18:30:16,233 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:30:16,233 [INFO] - Epoch: 75/130
2023-09-23 18:33:15,218 [INFO] - Training epoch stats:     Loss: 3.2009 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-23 18:36:40,918 [INFO] - Validation epoch stats:   Loss: 3.2327 - Binary-Cell-Dice: 0.7460 - Binary-Cell-Jacard: 0.6542 - bPQ-Score: 0.5593 - mPQ-Score: 0.4152 - Tissue-MC-Acc.: 0.0250
2023-09-23 18:36:46,859 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:36:46,859 [INFO] - Epoch: 76/130
2023-09-23 18:39:38,127 [INFO] - Training epoch stats:     Loss: 3.1920 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0275
2023-09-23 18:43:14,667 [INFO] - Validation epoch stats:   Loss: 3.2444 - Binary-Cell-Dice: 0.7547 - Binary-Cell-Jacard: 0.6650 - bPQ-Score: 0.5694 - mPQ-Score: 0.4203 - Tissue-MC-Acc.: 0.0273
2023-09-23 18:43:20,610 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:43:20,610 [INFO] - Epoch: 77/130
2023-09-23 18:46:13,659 [INFO] - Training epoch stats:     Loss: 3.1988 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 18:49:33,767 [INFO] - Validation epoch stats:   Loss: 3.2392 - Binary-Cell-Dice: 0.7460 - Binary-Cell-Jacard: 0.6534 - bPQ-Score: 0.5592 - mPQ-Score: 0.4122 - Tissue-MC-Acc.: 0.0262
2023-09-23 18:49:42,400 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:49:42,400 [INFO] - Epoch: 78/130
2023-09-23 18:52:34,518 [INFO] - Training epoch stats:     Loss: 3.1896 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0350
2023-09-23 18:56:02,406 [INFO] - Validation epoch stats:   Loss: 3.2353 - Binary-Cell-Dice: 0.7602 - Binary-Cell-Jacard: 0.6720 - bPQ-Score: 0.5715 - mPQ-Score: 0.4298 - Tissue-MC-Acc.: 0.0234
2023-09-23 18:56:02,409 [INFO] - New best model - save checkpoint
2023-09-23 18:56:14,380 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 18:56:14,380 [INFO] - Epoch: 79/130
2023-09-23 18:59:04,379 [INFO] - Training epoch stats:     Loss: 3.1960 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 19:02:27,319 [INFO] - Validation epoch stats:   Loss: 3.2364 - Binary-Cell-Dice: 0.7528 - Binary-Cell-Jacard: 0.6636 - bPQ-Score: 0.5703 - mPQ-Score: 0.4211 - Tissue-MC-Acc.: 0.0230
2023-09-23 19:02:49,321 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:02:49,322 [INFO] - Epoch: 80/130
2023-09-23 19:05:45,804 [INFO] - Training epoch stats:     Loss: 3.1887 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0279
2023-09-23 19:09:21,743 [INFO] - Validation epoch stats:   Loss: 3.2696 - Binary-Cell-Dice: 0.7519 - Binary-Cell-Jacard: 0.6594 - bPQ-Score: 0.5573 - mPQ-Score: 0.4021 - Tissue-MC-Acc.: 0.0266
2023-09-23 19:09:27,732 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:09:27,733 [INFO] - Epoch: 81/130
2023-09-23 19:12:20,004 [INFO] - Training epoch stats:     Loss: 3.1998 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 19:15:53,181 [INFO] - Validation epoch stats:   Loss: 3.2449 - Binary-Cell-Dice: 0.7531 - Binary-Cell-Jacard: 0.6615 - bPQ-Score: 0.5644 - mPQ-Score: 0.4197 - Tissue-MC-Acc.: 0.0337
2023-09-23 19:16:10,017 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:16:10,018 [INFO] - Epoch: 82/130
2023-09-23 19:19:10,363 [INFO] - Training epoch stats:     Loss: 3.1732 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0282
2023-09-23 19:22:43,891 [INFO] - Validation epoch stats:   Loss: 3.2344 - Binary-Cell-Dice: 0.7485 - Binary-Cell-Jacard: 0.6558 - bPQ-Score: 0.5551 - mPQ-Score: 0.4137 - Tissue-MC-Acc.: 0.0297
2023-09-23 19:23:12,211 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:23:12,212 [INFO] - Epoch: 83/130
2023-09-23 19:26:13,110 [INFO] - Training epoch stats:     Loss: 3.1836 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0324
2023-09-23 19:29:44,494 [INFO] - Validation epoch stats:   Loss: 3.2443 - Binary-Cell-Dice: 0.7478 - Binary-Cell-Jacard: 0.6565 - bPQ-Score: 0.5621 - mPQ-Score: 0.4201 - Tissue-MC-Acc.: 0.0305
2023-09-23 19:29:53,387 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:29:53,387 [INFO] - Epoch: 84/130
2023-09-23 19:32:46,446 [INFO] - Training epoch stats:     Loss: 3.1924 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0335
2023-09-23 19:36:23,719 [INFO] - Validation epoch stats:   Loss: 3.2600 - Binary-Cell-Dice: 0.7543 - Binary-Cell-Jacard: 0.6636 - bPQ-Score: 0.5659 - mPQ-Score: 0.4177 - Tissue-MC-Acc.: 0.0262
2023-09-23 19:36:49,959 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:36:49,959 [INFO] - Epoch: 85/130
2023-09-23 19:39:51,928 [INFO] - Training epoch stats:     Loss: 3.1853 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0305
2023-09-23 19:43:19,404 [INFO] - Validation epoch stats:   Loss: 3.2625 - Binary-Cell-Dice: 0.7486 - Binary-Cell-Jacard: 0.6577 - bPQ-Score: 0.5620 - mPQ-Score: 0.4120 - Tissue-MC-Acc.: 0.0285
2023-09-23 19:43:29,147 [DEBUG] - Old lr: 0.000050 - New lr: 0.000050
2023-09-23 19:43:29,147 [INFO] - Epoch: 86/130
2023-09-23 19:46:19,782 [INFO] - Training epoch stats:     Loss: 3.1860 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0312
2023-09-23 19:49:57,491 [INFO] - Validation epoch stats:   Loss: 3.2351 - Binary-Cell-Dice: 0.7541 - Binary-Cell-Jacard: 0.6643 - bPQ-Score: 0.5650 - mPQ-Score: 0.4182 - Tissue-MC-Acc.: 0.0277
2023-09-23 19:50:29,316 [DEBUG] - Old lr: 0.000050 - New lr: 0.000025
2023-09-23 19:50:29,317 [INFO] - Epoch: 87/130
2023-09-23 19:53:30,745 [INFO] - Training epoch stats:     Loss: 3.1746 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0324
2023-09-23 19:57:00,450 [INFO] - Validation epoch stats:   Loss: 3.2295 - Binary-Cell-Dice: 0.7560 - Binary-Cell-Jacard: 0.6672 - bPQ-Score: 0.5708 - mPQ-Score: 0.4261 - Tissue-MC-Acc.: 0.0273
2023-09-23 19:57:29,422 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 19:57:29,423 [INFO] - Epoch: 88/130
2023-09-23 20:00:28,620 [INFO] - Training epoch stats:     Loss: 3.1641 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0294
2023-09-23 20:04:02,315 [INFO] - Validation epoch stats:   Loss: 3.2221 - Binary-Cell-Dice: 0.7574 - Binary-Cell-Jacard: 0.6691 - bPQ-Score: 0.5731 - mPQ-Score: 0.4300 - Tissue-MC-Acc.: 0.0246
2023-09-23 20:04:02,328 [INFO] - New best model - save checkpoint
2023-09-23 20:04:17,546 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:04:17,547 [INFO] - Epoch: 89/130
2023-09-23 20:07:19,201 [INFO] - Training epoch stats:     Loss: 3.1537 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 20:11:04,415 [INFO] - Validation epoch stats:   Loss: 3.2293 - Binary-Cell-Dice: 0.7546 - Binary-Cell-Jacard: 0.6656 - bPQ-Score: 0.5682 - mPQ-Score: 0.4272 - Tissue-MC-Acc.: 0.0230
2023-09-23 20:11:32,518 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:11:32,519 [INFO] - Epoch: 90/130
2023-09-23 20:14:32,289 [INFO] - Training epoch stats:     Loss: 3.1583 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0294
2023-09-23 20:17:56,816 [INFO] - Validation epoch stats:   Loss: 3.2168 - Binary-Cell-Dice: 0.7549 - Binary-Cell-Jacard: 0.6660 - bPQ-Score: 0.5696 - mPQ-Score: 0.4276 - Tissue-MC-Acc.: 0.0230
2023-09-23 20:18:06,682 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:18:06,683 [INFO] - Epoch: 91/130
2023-09-23 20:21:10,292 [INFO] - Training epoch stats:     Loss: 3.1662 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 20:24:45,508 [INFO] - Validation epoch stats:   Loss: 3.2209 - Binary-Cell-Dice: 0.7572 - Binary-Cell-Jacard: 0.6688 - bPQ-Score: 0.5720 - mPQ-Score: 0.4338 - Tissue-MC-Acc.: 0.0246
2023-09-23 20:25:10,338 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:25:10,338 [INFO] - Epoch: 92/130
2023-09-23 20:28:13,841 [INFO] - Training epoch stats:     Loss: 3.1474 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 20:31:45,345 [INFO] - Validation epoch stats:   Loss: 3.2169 - Binary-Cell-Dice: 0.7498 - Binary-Cell-Jacard: 0.6597 - bPQ-Score: 0.5677 - mPQ-Score: 0.4274 - Tissue-MC-Acc.: 0.0273
2023-09-23 20:31:53,080 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:31:53,080 [INFO] - Epoch: 93/130
2023-09-23 20:35:00,773 [INFO] - Training epoch stats:     Loss: 3.1502 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0267
2023-09-23 20:38:39,577 [INFO] - Validation epoch stats:   Loss: 3.2279 - Binary-Cell-Dice: 0.7542 - Binary-Cell-Jacard: 0.6649 - bPQ-Score: 0.5705 - mPQ-Score: 0.4311 - Tissue-MC-Acc.: 0.0273
2023-09-23 20:38:50,699 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:38:50,700 [INFO] - Epoch: 94/130
2023-09-23 20:41:43,638 [INFO] - Training epoch stats:     Loss: 3.1514 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 20:45:24,624 [INFO] - Validation epoch stats:   Loss: 3.2255 - Binary-Cell-Dice: 0.7499 - Binary-Cell-Jacard: 0.6588 - bPQ-Score: 0.5680 - mPQ-Score: 0.4305 - Tissue-MC-Acc.: 0.0277
2023-09-23 20:45:46,145 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:45:46,145 [INFO] - Epoch: 95/130
2023-09-23 20:49:02,216 [INFO] - Training epoch stats:     Loss: 3.1492 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0275
2023-09-23 20:52:34,112 [INFO] - Validation epoch stats:   Loss: 3.2172 - Binary-Cell-Dice: 0.7551 - Binary-Cell-Jacard: 0.6664 - bPQ-Score: 0.5715 - mPQ-Score: 0.4328 - Tissue-MC-Acc.: 0.0246
2023-09-23 20:52:41,594 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:52:41,594 [INFO] - Epoch: 96/130
2023-09-23 20:55:41,804 [INFO] - Training epoch stats:     Loss: 3.1488 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0256
2023-09-23 20:59:16,761 [INFO] - Validation epoch stats:   Loss: 3.2240 - Binary-Cell-Dice: 0.7542 - Binary-Cell-Jacard: 0.6646 - bPQ-Score: 0.5678 - mPQ-Score: 0.4283 - Tissue-MC-Acc.: 0.0246
2023-09-23 20:59:31,756 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 20:59:31,757 [INFO] - Epoch: 97/130
2023-09-23 21:02:49,930 [INFO] - Training epoch stats:     Loss: 3.1484 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0252
2023-09-23 21:06:23,894 [INFO] - Validation epoch stats:   Loss: 3.2166 - Binary-Cell-Dice: 0.7581 - Binary-Cell-Jacard: 0.6697 - bPQ-Score: 0.5737 - mPQ-Score: 0.4317 - Tissue-MC-Acc.: 0.0250
2023-09-23 21:06:23,899 [INFO] - New best model - save checkpoint
2023-09-23 21:06:48,644 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:06:48,645 [INFO] - Epoch: 98/130
2023-09-23 21:09:42,308 [INFO] - Training epoch stats:     Loss: 3.1431 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0252
2023-09-23 21:13:20,937 [INFO] - Validation epoch stats:   Loss: 3.2172 - Binary-Cell-Dice: 0.7566 - Binary-Cell-Jacard: 0.6679 - bPQ-Score: 0.5707 - mPQ-Score: 0.4326 - Tissue-MC-Acc.: 0.0258
2023-09-23 21:13:30,666 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:13:30,667 [INFO] - Epoch: 99/130
2023-09-23 21:16:36,584 [INFO] - Training epoch stats:     Loss: 3.1420 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0286
2023-09-23 21:20:16,031 [INFO] - Validation epoch stats:   Loss: 3.2175 - Binary-Cell-Dice: 0.7533 - Binary-Cell-Jacard: 0.6634 - bPQ-Score: 0.5718 - mPQ-Score: 0.4294 - Tissue-MC-Acc.: 0.0321
2023-09-23 21:20:38,444 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:20:38,444 [INFO] - Epoch: 100/130
2023-09-23 21:24:00,263 [INFO] - Training epoch stats:     Loss: 3.1450 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0294
2023-09-23 21:27:34,498 [INFO] - Validation epoch stats:   Loss: 3.2143 - Binary-Cell-Dice: 0.7538 - Binary-Cell-Jacard: 0.6647 - bPQ-Score: 0.5693 - mPQ-Score: 0.4300 - Tissue-MC-Acc.: 0.0301
2023-09-23 21:27:43,467 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:27:43,468 [INFO] - Epoch: 101/130
2023-09-23 21:30:48,182 [INFO] - Training epoch stats:     Loss: 3.1403 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0271
2023-09-23 21:34:27,440 [INFO] - Validation epoch stats:   Loss: 3.2240 - Binary-Cell-Dice: 0.7609 - Binary-Cell-Jacard: 0.6731 - bPQ-Score: 0.5749 - mPQ-Score: 0.4329 - Tissue-MC-Acc.: 0.0270
2023-09-23 21:34:27,444 [INFO] - New best model - save checkpoint
2023-09-23 21:35:13,801 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:35:13,801 [INFO] - Epoch: 102/130
2023-09-23 21:38:07,644 [INFO] - Training epoch stats:     Loss: 3.1395 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0275
2023-09-23 21:41:49,322 [INFO] - Validation epoch stats:   Loss: 3.2135 - Binary-Cell-Dice: 0.7566 - Binary-Cell-Jacard: 0.6678 - bPQ-Score: 0.5734 - mPQ-Score: 0.4336 - Tissue-MC-Acc.: 0.0285
2023-09-23 21:42:00,340 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:42:00,341 [INFO] - Epoch: 103/130
2023-09-23 21:44:59,306 [INFO] - Training epoch stats:     Loss: 3.1325 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 21:48:35,912 [INFO] - Validation epoch stats:   Loss: 3.2194 - Binary-Cell-Dice: 0.7558 - Binary-Cell-Jacard: 0.6671 - bPQ-Score: 0.5721 - mPQ-Score: 0.4285 - Tissue-MC-Acc.: 0.0305
2023-09-23 21:48:57,727 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:48:57,728 [INFO] - Epoch: 104/130
2023-09-23 21:51:53,544 [INFO] - Training epoch stats:     Loss: 3.1429 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0335
2023-09-23 21:55:31,946 [INFO] - Validation epoch stats:   Loss: 3.2125 - Binary-Cell-Dice: 0.7594 - Binary-Cell-Jacard: 0.6718 - bPQ-Score: 0.5719 - mPQ-Score: 0.4343 - Tissue-MC-Acc.: 0.0325
2023-09-23 21:55:43,038 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 21:55:43,039 [INFO] - Epoch: 105/130
2023-09-23 21:58:43,281 [INFO] - Training epoch stats:     Loss: 3.1374 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 22:02:20,957 [INFO] - Validation epoch stats:   Loss: 3.2165 - Binary-Cell-Dice: 0.7564 - Binary-Cell-Jacard: 0.6676 - bPQ-Score: 0.5698 - mPQ-Score: 0.4322 - Tissue-MC-Acc.: 0.0305
2023-09-23 22:02:28,939 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:02:28,940 [INFO] - Epoch: 106/130
2023-09-23 22:05:34,603 [INFO] - Training epoch stats:     Loss: 3.1322 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0312
2023-09-23 22:09:12,304 [INFO] - Validation epoch stats:   Loss: 3.2163 - Binary-Cell-Dice: 0.7600 - Binary-Cell-Jacard: 0.6712 - bPQ-Score: 0.5748 - mPQ-Score: 0.4327 - Tissue-MC-Acc.: 0.0325
2023-09-23 22:09:45,443 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:09:45,444 [INFO] - Epoch: 107/130
2023-09-23 22:12:41,674 [INFO] - Training epoch stats:     Loss: 3.1335 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 22:16:22,706 [INFO] - Validation epoch stats:   Loss: 3.2169 - Binary-Cell-Dice: 0.7596 - Binary-Cell-Jacard: 0.6719 - bPQ-Score: 0.5729 - mPQ-Score: 0.4336 - Tissue-MC-Acc.: 0.0289
2023-09-23 22:16:34,075 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:16:34,075 [INFO] - Epoch: 108/130
2023-09-23 22:19:32,047 [INFO] - Training epoch stats:     Loss: 3.1326 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0290
2023-09-23 22:23:09,683 [INFO] - Validation epoch stats:   Loss: 3.2160 - Binary-Cell-Dice: 0.7553 - Binary-Cell-Jacard: 0.6655 - bPQ-Score: 0.5712 - mPQ-Score: 0.4306 - Tissue-MC-Acc.: 0.0270
2023-09-23 22:23:26,185 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:23:26,186 [INFO] - Epoch: 109/130
2023-09-23 22:26:21,334 [INFO] - Training epoch stats:     Loss: 3.1373 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0256
2023-09-23 22:30:00,323 [INFO] - Validation epoch stats:   Loss: 3.2183 - Binary-Cell-Dice: 0.7632 - Binary-Cell-Jacard: 0.6762 - bPQ-Score: 0.5731 - mPQ-Score: 0.4371 - Tissue-MC-Acc.: 0.0250
2023-09-23 22:30:07,760 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:30:07,761 [INFO] - Epoch: 110/130
2023-09-23 22:33:04,228 [INFO] - Training epoch stats:     Loss: 3.1328 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 22:36:45,118 [INFO] - Validation epoch stats:   Loss: 3.2214 - Binary-Cell-Dice: 0.7594 - Binary-Cell-Jacard: 0.6690 - bPQ-Score: 0.5697 - mPQ-Score: 0.4340 - Tissue-MC-Acc.: 0.0270
2023-09-23 22:36:54,625 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:36:54,625 [INFO] - Epoch: 111/130
2023-09-23 22:40:01,007 [INFO] - Training epoch stats:     Loss: 3.1240 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 22:43:34,387 [INFO] - Validation epoch stats:   Loss: 3.2132 - Binary-Cell-Dice: 0.7596 - Binary-Cell-Jacard: 0.6714 - bPQ-Score: 0.5717 - mPQ-Score: 0.4362 - Tissue-MC-Acc.: 0.0277
2023-09-23 22:44:00,548 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:44:00,548 [INFO] - Epoch: 112/130
2023-09-23 22:47:02,773 [INFO] - Training epoch stats:     Loss: 3.1341 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0252
2023-09-23 22:50:44,302 [INFO] - Validation epoch stats:   Loss: 3.2099 - Binary-Cell-Dice: 0.7588 - Binary-Cell-Jacard: 0.6708 - bPQ-Score: 0.5688 - mPQ-Score: 0.4345 - Tissue-MC-Acc.: 0.0273
2023-09-23 22:50:53,286 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:50:53,287 [INFO] - Epoch: 113/130
2023-09-23 22:53:44,497 [INFO] - Training epoch stats:     Loss: 3.1257 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0282
2023-09-23 22:57:31,435 [INFO] - Validation epoch stats:   Loss: 3.2195 - Binary-Cell-Dice: 0.7604 - Binary-Cell-Jacard: 0.6724 - bPQ-Score: 0.5742 - mPQ-Score: 0.4326 - Tissue-MC-Acc.: 0.0234
2023-09-23 22:57:48,573 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 22:57:48,574 [INFO] - Epoch: 114/130
2023-09-23 23:00:39,069 [INFO] - Training epoch stats:     Loss: 3.1315 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0320
2023-09-23 23:04:11,786 [INFO] - Validation epoch stats:   Loss: 3.2089 - Binary-Cell-Dice: 0.7588 - Binary-Cell-Jacard: 0.6712 - bPQ-Score: 0.5751 - mPQ-Score: 0.4338 - Tissue-MC-Acc.: 0.0258
2023-09-23 23:04:11,796 [INFO] - New best model - save checkpoint
2023-09-23 23:05:06,392 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:05:06,393 [INFO] - Epoch: 115/130
2023-09-23 23:08:06,162 [INFO] - Training epoch stats:     Loss: 3.1274 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0324
2023-09-23 23:11:39,413 [INFO] - Validation epoch stats:   Loss: 3.2110 - Binary-Cell-Dice: 0.7576 - Binary-Cell-Jacard: 0.6701 - bPQ-Score: 0.5749 - mPQ-Score: 0.4347 - Tissue-MC-Acc.: 0.0277
2023-09-23 23:11:48,081 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:11:48,082 [INFO] - Epoch: 116/130
2023-09-23 23:14:46,261 [INFO] - Training epoch stats:     Loss: 3.1276 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0305
2023-09-23 23:18:21,498 [INFO] - Validation epoch stats:   Loss: 3.2209 - Binary-Cell-Dice: 0.7584 - Binary-Cell-Jacard: 0.6707 - bPQ-Score: 0.5719 - mPQ-Score: 0.4338 - Tissue-MC-Acc.: 0.0305
2023-09-23 23:18:48,538 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:18:48,539 [INFO] - Epoch: 117/130
2023-09-23 23:21:56,105 [INFO] - Training epoch stats:     Loss: 3.1222 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0320
2023-09-23 23:25:31,162 [INFO] - Validation epoch stats:   Loss: 3.2292 - Binary-Cell-Dice: 0.7567 - Binary-Cell-Jacard: 0.6680 - bPQ-Score: 0.5716 - mPQ-Score: 0.4314 - Tissue-MC-Acc.: 0.0321
2023-09-23 23:25:44,877 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:25:44,877 [INFO] - Epoch: 118/130
2023-09-23 23:28:40,984 [INFO] - Training epoch stats:     Loss: 3.1200 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0271
2023-09-23 23:32:18,958 [INFO] - Validation epoch stats:   Loss: 3.2182 - Binary-Cell-Dice: 0.7604 - Binary-Cell-Jacard: 0.6721 - bPQ-Score: 0.5753 - mPQ-Score: 0.4350 - Tissue-MC-Acc.: 0.0289
2023-09-23 23:32:19,142 [INFO] - New best model - save checkpoint
2023-09-23 23:33:20,135 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:33:20,135 [INFO] - Epoch: 119/130
2023-09-23 23:36:15,485 [INFO] - Training epoch stats:     Loss: 3.1323 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 23:39:50,182 [INFO] - Validation epoch stats:   Loss: 3.2180 - Binary-Cell-Dice: 0.7654 - Binary-Cell-Jacard: 0.6787 - bPQ-Score: 0.5768 - mPQ-Score: 0.4358 - Tissue-MC-Acc.: 0.0270
2023-09-23 23:39:50,284 [INFO] - New best model - save checkpoint
2023-09-23 23:40:29,205 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:40:29,206 [INFO] - Epoch: 120/130
2023-09-23 23:43:13,912 [INFO] - Training epoch stats:     Loss: 3.1211 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-23 23:46:41,714 [INFO] - Validation epoch stats:   Loss: 3.2160 - Binary-Cell-Dice: 0.7575 - Binary-Cell-Jacard: 0.6684 - bPQ-Score: 0.5717 - mPQ-Score: 0.4307 - Tissue-MC-Acc.: 0.0281
2023-09-23 23:46:55,010 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:46:55,011 [INFO] - Epoch: 121/130
2023-09-23 23:49:52,028 [INFO] - Training epoch stats:     Loss: 3.1241 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0297
2023-09-23 23:53:26,932 [INFO] - Validation epoch stats:   Loss: 3.2185 - Binary-Cell-Dice: 0.7581 - Binary-Cell-Jacard: 0.6699 - bPQ-Score: 0.5765 - mPQ-Score: 0.4356 - Tissue-MC-Acc.: 0.0309
2023-09-23 23:53:34,124 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-23 23:53:34,125 [INFO] - Epoch: 122/130
2023-09-23 23:56:26,884 [INFO] - Training epoch stats:     Loss: 3.1213 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0316
2023-09-23 23:59:59,944 [INFO] - Validation epoch stats:   Loss: 3.2193 - Binary-Cell-Dice: 0.7620 - Binary-Cell-Jacard: 0.6749 - bPQ-Score: 0.5702 - mPQ-Score: 0.4353 - Tissue-MC-Acc.: 0.0258
2023-09-24 00:00:27,329 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:00:27,330 [INFO] - Epoch: 123/130
2023-09-24 00:03:17,635 [INFO] - Training epoch stats:     Loss: 3.1181 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-24 00:06:47,495 [INFO] - Validation epoch stats:   Loss: 3.2207 - Binary-Cell-Dice: 0.7584 - Binary-Cell-Jacard: 0.6693 - bPQ-Score: 0.5707 - mPQ-Score: 0.4285 - Tissue-MC-Acc.: 0.0273
2023-09-24 00:06:55,297 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:06:55,297 [INFO] - Epoch: 124/130
2023-09-24 00:09:57,347 [INFO] - Training epoch stats:     Loss: 3.1223 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0328
2023-09-24 00:13:24,925 [INFO] - Validation epoch stats:   Loss: 3.2166 - Binary-Cell-Dice: 0.7594 - Binary-Cell-Jacard: 0.6716 - bPQ-Score: 0.5726 - mPQ-Score: 0.4351 - Tissue-MC-Acc.: 0.0238
2023-09-24 00:13:33,318 [DEBUG] - Old lr: 0.000025 - New lr: 0.000025
2023-09-24 00:13:33,318 [INFO] - Epoch: 125/130
2023-09-24 00:16:32,726 [INFO] - Training epoch stats:     Loss: 3.1188 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0309
2023-09-24 00:20:05,116 [INFO] - Validation epoch stats:   Loss: 3.2149 - Binary-Cell-Dice: 0.7565 - Binary-Cell-Jacard: 0.6676 - bPQ-Score: 0.5679 - mPQ-Score: 0.4301 - Tissue-MC-Acc.: 0.0250
2023-09-24 00:20:28,363 [DEBUG] - Old lr: 0.000025 - New lr: 0.000013
2023-09-24 00:20:28,363 [INFO] - Epoch: 126/130
2023-09-24 00:23:21,950 [INFO] - Training epoch stats:     Loss: 3.1117 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0282
2023-09-24 00:26:52,627 [INFO] - Validation epoch stats:   Loss: 3.2085 - Binary-Cell-Dice: 0.7536 - Binary-Cell-Jacard: 0.6640 - bPQ-Score: 0.5716 - mPQ-Score: 0.4346 - Tissue-MC-Acc.: 0.0258
2023-09-24 00:27:02,174 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-24 00:27:02,175 [INFO] - Epoch: 127/130
2023-09-24 00:30:01,565 [INFO] - Training epoch stats:     Loss: 3.1068 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0256
2023-09-24 00:33:31,232 [INFO] - Validation epoch stats:   Loss: 3.2092 - Binary-Cell-Dice: 0.7579 - Binary-Cell-Jacard: 0.6701 - bPQ-Score: 0.5740 - mPQ-Score: 0.4356 - Tissue-MC-Acc.: 0.0246
2023-09-24 00:33:43,631 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-24 00:33:43,632 [INFO] - Epoch: 128/130
2023-09-24 00:36:43,062 [INFO] - Training epoch stats:     Loss: 3.1131 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0320
2023-09-24 00:40:14,180 [INFO] - Validation epoch stats:   Loss: 3.2154 - Binary-Cell-Dice: 0.7602 - Binary-Cell-Jacard: 0.6724 - bPQ-Score: 0.5764 - mPQ-Score: 0.4344 - Tissue-MC-Acc.: 0.0254
2023-09-24 00:40:40,315 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-24 00:40:40,316 [INFO] - Epoch: 129/130
2023-09-24 00:43:40,788 [INFO] - Training epoch stats:     Loss: 3.1138 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0312
2023-09-24 00:47:09,081 [INFO] - Validation epoch stats:   Loss: 3.2108 - Binary-Cell-Dice: 0.7558 - Binary-Cell-Jacard: 0.6673 - bPQ-Score: 0.5696 - mPQ-Score: 0.4351 - Tissue-MC-Acc.: 0.0242
2023-09-24 00:47:19,349 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-24 00:47:19,349 [INFO] - Epoch: 130/130
2023-09-24 00:50:23,822 [INFO] - Training epoch stats:     Loss: 3.1062 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0286
2023-09-24 00:53:55,105 [INFO] - Validation epoch stats:   Loss: 3.2105 - Binary-Cell-Dice: 0.7586 - Binary-Cell-Jacard: 0.6715 - bPQ-Score: 0.5750 - mPQ-Score: 0.4370 - Tissue-MC-Acc.: 0.0273
2023-09-24 00:54:02,201 [DEBUG] - Old lr: 0.000013 - New lr: 0.000013
2023-09-24 00:54:02,212 [INFO] -
