2023-09-22 14:33:48,338 [INFO] - Instantiated Logger. WandB init and config update finished.
2023-09-22 14:33:48,412 [INFO] - Cuda devices: [<torch.cuda.device object at 0x7effa0563c70>]
2023-09-22 14:33:48,413 [INFO] - Using GPU: cuda:0
2023-09-22 14:33:48,413 [INFO] - Using device: cuda:0
2023-09-22 14:33:48,414 [INFO] - Loss functions:
2023-09-22 14:33:48,414 [INFO] - {'dist_map': {'bceweighted': {'loss_fn': BCEWithLogitsLoss(), 'weight': 1}}, 'stardist_map': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'stardist_map_refined': {'L1LossWeighted': {'loss_fn': L1LossWeighted(), 'weight': 1}}, 'nuclei_type_map': {'bce': {'loss_fn': <PERSON>entropy<PERSON>oss(), 'weight': 0.5}, 'dice': {'loss_fn': DiceLoss(), 'weight': 0.2}, 'mcfocaltverskyloss': {'loss_fn': MCFocalTverskyLoss(), 'weight': 0.5}}}
2023-09-22 14:33:50,716 [INFO] - Loaded CellVit256 model
2023-09-22 14:33:50,718 [INFO] -
Model: CellViT256CPP(
  (encoder): ViTCellViT(
    (patch_embed): PatchEmbed(
      (proj): Conv2d(3, 384, kernel_size=(16, 16), stride=(16, 16))
    )
    (pos_drop): Dropout(p=0, inplace=False)
    (blocks): ModuleList(
      (0): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): Identity()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (1): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (2): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (3): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (4): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (5): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (6): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (7): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (8): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (9): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (10): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
      (11): Block(
        (norm1): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (attn): Attention(
          (qkv): Linear(in_features=384, out_features=1152, bias=True)
          (attn_drop): Dropout(p=0.1, inplace=False)
          (proj): Linear(in_features=384, out_features=384, bias=True)
          (proj_drop): Dropout(p=0, inplace=False)
        )
        (drop_path): DropPath()
        (norm2): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
        (mlp): Mlp(
          (fc1): Linear(in_features=384, out_features=1536, bias=True)
          (act): GELU(approximate='none')
          (fc2): Linear(in_features=1536, out_features=384, bias=True)
          (drop): Dropout(p=0, inplace=False)
        )
      )
    )
    (norm): LayerNorm((384,), eps=1e-06, elementwise_affine=True)
    (head): Linear(in_features=384, out_features=19, bias=True)
  )
  (decoder0): Sequential(
    (0): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(3, 32, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
    (1): Conv2DBlock(
      (block): Sequential(
        (0): Conv2d(32, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (2): ReLU(inplace=True)
        (3): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder1): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (2): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(128, 128, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder2): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
    (1): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(256, 256, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (decoder3): Sequential(
    (0): Deconv2DBlock(
      (block): Sequential(
        (0): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
        (1): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (2): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
        (3): ReLU(inplace=True)
        (4): Dropout(p=0, inplace=False)
      )
    )
  )
  (stardist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (dist_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (nuclei_type_maps_decoder): Sequential(
    (bottleneck_upsampler): ConvTranspose2d(384, 312, kernel_size=(2, 2), stride=(2, 2))
    (decoder3_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(624, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(312, 312, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(312, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (3): ConvTranspose2d(312, 256, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder2_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(512, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(256, 128, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder1_upsampler): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): ConvTranspose2d(128, 64, kernel_size=(2, 2), stride=(2, 2))
    )
    (decoder0_header): Sequential(
      (0): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(128, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (1): Conv2DBlock(
        (block): Sequential(
          (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
          (1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
          (2): ReLU(inplace=True)
          (3): Dropout(p=0, inplace=False)
        )
      )
      (2): Conv2d(64, 32, kernel_size=(1, 1), stride=(1, 1))
    )
  )
  (stardist_head): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (dist_head): Conv2d(32, 1, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (type_head): Conv2d(32, 6, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (classifier_head): Linear(in_features=256, out_features=19, bias=True)
  (conv_0_confidence): Conv2d(32, 32, kernel_size=(1, 1), stride=(1, 1), bias=False)
  (conv_1_confidence): Conv2d(6, 6, kernel_size=(1, 1), stride=(1, 1))
  (sampling_features): SamplingFeatures()
  (final_activation_ray): ReLU(inplace=True)
)
2023-09-22 14:33:51,531 [INFO] -
===============================================================================================
Layer (type:depth-idx)                        Output Shape              Param #
===============================================================================================
CellViT256CPP                                 [1, 19]                   6,802,595
├─ViTCellViT: 1-1                             [1, 19]                   76,032
│    └─PatchEmbed: 2-1                        [1, 256, 384]             --
│    │    └─Conv2d: 3-1                       [1, 384, 16, 16]          (295,296)
│    └─Dropout: 2-2                           [1, 257, 384]             --
│    └─ModuleList: 2-3                        --                        --
│    │    └─Block: 3-2                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-3                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-4                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-5                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-6                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-7                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-8                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-9                        [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-10                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-11                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-12                       [1, 257, 384]             (1,774,464)
│    │    └─Block: 3-13                       [1, 257, 384]             (1,774,464)
│    └─LayerNorm: 2-4                         [1, 257, 384]             (768)
│    └─Linear: 2-5                            [1, 19]                   7,315
├─Sequential: 1-10                            --                        (recursive)
│    └─ConvTranspose2d: 2-6                   [1, 312, 32, 32]          479,544
├─Sequential: 1-3                             [1, 312, 32, 32]          --
│    └─Deconv2DBlock: 2-7                     [1, 312, 32, 32]          --
│    │    └─Sequential: 3-14                  [1, 312, 32, 32]          1,356,576
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-8                        [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-15                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-16                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-17                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-18             [1, 256, 64, 64]          319,744
├─Sequential: 1-5                             [1, 256, 64, 64]          --
│    └─Deconv2DBlock: 2-9                     [1, 256, 32, 32]          --
│    │    └─Sequential: 3-19                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-10                    [1, 256, 64, 64]          --
│    │    └─Sequential: 3-20                  [1, 256, 64, 64]          852,992
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-11                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-21                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-22                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-23             [1, 128, 128, 128]        131,200
├─Sequential: 1-7                             [1, 128, 128, 128]        --
│    └─Deconv2DBlock: 2-12                    [1, 256, 32, 32]          --
│    │    └─Sequential: 3-24                  [1, 256, 32, 32]          984,064
│    └─Deconv2DBlock: 2-13                    [1, 128, 64, 64]          --
│    │    └─Sequential: 3-25                  [1, 128, 64, 64]          279,040
│    └─Deconv2DBlock: 2-14                    [1, 128, 128, 128]        --
│    │    └─Sequential: 3-26                  [1, 128, 128, 128]        213,504
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-15                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-27                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-28                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-29             [1, 64, 256, 256]         32,832
├─Sequential: 1-9                             [1, 64, 256, 256]         --
│    └─Conv2DBlock: 2-16                      [1, 32, 256, 256]         --
│    │    └─Sequential: 3-30                  [1, 32, 256, 256]         960
│    └─Conv2DBlock: 2-17                      [1, 64, 256, 256]         --
│    │    └─Sequential: 3-31                  [1, 64, 256, 256]         18,624
├─Sequential: 1-10                            --                        (recursive)
│    └─Sequential: 2-18                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-32                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-33                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-34                      [1, 32, 256, 256]         2,080
├─Sequential: 1-27                            --                        (recursive)
│    └─ConvTranspose2d: 2-19                  [1, 312, 32, 32]          479,544
├─Sequential: 1-12                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-20                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-35                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-21                       [1, 256, 64, 64]          --
│    │    └─Conv2DBlock: 3-36                 [1, 312, 32, 32]          1,753,128
│    │    └─Conv2DBlock: 3-37                 [1, 312, 32, 32]          877,032
│    │    └─Conv2DBlock: 3-38                 [1, 312, 32, 32]          877,032
│    │    └─ConvTranspose2d: 3-39             [1, 256, 64, 64]          319,744
├─Sequential: 1-14                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-22                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-40                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-23                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-41                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-24                       [1, 128, 128, 128]        --
│    │    └─Conv2DBlock: 3-42                 [1, 256, 64, 64]          1,180,416
│    │    └─Conv2DBlock: 3-43                 [1, 256, 64, 64]          590,592
│    │    └─ConvTranspose2d: 3-44             [1, 128, 128, 128]        131,200
├─Sequential: 1-16                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-25                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-45                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-26                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-46                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-27                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-47                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-28                       [1, 64, 256, 256]         --
│    │    └─Conv2DBlock: 3-48                 [1, 128, 128, 128]        295,296
│    │    └─Conv2DBlock: 3-49                 [1, 128, 128, 128]        147,840
│    │    └─ConvTranspose2d: 3-50             [1, 64, 256, 256]         32,832
├─Sequential: 1-18                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-29                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-51                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-30                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-52                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-31                       [1, 32, 256, 256]         --
│    │    └─Conv2DBlock: 3-53                 [1, 64, 256, 256]         73,920
│    │    └─Conv2DBlock: 3-54                 [1, 64, 256, 256]         37,056
│    │    └─Conv2d: 3-55                      [1, 32, 256, 256]         2,080
│    └─ConvTranspose2d: 2-32                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-20                            [1, 312, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-33                    [1, 312, 32, 32]          (recursive)
│    │    └─Sequential: 3-56                  [1, 312, 32, 32]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-34                       [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-57                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-58                 [1, 312, 32, 32]          (recursive)
│    │    └─Conv2DBlock: 3-59                 [1, 312, 32, 32]          (recursive)
│    │    └─ConvTranspose2d: 3-60             [1, 256, 64, 64]          (recursive)
├─Sequential: 1-22                            [1, 256, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-35                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-61                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-36                    [1, 256, 64, 64]          (recursive)
│    │    └─Sequential: 3-62                  [1, 256, 64, 64]          (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-37                       [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-63                 [1, 256, 64, 64]          (recursive)
│    │    └─Conv2DBlock: 3-64                 [1, 256, 64, 64]          (recursive)
│    │    └─ConvTranspose2d: 3-65             [1, 128, 128, 128]        (recursive)
├─Sequential: 1-24                            [1, 128, 128, 128]        (recursive)
│    └─Deconv2DBlock: 2-38                    [1, 256, 32, 32]          (recursive)
│    │    └─Sequential: 3-66                  [1, 256, 32, 32]          (recursive)
│    └─Deconv2DBlock: 2-39                    [1, 128, 64, 64]          (recursive)
│    │    └─Sequential: 3-67                  [1, 128, 64, 64]          (recursive)
│    └─Deconv2DBlock: 2-40                    [1, 128, 128, 128]        (recursive)
│    │    └─Sequential: 3-68                  [1, 128, 128, 128]        (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-41                       [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-69                 [1, 128, 128, 128]        (recursive)
│    │    └─Conv2DBlock: 3-70                 [1, 128, 128, 128]        (recursive)
│    │    └─ConvTranspose2d: 3-71             [1, 64, 256, 256]         (recursive)
├─Sequential: 1-26                            [1, 64, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-42                      [1, 32, 256, 256]         (recursive)
│    │    └─Sequential: 3-72                  [1, 32, 256, 256]         (recursive)
│    └─Conv2DBlock: 2-43                      [1, 64, 256, 256]         (recursive)
│    │    └─Sequential: 3-73                  [1, 64, 256, 256]         (recursive)
├─Sequential: 1-27                            --                        (recursive)
│    └─Sequential: 2-44                       [1, 32, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-74                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2DBlock: 3-75                 [1, 64, 256, 256]         (recursive)
│    │    └─Conv2d: 3-76                      [1, 32, 256, 256]         (recursive)
├─Conv2d: 1-28                                [1, 32, 256, 256]         1,024
├─Conv2d: 1-29                                [1, 1, 256, 256]          32
├─Conv2d: 1-30                                [1, 6, 256, 256]          192
├─Conv2d: 1-31                                [1, 32, 256, 256]         1,024
├─SamplingFeatures: 1-32                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-33                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-34                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-35                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-36                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-37                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-38                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-39                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-40                      [1, 32, 256, 256]         --
├─SamplingFeatures: 1-41                      [1, 32, 256, 256]         --
├─Conv2d: 1-42                                [32, 6, 256, 256]         42
├─ReLU: 1-43                                  [1, 32, 256, 256]         --
===============================================================================================
Total params: 46,763,136
Trainable params: 25,097,472
Non-trainable params: 21,665,664
Total mult-adds (G): 133.49
===============================================================================================
Input size (MB): 0.79
Forward/backward pass size (MB): 1855.47
Params size (MB): 159.54
Estimated Total Size (MB): 2015.80
===============================================================================================
2023-09-22 14:33:52,610 [INFO] - Loaded AdamW Optimizer with following hyperparameters:
2023-09-22 14:33:52,618 [INFO] - {'betas': [0.85, 0.95], 'lr': 0.0003, 'weight_decay': 0.0001}
2023-09-22 14:33:52,618 [INFO] - Using early stopping with a range of 130 and maximize strategy
2023-09-22 14:33:52,906 [INFO] - Using Weighted Sampling with strategy: cell+tissue
2023-09-22 14:33:52,908 [INFO] - Unique-Weights: tensor([0.5558, 0.5935, 0.5950, 0.6870, 0.6991, 0.7247, 0.7262, 0.7300, 0.7314,
        0.7691, 0.8303, 0.8356, 0.8611, 0.8626, 0.8747, 0.9003, 0.9056, 0.9474,
        0.9667, 0.9823, 0.9883, 0.9902, 1.0059, 1.0072, 1.0097, 1.0112, 1.0200,
        1.0215, 1.0260, 1.0278, 1.0367, 1.0449, 1.0464, 1.0536, 1.0910, 1.0913,
        1.0922, 1.0927, 1.1088, 1.1094, 1.1143, 1.1173, 1.1216, 1.1256, 1.1283,
        1.1287, 1.1299, 1.1316, 1.1400, 1.1423, 1.1465, 1.1471, 1.1478, 1.1505,
        1.1520, 1.1526, 1.1538, 1.1550, 1.1565, 1.1579, 1.1624, 1.1639, 1.1643,
        1.1658, 1.1777, 1.1792, 1.1813, 1.1828, 1.1853, 1.1855, 1.1870, 1.1956,
        1.1969, 1.2016, 1.2034, 1.2205, 1.2277, 1.2292, 1.2343, 1.2355, 1.2521,
        1.2527, 1.2576, 1.2580, 1.2595, 1.2606, 1.2621, 1.2651, 1.2663, 1.2666,
        1.2669, 1.2678, 1.2680, 1.2829, 1.2833, 1.2835, 1.2844, 1.2850, 1.2869,
        1.2884, 1.2899, 1.2902, 1.2915, 1.2929, 1.2972, 1.3012, 1.3055, 1.3141,
        1.3156, 1.3165, 1.3219, 1.3221, 1.3276, 1.3282, 1.3294, 1.3321, 1.3380,
        1.3399, 1.3533, 1.3569, 1.3611, 1.3707, 1.3719, 1.3885, 1.3891, 1.3940,
        1.3971, 1.4028, 1.4033, 1.4099, 1.4111, 1.4229, 1.4243, 1.4275, 1.4336,
        1.4377, 1.4407, 1.4419, 1.4432, 1.4436, 1.4455, 1.4585, 1.4591, 1.4625,
        1.4640, 1.4646, 1.4658, 1.4671, 1.4881, 1.4897, 1.4975, 1.5089, 1.5392,
        1.5449, 1.5463, 1.5475, 1.5507, 1.5593, 1.5608, 1.5612, 1.5641, 1.5647,
        1.5696, 1.5702, 1.5746, 1.5939, 1.5953, 1.6031, 1.6174, 1.6871, 1.7110,
        1.7205, 1.7216, 1.7443, 1.7695, 1.8002, 1.8627, 1.8866])
2023-09-22 14:33:52,908 [INFO] - Instantiate Trainer
2023-09-22 14:33:52,908 [INFO] - Calling Trainer Fit
2023-09-22 14:33:52,908 [INFO] - Starting training, total number of epochs: 130
2023-09-22 14:33:52,909 [INFO] - Epoch: 1/130
2023-09-22 14:36:44,145 [INFO] - Training epoch stats:     Loss: 4.4784 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0507
2023-09-22 14:37:11,330 [DEBUG] - Old lr: 0.000300 - New lr: 0.000285
2023-09-22 14:37:11,336 [INFO] - Epoch: 2/130
2023-09-22 14:40:06,120 [INFO] - Training epoch stats:     Loss: 3.7240 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 14:40:27,326 [DEBUG] - Old lr: 0.000285 - New lr: 0.000271
2023-09-22 14:40:27,326 [INFO] - Epoch: 3/130
2023-09-22 14:43:18,296 [INFO] - Training epoch stats:     Loss: 3.5768 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0377
2023-09-22 14:43:40,546 [DEBUG] - Old lr: 0.000271 - New lr: 0.000257
2023-09-22 14:43:40,546 [INFO] - Epoch: 4/130
2023-09-22 14:46:39,222 [INFO] - Training epoch stats:     Loss: 3.5496 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0396
2023-09-22 14:46:44,023 [DEBUG] - Old lr: 0.000257 - New lr: 0.000244
2023-09-22 14:46:44,023 [INFO] - Epoch: 5/130
2023-09-22 14:49:28,125 [INFO] - Training epoch stats:     Loss: 3.4967 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0495
2023-09-22 14:49:41,480 [DEBUG] - Old lr: 0.000244 - New lr: 0.000232
2023-09-22 14:49:41,481 [INFO] - Epoch: 6/130
2023-09-22 14:52:28,533 [INFO] - Training epoch stats:     Loss: 3.4904 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0472
2023-09-22 14:52:46,887 [DEBUG] - Old lr: 0.000232 - New lr: 0.000221
2023-09-22 14:52:46,888 [INFO] - Epoch: 7/130
2023-09-22 14:55:38,829 [INFO] - Training epoch stats:     Loss: 3.4576 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 14:55:52,841 [DEBUG] - Old lr: 0.000221 - New lr: 0.000210
2023-09-22 14:55:52,842 [INFO] - Epoch: 8/130
2023-09-22 14:58:43,573 [INFO] - Training epoch stats:     Loss: 3.4442 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0472
2023-09-22 14:59:00,505 [DEBUG] - Old lr: 0.000210 - New lr: 0.000199
2023-09-22 14:59:00,506 [INFO] - Epoch: 9/130
2023-09-22 15:01:57,102 [INFO] - Training epoch stats:     Loss: 3.4166 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0349
2023-09-22 15:02:01,181 [DEBUG] - Old lr: 0.000199 - New lr: 0.000189
2023-09-22 15:02:01,182 [INFO] - Epoch: 10/130
2023-09-22 15:04:51,285 [INFO] - Training epoch stats:     Loss: 3.4220 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0468
2023-09-22 15:09:46,445 [INFO] - Validation epoch stats:   Loss: 3.3907 - Binary-Cell-Dice: 0.7548 - Binary-Cell-Jacard: 0.6510 - bPQ-Score: 0.5236 - mPQ-Score: 0.3716 - Tissue-MC-Acc.: 0.0192
2023-09-22 15:09:46,450 [INFO] - New best model - save checkpoint
2023-09-22 15:10:08,788 [DEBUG] - Old lr: 0.000189 - New lr: 0.000180
2023-09-22 15:10:08,789 [INFO] - Epoch: 11/130
2023-09-22 15:12:48,417 [INFO] - Training epoch stats:     Loss: 3.4190 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-22 15:12:52,413 [DEBUG] - Old lr: 0.000180 - New lr: 0.000171
2023-09-22 15:12:52,414 [INFO] - Epoch: 12/130
2023-09-22 15:15:36,997 [INFO] - Training epoch stats:     Loss: 3.4140 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 15:15:46,462 [DEBUG] - Old lr: 0.000171 - New lr: 0.000162
2023-09-22 15:15:46,463 [INFO] - Epoch: 13/130
2023-09-22 15:18:30,251 [INFO] - Training epoch stats:     Loss: 3.3845 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0377
2023-09-22 15:18:36,021 [DEBUG] - Old lr: 0.000162 - New lr: 0.000154
2023-09-22 15:18:36,022 [INFO] - Epoch: 14/130
2023-09-22 15:21:16,318 [INFO] - Training epoch stats:     Loss: 3.3827 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-22 15:21:20,505 [DEBUG] - Old lr: 0.000154 - New lr: 0.000146
2023-09-22 15:21:20,506 [INFO] - Epoch: 15/130
2023-09-22 15:23:58,193 [INFO] - Training epoch stats:     Loss: 3.3767 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-22 15:24:04,054 [DEBUG] - Old lr: 0.000146 - New lr: 0.000139
2023-09-22 15:24:04,055 [INFO] - Epoch: 16/130
2023-09-22 15:26:39,434 [INFO] - Training epoch stats:     Loss: 3.3524 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-22 15:26:49,920 [DEBUG] - Old lr: 0.000139 - New lr: 0.000132
2023-09-22 15:26:49,920 [INFO] - Epoch: 17/130
2023-09-22 15:29:32,366 [INFO] - Training epoch stats:     Loss: 3.3501 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0432
2023-09-22 15:29:47,794 [DEBUG] - Old lr: 0.000132 - New lr: 0.000125
2023-09-22 15:29:47,794 [INFO] - Epoch: 18/130
2023-09-22 15:33:07,655 [INFO] - Training epoch stats:     Loss: 3.3578 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-22 15:33:20,318 [DEBUG] - Old lr: 0.000125 - New lr: 0.000119
2023-09-22 15:33:20,319 [INFO] - Epoch: 19/130
2023-09-22 15:35:58,853 [INFO] - Training epoch stats:     Loss: 3.3293 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0333
2023-09-22 15:36:04,785 [DEBUG] - Old lr: 0.000119 - New lr: 0.000113
2023-09-22 15:36:04,786 [INFO] - Epoch: 20/130
2023-09-22 15:38:45,337 [INFO] - Training epoch stats:     Loss: 3.3220 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 15:43:39,258 [INFO] - Validation epoch stats:   Loss: 3.3305 - Binary-Cell-Dice: 0.7746 - Binary-Cell-Jacard: 0.6814 - bPQ-Score: 0.5589 - mPQ-Score: 0.4157 - Tissue-MC-Acc.: 0.0192
2023-09-22 15:43:39,266 [INFO] - New best model - save checkpoint
2023-09-22 15:43:52,700 [DEBUG] - Old lr: 0.000113 - New lr: 0.000108
2023-09-22 15:43:52,701 [INFO] - Epoch: 21/130
2023-09-22 15:46:33,093 [INFO] - Training epoch stats:     Loss: 3.3206 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0460
2023-09-22 15:46:41,780 [DEBUG] - Old lr: 0.000108 - New lr: 0.000102
2023-09-22 15:46:41,780 [INFO] - Epoch: 22/130
2023-09-22 15:49:24,830 [INFO] - Training epoch stats:     Loss: 3.3270 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 15:49:33,611 [DEBUG] - Old lr: 0.000102 - New lr: 0.000097
2023-09-22 15:49:33,612 [INFO] - Epoch: 23/130
2023-09-22 15:52:16,155 [INFO] - Training epoch stats:     Loss: 3.3085 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 15:52:25,097 [DEBUG] - Old lr: 0.000097 - New lr: 0.000092
2023-09-22 15:52:25,098 [INFO] - Epoch: 24/130
2023-09-22 15:55:06,710 [INFO] - Training epoch stats:     Loss: 3.3199 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0444
2023-09-22 15:55:17,113 [DEBUG] - Old lr: 0.000092 - New lr: 0.000088
2023-09-22 15:55:17,113 [INFO] - Epoch: 25/130
2023-09-22 15:58:00,536 [INFO] - Training epoch stats:     Loss: 3.2875 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 15:58:06,467 [DEBUG] - Old lr: 0.000088 - New lr: 0.000083
2023-09-22 15:58:06,468 [INFO] - Epoch: 26/130
2023-09-22 16:00:54,936 [INFO] - Training epoch stats:     Loss: 3.5550 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0345
2023-09-22 16:01:01,933 [DEBUG] - Old lr: 0.000083 - New lr: 0.000079
2023-09-22 16:01:01,934 [INFO] - Epoch: 27/130
2023-09-22 16:03:47,142 [INFO] - Training epoch stats:     Loss: 3.4759 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 16:04:04,621 [DEBUG] - Old lr: 0.000079 - New lr: 0.000075
2023-09-22 16:04:04,622 [INFO] - Epoch: 28/130
2023-09-22 16:06:58,615 [INFO] - Training epoch stats:     Loss: 3.4354 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0301
2023-09-22 16:07:13,988 [DEBUG] - Old lr: 0.000075 - New lr: 0.000071
2023-09-22 16:07:13,989 [INFO] - Epoch: 29/130
2023-09-22 16:10:00,345 [INFO] - Training epoch stats:     Loss: 3.3950 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0317
2023-09-22 16:10:12,411 [DEBUG] - Old lr: 0.000071 - New lr: 0.000068
2023-09-22 16:10:12,413 [INFO] - Epoch: 30/130
2023-09-22 16:13:02,591 [INFO] - Training epoch stats:     Loss: 3.3852 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0384
2023-09-22 16:17:31,558 [INFO] - Validation epoch stats:   Loss: 3.3774 - Binary-Cell-Dice: 0.7640 - Binary-Cell-Jacard: 0.6650 - bPQ-Score: 0.5673 - mPQ-Score: 0.4000 - Tissue-MC-Acc.: 0.0305
2023-09-22 16:17:31,568 [INFO] - New best model - save checkpoint
2023-09-22 16:18:01,539 [DEBUG] - Old lr: 0.000068 - New lr: 0.000064
2023-09-22 16:18:01,540 [INFO] - Epoch: 31/130
2023-09-22 16:20:58,067 [INFO] - Training epoch stats:     Loss: 3.3599 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 16:21:03,957 [DEBUG] - Old lr: 0.000064 - New lr: 0.000061
2023-09-22 16:21:03,957 [INFO] - Epoch: 32/130
2023-09-22 16:23:52,541 [INFO] - Training epoch stats:     Loss: 3.3537 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 16:23:58,684 [DEBUG] - Old lr: 0.000061 - New lr: 0.000058
2023-09-22 16:23:58,685 [INFO] - Epoch: 33/130
2023-09-22 16:26:47,525 [INFO] - Training epoch stats:     Loss: 3.3301 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 16:27:14,302 [DEBUG] - Old lr: 0.000058 - New lr: 0.000055
2023-09-22 16:27:14,303 [INFO] - Epoch: 34/130
2023-09-22 16:30:03,212 [INFO] - Training epoch stats:     Loss: 3.3181 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-22 16:30:22,138 [DEBUG] - Old lr: 0.000055 - New lr: 0.000052
2023-09-22 16:30:22,138 [INFO] - Epoch: 35/130
2023-09-22 16:33:10,549 [INFO] - Training epoch stats:     Loss: 3.3009 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0384
2023-09-22 16:33:16,979 [DEBUG] - Old lr: 0.000052 - New lr: 0.000050
2023-09-22 16:33:16,979 [INFO] - Epoch: 36/130
2023-09-22 16:36:02,328 [INFO] - Training epoch stats:     Loss: 3.2943 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 16:36:09,634 [DEBUG] - Old lr: 0.000050 - New lr: 0.000047
2023-09-22 16:36:09,635 [INFO] - Epoch: 37/130
2023-09-22 16:38:53,299 [INFO] - Training epoch stats:     Loss: 3.2959 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0468
2023-09-22 16:39:08,909 [DEBUG] - Old lr: 0.000047 - New lr: 0.000045
2023-09-22 16:39:08,910 [INFO] - Epoch: 38/130
2023-09-22 16:42:02,233 [INFO] - Training epoch stats:     Loss: 3.2930 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0428
2023-09-22 16:42:27,295 [DEBUG] - Old lr: 0.000045 - New lr: 0.000043
2023-09-22 16:42:27,296 [INFO] - Epoch: 39/130
2023-09-22 16:45:13,896 [INFO] - Training epoch stats:     Loss: 3.2754 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 16:45:28,201 [DEBUG] - Old lr: 0.000043 - New lr: 0.000041
2023-09-22 16:45:28,202 [INFO] - Epoch: 40/130
2023-09-22 16:48:19,086 [INFO] - Training epoch stats:     Loss: 3.2668 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0448
2023-09-22 16:53:45,761 [INFO] - Validation epoch stats:   Loss: 3.2978 - Binary-Cell-Dice: 0.7757 - Binary-Cell-Jacard: 0.6852 - bPQ-Score: 0.5837 - mPQ-Score: 0.4329 - Tissue-MC-Acc.: 0.0264
2023-09-22 16:53:45,771 [INFO] - New best model - save checkpoint
2023-09-22 16:54:11,463 [DEBUG] - Old lr: 0.000041 - New lr: 0.000039
2023-09-22 16:54:11,464 [INFO] - Epoch: 41/130
2023-09-22 16:57:00,862 [INFO] - Training epoch stats:     Loss: 3.2546 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0396
2023-09-22 16:57:15,164 [DEBUG] - Old lr: 0.000039 - New lr: 0.000037
2023-09-22 16:57:15,164 [INFO] - Epoch: 42/130
2023-09-22 17:00:06,944 [INFO] - Training epoch stats:     Loss: 3.2432 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0420
2023-09-22 17:00:13,015 [DEBUG] - Old lr: 0.000037 - New lr: 0.000035
2023-09-22 17:00:13,015 [INFO] - Epoch: 43/130
2023-09-22 17:02:58,679 [INFO] - Training epoch stats:     Loss: 3.2336 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-22 17:03:12,009 [DEBUG] - Old lr: 0.000035 - New lr: 0.000033
2023-09-22 17:03:12,010 [INFO] - Epoch: 44/130
2023-09-22 17:06:03,134 [INFO] - Training epoch stats:     Loss: 3.2189 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0480
2023-09-22 17:06:19,774 [DEBUG] - Old lr: 0.000033 - New lr: 0.000031
2023-09-22 17:06:19,774 [INFO] - Epoch: 45/130
2023-09-22 17:09:10,584 [INFO] - Training epoch stats:     Loss: 3.2180 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 17:09:16,592 [DEBUG] - Old lr: 0.000031 - New lr: 0.000030
2023-09-22 17:09:16,593 [INFO] - Epoch: 46/130
2023-09-22 17:12:03,839 [INFO] - Training epoch stats:     Loss: 3.2062 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0495
2023-09-22 17:12:09,852 [DEBUG] - Old lr: 0.000030 - New lr: 0.000028
2023-09-22 17:12:09,852 [INFO] - Epoch: 47/130
2023-09-22 17:14:55,732 [INFO] - Training epoch stats:     Loss: 3.2140 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 17:15:10,921 [DEBUG] - Old lr: 0.000028 - New lr: 0.000027
2023-09-22 17:15:10,922 [INFO] - Epoch: 48/130
2023-09-22 17:18:00,407 [INFO] - Training epoch stats:     Loss: 3.1883 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0428
2023-09-22 17:18:14,558 [DEBUG] - Old lr: 0.000027 - New lr: 0.000026
2023-09-22 17:18:14,558 [INFO] - Epoch: 49/130
2023-09-22 17:21:09,637 [INFO] - Training epoch stats:     Loss: 3.1932 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 17:21:22,440 [DEBUG] - Old lr: 0.000026 - New lr: 0.000024
2023-09-22 17:21:22,441 [INFO] - Epoch: 50/130
2023-09-22 17:24:11,661 [INFO] - Training epoch stats:     Loss: 3.1913 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-22 17:29:09,526 [INFO] - Validation epoch stats:   Loss: 3.2443 - Binary-Cell-Dice: 0.7747 - Binary-Cell-Jacard: 0.6861 - bPQ-Score: 0.5905 - mPQ-Score: 0.4412 - Tissue-MC-Acc.: 0.0267
2023-09-22 17:29:09,536 [INFO] - New best model - save checkpoint
2023-09-22 17:29:46,207 [DEBUG] - Old lr: 0.000024 - New lr: 0.000023
2023-09-22 17:29:46,208 [INFO] - Epoch: 51/130
2023-09-22 17:32:33,633 [INFO] - Training epoch stats:     Loss: 3.1791 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0373
2023-09-22 17:32:52,558 [DEBUG] - Old lr: 0.000023 - New lr: 0.000022
2023-09-22 17:32:52,558 [INFO] - Epoch: 52/130
2023-09-22 17:35:38,438 [INFO] - Training epoch stats:     Loss: 3.1765 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 17:35:46,716 [DEBUG] - Old lr: 0.000022 - New lr: 0.000021
2023-09-22 17:35:46,717 [INFO] - Epoch: 53/130
2023-09-22 17:38:31,078 [INFO] - Training epoch stats:     Loss: 3.1723 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0420
2023-09-22 17:38:51,448 [DEBUG] - Old lr: 0.000021 - New lr: 0.000020
2023-09-22 17:38:51,448 [INFO] - Epoch: 54/130
2023-09-22 17:41:39,787 [INFO] - Training epoch stats:     Loss: 3.1579 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0464
2023-09-22 17:42:04,665 [DEBUG] - Old lr: 0.000020 - New lr: 0.000019
2023-09-22 17:42:04,666 [INFO] - Epoch: 55/130
2023-09-22 17:44:55,713 [INFO] - Training epoch stats:     Loss: 3.1676 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-22 17:45:19,939 [DEBUG] - Old lr: 0.000019 - New lr: 0.000018
2023-09-22 17:45:19,940 [INFO] - Epoch: 56/130
2023-09-22 17:48:08,095 [INFO] - Training epoch stats:     Loss: 3.1809 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-22 17:48:15,461 [DEBUG] - Old lr: 0.000018 - New lr: 0.000017
2023-09-22 17:48:15,462 [INFO] - Epoch: 57/130
2023-09-22 17:51:05,574 [INFO] - Training epoch stats:     Loss: 3.1632 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0484
2023-09-22 17:51:27,126 [DEBUG] - Old lr: 0.000017 - New lr: 0.000016
2023-09-22 17:51:27,126 [INFO] - Epoch: 58/130
2023-09-22 17:54:10,586 [INFO] - Training epoch stats:     Loss: 3.1421 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-22 17:54:23,944 [DEBUG] - Old lr: 0.000016 - New lr: 0.000015
2023-09-22 17:54:23,944 [INFO] - Epoch: 59/130
2023-09-22 17:57:08,586 [INFO] - Training epoch stats:     Loss: 3.1569 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-22 17:57:14,614 [DEBUG] - Old lr: 0.000015 - New lr: 0.000015
2023-09-22 17:57:14,615 [INFO] - Epoch: 60/130
2023-09-22 17:59:53,662 [INFO] - Training epoch stats:     Loss: 3.1409 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0349
2023-09-22 18:05:46,549 [INFO] - Validation epoch stats:   Loss: 3.2147 - Binary-Cell-Dice: 0.7750 - Binary-Cell-Jacard: 0.6874 - bPQ-Score: 0.5938 - mPQ-Score: 0.4572 - Tissue-MC-Acc.: 0.0203
2023-09-22 18:05:46,558 [INFO] - New best model - save checkpoint
2023-09-22 18:06:13,786 [DEBUG] - Old lr: 0.000015 - New lr: 0.000014
2023-09-22 18:06:13,787 [INFO] - Epoch: 61/130
2023-09-22 18:08:59,636 [INFO] - Training epoch stats:     Loss: 3.1359 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0460
2023-09-22 18:09:15,182 [DEBUG] - Old lr: 0.000014 - New lr: 0.000013
2023-09-22 18:09:15,183 [INFO] - Epoch: 62/130
2023-09-22 18:12:03,738 [INFO] - Training epoch stats:     Loss: 3.1399 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-22 18:12:09,775 [DEBUG] - Old lr: 0.000013 - New lr: 0.000012
2023-09-22 18:12:09,776 [INFO] - Epoch: 63/130
2023-09-22 18:14:51,135 [INFO] - Training epoch stats:     Loss: 3.1279 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 18:15:05,194 [DEBUG] - Old lr: 0.000012 - New lr: 0.000012
2023-09-22 18:15:05,195 [INFO] - Epoch: 64/130
2023-09-22 18:17:53,376 [INFO] - Training epoch stats:     Loss: 3.1297 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0373
2023-09-22 18:18:06,768 [DEBUG] - Old lr: 0.000012 - New lr: 0.000011
2023-09-22 18:18:06,768 [INFO] - Epoch: 65/130
2023-09-22 18:20:57,389 [INFO] - Training epoch stats:     Loss: 3.1258 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 18:21:11,546 [DEBUG] - Old lr: 0.000011 - New lr: 0.000011
2023-09-22 18:21:11,547 [INFO] - Epoch: 66/130
2023-09-22 18:23:58,958 [INFO] - Training epoch stats:     Loss: 3.1240 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0480
2023-09-22 18:24:04,954 [DEBUG] - Old lr: 0.000011 - New lr: 0.000010
2023-09-22 18:24:04,955 [INFO] - Epoch: 67/130
2023-09-22 18:26:45,429 [INFO] - Training epoch stats:     Loss: 3.1078 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-22 18:27:00,085 [DEBUG] - Old lr: 0.000010 - New lr: 0.000010
2023-09-22 18:27:00,085 [INFO] - Epoch: 68/130
2023-09-22 18:29:45,826 [INFO] - Training epoch stats:     Loss: 3.1158 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-22 18:29:57,975 [DEBUG] - Old lr: 0.000010 - New lr: 0.000009
2023-09-22 18:29:57,975 [INFO] - Epoch: 69/130
2023-09-22 18:32:46,332 [INFO] - Training epoch stats:     Loss: 3.1097 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-22 18:33:12,089 [DEBUG] - Old lr: 0.000009 - New lr: 0.000009
2023-09-22 18:33:12,089 [INFO] - Epoch: 70/130
2023-09-22 18:36:01,749 [INFO] - Training epoch stats:     Loss: 3.1044 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0428
2023-09-22 18:41:47,549 [INFO] - Validation epoch stats:   Loss: 3.2041 - Binary-Cell-Dice: 0.7791 - Binary-Cell-Jacard: 0.6919 - bPQ-Score: 0.5994 - mPQ-Score: 0.4619 - Tissue-MC-Acc.: 0.0230
2023-09-22 18:41:47,558 [INFO] - New best model - save checkpoint
2023-09-22 18:42:14,939 [DEBUG] - Old lr: 0.000009 - New lr: 0.000008
2023-09-22 18:42:14,939 [INFO] - Epoch: 71/130
2023-09-22 18:45:05,009 [INFO] - Training epoch stats:     Loss: 3.0967 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0543
2023-09-22 18:45:22,678 [DEBUG] - Old lr: 0.000008 - New lr: 0.000008
2023-09-22 18:45:22,678 [INFO] - Epoch: 72/130
2023-09-22 18:48:11,767 [INFO] - Training epoch stats:     Loss: 3.1079 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0476
2023-09-22 18:48:25,915 [DEBUG] - Old lr: 0.000008 - New lr: 0.000007
2023-09-22 18:48:25,916 [INFO] - Epoch: 73/130
2023-09-22 18:51:13,942 [INFO] - Training epoch stats:     Loss: 3.1085 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-22 18:51:19,957 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-22 18:51:19,958 [INFO] - Epoch: 74/130
2023-09-22 18:54:07,860 [INFO] - Training epoch stats:     Loss: 3.1044 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 18:54:20,261 [DEBUG] - Old lr: 0.000007 - New lr: 0.000007
2023-09-22 18:54:20,262 [INFO] - Epoch: 75/130
2023-09-22 18:57:09,492 [INFO] - Training epoch stats:     Loss: 3.0998 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0460
2023-09-22 18:57:21,046 [DEBUG] - Old lr: 0.000007 - New lr: 0.000006
2023-09-22 18:57:21,046 [INFO] - Epoch: 76/130
2023-09-22 19:00:45,559 [INFO] - Training epoch stats:     Loss: 3.0953 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0484
2023-09-22 19:00:59,486 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-22 19:00:59,487 [INFO] - Epoch: 77/130
2023-09-22 19:03:50,235 [INFO] - Training epoch stats:     Loss: 3.1072 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-22 19:04:05,227 [DEBUG] - Old lr: 0.000006 - New lr: 0.000006
2023-09-22 19:04:05,228 [INFO] - Epoch: 78/130
2023-09-22 19:06:54,962 [INFO] - Training epoch stats:     Loss: 3.0882 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0464
2023-09-22 19:07:01,522 [DEBUG] - Old lr: 0.000006 - New lr: 0.000005
2023-09-22 19:07:01,523 [INFO] - Epoch: 79/130
2023-09-22 19:09:47,610 [INFO] - Training epoch stats:     Loss: 3.0939 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0491
2023-09-22 19:10:01,333 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-22 19:10:01,333 [INFO] - Epoch: 80/130
2023-09-22 19:12:48,159 [INFO] - Training epoch stats:     Loss: 3.0917 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0448
2023-09-22 19:17:28,949 [INFO] - Validation epoch stats:   Loss: 3.1973 - Binary-Cell-Dice: 0.7759 - Binary-Cell-Jacard: 0.6906 - bPQ-Score: 0.5971 - mPQ-Score: 0.4607 - Tissue-MC-Acc.: 0.0233
2023-09-22 19:17:42,891 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-22 19:17:42,892 [INFO] - Epoch: 81/130
2023-09-22 19:20:32,431 [INFO] - Training epoch stats:     Loss: 3.0751 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0432
2023-09-22 19:20:38,864 [DEBUG] - Old lr: 0.000005 - New lr: 0.000005
2023-09-22 19:20:38,865 [INFO] - Epoch: 82/130
2023-09-22 19:23:24,185 [INFO] - Training epoch stats:     Loss: 3.0959 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-22 19:23:38,018 [DEBUG] - Old lr: 0.000005 - New lr: 0.000004
2023-09-22 19:23:38,019 [INFO] - Epoch: 83/130
2023-09-22 19:26:29,491 [INFO] - Training epoch stats:     Loss: 3.0938 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0499
2023-09-22 19:26:42,636 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-22 19:26:42,637 [INFO] - Epoch: 84/130
2023-09-22 19:29:29,223 [INFO] - Training epoch stats:     Loss: 3.0855 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0448
2023-09-22 19:29:42,620 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-22 19:29:42,621 [INFO] - Epoch: 85/130
2023-09-22 19:32:26,005 [INFO] - Training epoch stats:     Loss: 3.0867 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0472
2023-09-22 19:32:32,686 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-22 19:32:32,687 [INFO] - Epoch: 86/130
2023-09-22 19:35:16,264 [INFO] - Training epoch stats:     Loss: 3.0805 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0499
2023-09-22 19:35:34,499 [DEBUG] - Old lr: 0.000004 - New lr: 0.000004
2023-09-22 19:35:34,499 [INFO] - Epoch: 87/130
2023-09-22 19:38:17,850 [INFO] - Training epoch stats:     Loss: 3.0938 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0484
2023-09-22 19:38:32,101 [DEBUG] - Old lr: 0.000004 - New lr: 0.000003
2023-09-22 19:38:32,102 [INFO] - Epoch: 88/130
2023-09-22 19:41:16,672 [INFO] - Training epoch stats:     Loss: 3.0732 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 19:41:32,485 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 19:41:32,486 [INFO] - Epoch: 89/130
2023-09-22 19:44:25,175 [INFO] - Training epoch stats:     Loss: 3.0970 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0472
2023-09-22 19:44:30,534 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 19:44:30,535 [INFO] - Epoch: 90/130
2023-09-22 19:47:14,507 [INFO] - Training epoch stats:     Loss: 3.0825 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0476
2023-09-22 19:52:04,462 [INFO] - Validation epoch stats:   Loss: 3.1981 - Binary-Cell-Dice: 0.7779 - Binary-Cell-Jacard: 0.6921 - bPQ-Score: 0.6009 - mPQ-Score: 0.4627 - Tissue-MC-Acc.: 0.0248
2023-09-22 19:52:04,473 [INFO] - New best model - save checkpoint
2023-09-22 19:52:33,071 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 19:52:33,072 [INFO] - Epoch: 91/130
2023-09-22 19:55:21,307 [INFO] - Training epoch stats:     Loss: 3.0689 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 19:55:31,110 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 19:55:31,111 [INFO] - Epoch: 92/130
2023-09-22 19:58:15,009 [INFO] - Training epoch stats:     Loss: 3.0782 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 19:58:29,106 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 19:58:29,106 [INFO] - Epoch: 93/130
2023-09-22 20:01:16,246 [INFO] - Training epoch stats:     Loss: 3.0825 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 20:01:40,433 [DEBUG] - Old lr: 0.000003 - New lr: 0.000003
2023-09-22 20:01:40,433 [INFO] - Epoch: 94/130
2023-09-22 20:04:25,358 [INFO] - Training epoch stats:     Loss: 3.0881 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 20:04:50,816 [DEBUG] - Old lr: 0.000003 - New lr: 0.000002
2023-09-22 20:04:50,816 [INFO] - Epoch: 95/130
2023-09-22 20:07:57,251 [INFO] - Training epoch stats:     Loss: 3.0814 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0392
2023-09-22 20:08:14,243 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:08:14,244 [INFO] - Epoch: 96/130
2023-09-22 20:11:01,697 [INFO] - Training epoch stats:     Loss: 3.0801 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 20:11:31,123 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:11:31,124 [INFO] - Epoch: 97/130
2023-09-22 20:14:24,549 [INFO] - Training epoch stats:     Loss: 3.0774 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0468
2023-09-22 20:14:54,556 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:14:54,557 [INFO] - Epoch: 98/130
2023-09-22 20:17:47,616 [INFO] - Training epoch stats:     Loss: 3.0716 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0321
2023-09-22 20:18:01,970 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:18:01,970 [INFO] - Epoch: 99/130
2023-09-22 20:20:57,856 [INFO] - Training epoch stats:     Loss: 3.0700 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0337
2023-09-22 20:21:03,857 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:21:03,858 [INFO] - Epoch: 100/130
2023-09-22 20:23:45,925 [INFO] - Training epoch stats:     Loss: 3.0809 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 20:28:44,457 [INFO] - Validation epoch stats:   Loss: 3.2022 - Binary-Cell-Dice: 0.7797 - Binary-Cell-Jacard: 0.6948 - bPQ-Score: 0.6010 - mPQ-Score: 0.4650 - Tissue-MC-Acc.: 0.0252
2023-09-22 20:28:44,467 [INFO] - New best model - save checkpoint
2023-09-22 20:29:12,425 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:29:12,426 [INFO] - Epoch: 101/130
2023-09-22 20:31:59,166 [INFO] - Training epoch stats:     Loss: 3.0811 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0408
2023-09-22 20:32:13,503 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:32:13,504 [INFO] - Epoch: 102/130
2023-09-22 20:34:58,450 [INFO] - Training epoch stats:     Loss: 3.0670 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 20:35:04,465 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:35:04,465 [INFO] - Epoch: 103/130
2023-09-22 20:37:47,457 [INFO] - Training epoch stats:     Loss: 3.0666 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 20:38:07,391 [DEBUG] - Old lr: 0.000002 - New lr: 0.000002
2023-09-22 20:38:07,392 [INFO] - Epoch: 104/130
2023-09-22 20:40:56,068 [INFO] - Training epoch stats:     Loss: 3.0684 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 20:41:34,039 [DEBUG] - Old lr: 0.000002 - New lr: 0.000001
2023-09-22 20:41:34,040 [INFO] - Epoch: 105/130
2023-09-22 20:44:23,005 [INFO] - Training epoch stats:     Loss: 3.0690 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0365
2023-09-22 20:44:35,177 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 20:44:35,178 [INFO] - Epoch: 106/130
2023-09-22 20:47:26,298 [INFO] - Training epoch stats:     Loss: 3.0697 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-22 20:47:32,486 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 20:47:32,487 [INFO] - Epoch: 107/130
2023-09-22 20:50:25,016 [INFO] - Training epoch stats:     Loss: 3.0794 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 20:50:58,285 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 20:50:58,286 [INFO] - Epoch: 108/130
2023-09-22 20:53:52,748 [INFO] - Training epoch stats:     Loss: 3.0656 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0416
2023-09-22 20:54:09,986 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 20:54:09,987 [INFO] - Epoch: 109/130
2023-09-22 20:57:00,163 [INFO] - Training epoch stats:     Loss: 3.0711 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0460
2023-09-22 20:57:06,192 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 20:57:06,193 [INFO] - Epoch: 110/130
2023-09-22 20:59:50,733 [INFO] - Training epoch stats:     Loss: 3.0794 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-22 21:05:12,645 [INFO] - Validation epoch stats:   Loss: 3.1973 - Binary-Cell-Dice: 0.7784 - Binary-Cell-Jacard: 0.6925 - bPQ-Score: 0.5989 - mPQ-Score: 0.4615 - Tissue-MC-Acc.: 0.0271
2023-09-22 21:05:26,120 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:05:26,121 [INFO] - Epoch: 111/130
2023-09-22 21:08:12,868 [INFO] - Training epoch stats:     Loss: 3.0797 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0388
2023-09-22 21:08:25,879 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:08:25,880 [INFO] - Epoch: 112/130
2023-09-22 21:11:08,067 [INFO] - Training epoch stats:     Loss: 3.0842 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 21:11:22,305 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:11:22,305 [INFO] - Epoch: 113/130
2023-09-22 21:14:08,449 [INFO] - Training epoch stats:     Loss: 3.0721 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0412
2023-09-22 21:14:14,461 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:14:14,461 [INFO] - Epoch: 114/130
2023-09-22 21:16:57,628 [INFO] - Training epoch stats:     Loss: 3.0732 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0468
2023-09-22 21:17:16,650 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:17:16,650 [INFO] - Epoch: 115/130
2023-09-22 21:20:03,711 [INFO] - Training epoch stats:     Loss: 3.0733 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0424
2023-09-22 21:20:39,125 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:20:39,126 [INFO] - Epoch: 116/130
2023-09-22 21:23:34,292 [INFO] - Training epoch stats:     Loss: 3.0805 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0440
2023-09-22 21:23:43,678 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:23:43,679 [INFO] - Epoch: 117/130
2023-09-22 21:26:31,964 [INFO] - Training epoch stats:     Loss: 3.0802 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 21:26:59,926 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:26:59,927 [INFO] - Epoch: 118/130
2023-09-22 21:29:55,001 [INFO] - Training epoch stats:     Loss: 3.0638 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0456
2023-09-22 21:30:15,055 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:30:15,056 [INFO] - Epoch: 119/130
2023-09-22 21:33:06,556 [INFO] - Training epoch stats:     Loss: 3.0673 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0432
2023-09-22 21:33:19,092 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:33:19,093 [INFO] - Epoch: 120/130
2023-09-22 21:36:06,217 [INFO] - Training epoch stats:     Loss: 3.0824 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0400
2023-09-22 21:41:08,893 [INFO] - Validation epoch stats:   Loss: 3.1987 - Binary-Cell-Dice: 0.7809 - Binary-Cell-Jacard: 0.6963 - bPQ-Score: 0.6027 - mPQ-Score: 0.4641 - Tissue-MC-Acc.: 0.0264
2023-09-22 21:41:08,903 [INFO] - New best model - save checkpoint
2023-09-22 21:41:34,907 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:41:34,907 [INFO] - Epoch: 121/130
2023-09-22 21:44:17,348 [INFO] - Training epoch stats:     Loss: 3.0725 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0373
2023-09-22 21:44:31,102 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:44:31,103 [INFO] - Epoch: 122/130
2023-09-22 21:47:15,662 [INFO] - Training epoch stats:     Loss: 3.0738 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0369
2023-09-22 21:47:29,065 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:47:29,066 [INFO] - Epoch: 123/130
2023-09-22 21:50:14,870 [INFO] - Training epoch stats:     Loss: 3.0791 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0333
2023-09-22 21:50:20,904 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:50:20,904 [INFO] - Epoch: 124/130
2023-09-22 21:53:05,758 [INFO] - Training epoch stats:     Loss: 3.0574 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0472
2023-09-22 21:53:18,926 [DEBUG] - Old lr: 0.000001 - New lr: 0.000001
2023-09-22 21:53:18,927 [INFO] - Epoch: 125/130
2023-09-22 21:56:06,183 [INFO] - Training epoch stats:     Loss: 3.0700 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0452
2023-09-22 21:56:32,370 [DEBUG] - Old lr: 0.000001 - New lr: 0.000000
2023-09-22 21:56:32,371 [INFO] - Epoch: 126/130
2023-09-22 21:59:24,756 [INFO] - Training epoch stats:     Loss: 3.0576 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0404
2023-09-22 21:59:30,760 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-22 21:59:30,761 [INFO] - Epoch: 127/130
2023-09-22 22:02:25,640 [INFO] - Training epoch stats:     Loss: 3.0672 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0448
2023-09-22 22:02:33,498 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-22 22:02:33,499 [INFO] - Epoch: 128/130
2023-09-22 22:05:21,024 [INFO] - Training epoch stats:     Loss: 3.0650 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0444
2023-09-22 22:05:33,655 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-22 22:05:33,656 [INFO] - Epoch: 129/130
2023-09-22 22:08:32,493 [INFO] - Training epoch stats:     Loss: 3.0575 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0436
2023-09-22 22:08:47,374 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-22 22:08:47,375 [INFO] - Epoch: 130/130
2023-09-22 22:11:31,866 [INFO] - Training epoch stats:     Loss: 3.0753 - Binary-Cell-Dice: 0.0000 - Binary-Cell-Jacard: 0.0000 - Tissue-MC-Acc.: 0.0396
2023-09-22 22:17:23,572 [INFO] - Validation epoch stats:   Loss: 3.1964 - Binary-Cell-Dice: 0.7808 - Binary-Cell-Jacard: 0.6955 - bPQ-Score: 0.6023 - mPQ-Score: 0.4671 - Tissue-MC-Acc.: 0.0260
2023-09-22 22:17:38,508 [DEBUG] - Old lr: 0.000000 - New lr: 0.000000
2023-09-22 22:17:38,516 [INFO] -
