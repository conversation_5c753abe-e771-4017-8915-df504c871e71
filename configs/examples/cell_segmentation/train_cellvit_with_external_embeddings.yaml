# Example configuration for training CellViT with external embeddings from transcription information
# This configuration shows how to incorporate external embeddings into the CellViT training process

# Random Seed
random_seed: 19

# Logging settings
logging:
  mode: online  # online, offline, disabled
  project: CellViT-External-Embeddings
  tags:
    - CellViT
    - External-Embeddings
    - Transcription
  log_comment: CellViT training with external embeddings from transcription information
  log_dir: ./logs/
  level: INFO
  group: CellViT-External-Embeddings
  log_images: True
  log_freq: 50

# Wandb sweep configuration
wandb_sweep:
  method: grid
  metric:
    goal: maximize
    name: mPQ/Validation
  parameters:
    learning_rate:
      values: [0.0003, 0.0001]
    batch_size:
      values: [8, 16]

# Data settings
data:
  dataset: PanNuke  # Name of the dataset
  dataset_path: ./data/PanNuke  # Path to dataset
  train_folds: [0, 1, 2]  # Folds for training (0, 1, 2 for PanNuke)
  val_folds: [3]  # Folds for validation
  test_folds: [4]  # Folds for testing
  num_nuclei_classes: 6  # Number of nuclei classes (including background)
  num_tissue_classes: 19  # Number of tissue classes
  magnification: 40  # Magnification of the images
  
  # External embeddings configuration
  external_embeddings:
    enabled: true  # Enable external embeddings
    embeddings_path: ./data/PanNuke/external_embeddings.npz  # Path to external embeddings file
    embedding_dim: 512  # Dimension of external embeddings
    
  # Data augmentation settings
  transforms:
    randomrotate90:
      p: 0.5
    horizontalflip:
      p: 0.5
    verticalflip:
      p: 0.5
    downscale:
      p: 0.25
      scale: [0.5, 1.0]
    blur:
      p: 0.2
      blur_limit: 10
    gaussnoise:
      p: 0.25
      var_limit: [10, 50]
    colorjitter:
      p: 0.2
      brightness: 0.1
      contrast: 0.1
      saturation: 0.1
      hue: 0.1
    superpixels:
      p: 0.1
      p_replace: 0.1
      n_segments: 100
    normalize:
      mean: [0.5, 0.5, 0.5]
      std: [0.5, 0.5, 0.5]

# Model settings
model:
  type: CellViT  # Model type
  backbone: SAM-B  # Backbone architecture (SAM-B, SAM-L, SAM-H, ViT256)
  pretrained_encoder: ./models/pretrained/sam_vit_b_01ec64.pth  # Path to pretrained encoder
  shared_skip_connections: true  # Use shared skip connections
  
  # External embedding settings
  external_embeddings:
    use_external_embeddings: true  # Enable external embeddings in model
    external_embedding_dim: 512  # Dimension of external embeddings
    fusion_method: attention  # Method for fusing embeddings (attention, concat, add)

# Training settings
training:
  drop_rate: 0.1  # Dropout rate
  attn_drop_rate: 0.1  # Attention dropout rate
  drop_path_rate: 0.1  # Drop path rate
  batch_size: 8  # Batch size
  epochs: 130  # Number of training epochs
  learning_rate: 0.0003  # Learning rate
  weight_decay: 1e-5  # Weight decay
  early_stopping_patience: 25  # Early stopping patience
  schedule_lr: true  # Enable learning rate scheduling
  gamma: 0.85  # Learning rate decay factor
  step_size: 25  # Learning rate decay step size
  mixed_precision: true  # Enable mixed precision training
  find_lr: false  # Enable learning rate finder
  regression_loss: false  # Enable regression loss
  
  # Optimizer settings
  optimizer: AdamW  # Optimizer type
  optimizer_hyperparameter:
    betas: [0.85, 0.95]  # Adam beta parameters

# Loss function settings
loss:
  # Standard CellViT losses
  nuclei_binary_map:
    bce:
      loss_fn: xentropy_loss
      weight: 1
    dice:
      loss_fn: dice_loss
      weight: 1
  hv_map:
    mse:
      loss_fn: mse_loss_maps
      weight: 1
    msge:
      loss_fn: msge_loss_maps
      weight: 1
  nuclei_type_map:
    bce:
      loss_fn: xentropy_loss
      weight: 1
    dice:
      loss_fn: dice_loss
      weight: 1
  tissue_types:
    ce:
      loss_fn: CrossEntropyLoss
      weight: 1
      
  # External embedding alignment loss
  external_embeddings:
    alignment:
      loss_fn: EmbeddingAlignmentLoss
      weight: 0.1  # Weight for embedding alignment loss
      args:
        loss_type: cosine  # Type of alignment loss (cosine, mse, l1)
        temperature: 0.1  # Temperature parameter for cosine loss

# Hardware settings
gpu: 0  # GPU device ID
