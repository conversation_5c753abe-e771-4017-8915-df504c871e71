# Byte-compiled / optimized / DLL files
__pycache__/


# development
.vscode

# test folder with test files
debug

# logs
cufile.log
outputs

# coverage
htmlcov

# other
docs/build
docs/source
docs/make.bat
docs/Makefile

# local configs
configs/projects
configs/PanNuke
configs/Lizzard
configs/Conic
configs/TCGA

# pretrained models
*.pth
docker/

# Notebooks
notebooks/

# ruff
.ruff_cache

# slurm
slurm

# example
example/output
example/TCGA-V5-A7RE-11A-01-TS1.57401526-EF9E-49AC-8FF6-B4F9652311CE.svs
example/test_dataset

# results
results/

# other
.DS_Store
Orig-Metrics
