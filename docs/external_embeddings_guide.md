# External Embeddings Integration for CellViT

This guide explains how to integrate external embeddings from transcription information into the CellViT training process.

## Overview

The external embeddings feature allows you to incorporate additional information from transcription data (e.g., gene expression, RNA-seq data) into the CellViT model training. This can help the model learn better representations by leveraging both visual and molecular information.

## Key Features

- **Multi-modal Learning**: Combine visual features from histology images with molecular features from transcription data
- **Flexible Embedding Fusion**: Support for attention-based fusion of visual and external embeddings
- **Configurable Loss Functions**: Add embedding alignment losses to encourage consistency between modalities
- **Multiple File Formats**: Support for `.npy`, `.npz`, `.pt`, and `.pth` embedding files

## Architecture Changes

### Dataset Modifications
- `PanNukeDataset` now supports loading external embeddings
- Returns 5-tuple: `(image, masks, tissue_type, image_name, external_embedding)`
- Handles missing embeddings gracefully with zero padding

### Model Modifications
- `CellViT` models now accept `external_embeddings` parameter in forward pass
- Added embedding projection and fusion layers
- Attention-based fusion mechanism for combining visual and external features

### Training Modifications
- Trainer automatically handles external embeddings when present
- Support for embedding alignment losses
- Backward compatibility with existing training pipelines

## Usage

### 1. Prepare External Embeddings

Create embeddings file in one of the supported formats:

```python
import numpy as np

# Example: Create embeddings for each image
embeddings = {
    "image_001.png": np.random.randn(512),  # 512-dim embedding
    "image_002.png": np.random.randn(512),
    # ... more embeddings
}

# Save as .npz file
np.savez("external_embeddings.npz", **embeddings)

# Or save as .npy array (must match image order)
embedding_array = np.array([embeddings[img] for img in sorted(embeddings.keys())])
np.save("external_embeddings.npy", embedding_array)
```

### 2. Configure Dataset

```python
from cell_segmentation.datasets.pannuke import PanNukeDataset

dataset = PanNukeDataset(
    dataset_path="./data/PanNuke",
    folds=[0, 1, 2],
    external_embeddings_path="./data/external_embeddings.npz",
    embedding_dim=512
)
```

### 3. Configure Model

```python
from models.segmentation.cell_segmentation.cellvit import CellViTSAM

model = CellViTSAM(
    model_path="./models/sam_vit_b_01ec64.pth",
    num_nuclei_classes=6,
    num_tissue_classes=19,
    vit_structure="SAM-B",
    external_embedding_dim=512,
    use_external_embeddings=True
)
```

### 4. Configure Loss Functions

Add embedding alignment loss to your configuration:

```yaml
loss:
  # Standard losses...
  nuclei_binary_map:
    bce:
      loss_fn: xentropy_loss
      weight: 1
  
  # External embedding alignment loss
  external_embeddings:
    alignment:
      loss_fn: EmbeddingAlignmentLoss
      weight: 0.1
      args:
        loss_type: cosine  # or "mse", "l1"
        temperature: 0.1
```

### 5. Training

```python
# Forward pass with external embeddings
predictions = model(images, external_embeddings=external_embeddings)

# Or use the provided example script
python examples/train_cellvit_with_external_embeddings.py \
    --config configs/examples/cell_segmentation/train_cellvit_with_external_embeddings.yaml
```

## Configuration Options

### Dataset Configuration
```yaml
data:
  external_embeddings:
    enabled: true
    embeddings_path: "./data/external_embeddings.npz"
    embedding_dim: 512
```

### Model Configuration
```yaml
model:
  external_embeddings:
    use_external_embeddings: true
    external_embedding_dim: 512
    fusion_method: attention  # Future: support for different fusion methods
```

### Loss Configuration
```yaml
loss:
  external_embeddings:
    alignment:
      loss_fn: EmbeddingAlignmentLoss
      weight: 0.1
      args:
        loss_type: cosine  # cosine, mse, l1
        temperature: 0.1
```

## Embedding File Formats

### 1. NumPy Compressed (.npz)
```python
# Dictionary mapping image names to embeddings
embeddings = {"image1.png": embedding1, "image2.png": embedding2}
np.savez("embeddings.npz", **embeddings)
```

### 2. NumPy Array (.npy)
```python
# Array with embeddings in same order as dataset images
embeddings_array = np.array([emb1, emb2, emb3, ...])
np.save("embeddings.npy", embeddings_array)
```

### 3. PyTorch (.pt/.pth)
```python
# Dictionary or tensor
torch.save(embeddings_dict, "embeddings.pt")
# or
torch.save(embeddings_tensor, "embeddings.pt")
```

## Best Practices

1. **Embedding Dimension**: Use consistent embedding dimensions (e.g., 512, 768, 1024)
2. **Normalization**: Consider normalizing embeddings to unit norm for cosine similarity
3. **Loss Weighting**: Start with low weights (0.01-0.1) for alignment losses
4. **Missing Data**: The system handles missing embeddings with zero padding
5. **Validation**: Ensure embedding files contain entries for all images in your dataset

## Troubleshooting

### Common Issues

1. **Missing Embeddings**: Check that all image names in your dataset have corresponding embeddings
2. **Dimension Mismatch**: Ensure `embedding_dim` matches the actual embedding dimensions
3. **File Format**: Verify that embedding files are in supported formats
4. **Memory Usage**: Large embedding files may require more RAM

### Error Messages

- `FileNotFoundError`: Check that the embeddings file path is correct
- `ValueError: Number of embeddings doesn't match`: Ensure embedding count matches image count
- `KeyError`: Missing embedding for specific image - check image naming consistency

## Example Workflow

1. Extract transcription features using your preferred method (e.g., gene expression analysis)
2. Create embeddings file mapping image names to feature vectors
3. Update configuration to enable external embeddings
4. Train model with both visual and transcription information
5. Evaluate improved performance on downstream tasks

## Future Enhancements

- Support for different fusion mechanisms (concatenation, element-wise addition)
- Hierarchical embeddings for multi-scale transcription data
- Dynamic embedding loading for large datasets
- Integration with popular transcriptomics analysis tools
