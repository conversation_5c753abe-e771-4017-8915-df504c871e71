# Quick Start Guide: CellViT with External Embeddings

## Overview

This guide provides step-by-step instructions for using the enhanced CellViT model with external embeddings from transcription information.

## What's New

✅ **Multi-modal Learning**: Combine histological images with transcription data  
✅ **Attention-based Fusion**: Advanced mechanism for integrating visual and molecular features  
✅ **Flexible Configuration**: Easy to enable/disable external embeddings  
✅ **Multiple File Formats**: Support for .npy, .npz, .pt, .pth embedding files  
✅ **Backward Compatibility**: Existing models work without modification  

## Prerequisites

1. **Dependencies**: torch, numpy, torchvision, albumentations, etc.
2. **Dataset**: PanNuke or compatible dataset
3. **External Embeddings**: Transcription-derived feature vectors

## Step 1: Prepare External Embeddings

### Option A: From Dictionary (Recommended)
```python
import numpy as np

# Create embeddings for each image
embeddings = {
    "image_001.png": np.random.randn(512),  # Replace with real embeddings
    "image_002.png": np.random.randn(512),
    "image_003.png": np.random.randn(512),
    # ... add all your images
}

# Save as compressed numpy file
np.savez("external_embeddings.npz", **embeddings)
```

### Option B: From Array
```python
# If embeddings are in same order as dataset images
embedding_array = np.array([emb1, emb2, emb3, ...])  # Shape: (N, embedding_dim)
np.save("external_embeddings.npy", embedding_array)
```

### Option C: From PyTorch
```python
import torch

# Dictionary or tensor
torch.save(embeddings_dict, "external_embeddings.pt")
```

## Step 2: Configure Training

### Update Configuration File
```yaml
# data section
data:
  dataset_path: ./data/PanNuke
  train_folds: [0, 1, 2]
  val_folds: [3]
  
  # Add external embeddings configuration
  external_embeddings:
    enabled: true
    embeddings_path: ./data/external_embeddings.npz
    embedding_dim: 512

# model section  
model:
  type: CellViT
  backbone: SAM-B
  
  # Add external embedding support
  external_embeddings:
    use_external_embeddings: true
    external_embedding_dim: 512

# loss section
loss:
  # Standard losses...
  nuclei_binary_map:
    bce:
      loss_fn: xentropy_loss
      weight: 1
  
  # Add embedding alignment loss
  external_embeddings:
    alignment:
      loss_fn: EmbeddingAlignmentLoss
      weight: 0.1  # Start with low weight
      args:
        loss_type: cosine  # or "mse", "l1"
```

## Step 3: Create Dataset and Model

```python
from cell_segmentation.datasets.pannuke import PanNukeDataset
from models.segmentation.cell_segmentation.cellvit import CellViTSAM

# Create dataset with external embeddings
dataset = PanNukeDataset(
    dataset_path="./data/PanNuke",
    folds=[0, 1, 2],
    external_embeddings_path="./data/external_embeddings.npz",
    embedding_dim=512
)

# Create model with external embedding support
model = CellViTSAM(
    model_path="./models/sam_vit_b_01ec64.pth",
    num_nuclei_classes=6,
    num_tissue_classes=19,
    vit_structure="SAM-B",
    external_embedding_dim=512,
    use_external_embeddings=True
)
```

## Step 4: Train the Model

### Option A: Use Example Script
```bash
python examples/train_cellvit_with_external_embeddings.py \
    --config configs/examples/cell_segmentation/train_cellvit_with_external_embeddings.yaml
```

### Option B: Custom Training Loop
```python
from cell_segmentation.trainer.trainer_cellvit import CellViTTrainer

# Create trainer (handles external embeddings automatically)
trainer = CellViTTrainer(
    model=model,
    loss_fn_dict=loss_functions,
    optimizer=optimizer,
    scheduler=scheduler,
    device=device,
    # ... other parameters
)

# Train (external embeddings handled automatically)
trainer.fit(train_dataloader, val_dataloader, epochs=130)
```

## Step 5: Validate Implementation

```bash
# Check implementation
python validate_implementation.py

# Run tests (requires dependencies)
python tests/test_external_embeddings.py
```

## Key Features Explained

### 1. Automatic Batch Handling
The trainer automatically detects and handles external embeddings:
```python
# Batch structure:
# batch[0] = images
# batch[1] = masks  
# batch[2] = tissue_types
# batch[3] = image_names
# batch[4] = external_embeddings (if available)
```

### 2. Attention-based Fusion
```python
# Visual class token + External embedding → Fused representation
visual_token = encoder_output[:, 0:1, :]  # Class token
external_token = project(external_embedding)  # Projected embedding
fused_token = attention(visual_token, external_token)  # Attention fusion
tissue_prediction = classifier(fused_token)  # Enhanced prediction
```

### 3. Graceful Degradation
- Missing embeddings → Zero padding
- Disabled external embeddings → Standard CellViT behavior
- Backward compatibility maintained

## Best Practices

### 1. Embedding Preparation
- **Normalize**: Consider L2 normalization for cosine similarity
- **Dimension**: Use standard sizes (256, 512, 768, 1024)
- **Quality**: Ensure embeddings capture relevant biological information

### 2. Loss Weighting
- **Start Low**: Begin with alignment loss weight 0.01-0.1
- **Tune Gradually**: Increase if alignment improves performance
- **Monitor**: Watch for overfitting to external embeddings

### 3. Validation
- **Cross-modal**: Validate on samples with/without external embeddings
- **Ablation**: Compare performance with and without external embeddings
- **Generalization**: Test on different tissue types and conditions

## Troubleshooting

### Common Issues

1. **FileNotFoundError**: Check embeddings file path
2. **Dimension Mismatch**: Verify embedding_dim matches actual embeddings
3. **Missing Embeddings**: Ensure all images have corresponding embeddings
4. **Memory Issues**: Use smaller batch sizes for large embeddings

### Performance Tips

1. **Preload**: Use dataset caching for small embedding files
2. **Batch Size**: Reduce if memory usage is high
3. **Mixed Precision**: Enable for faster training
4. **Learning Rate**: May need adjustment with external embeddings

## Expected Improvements

With properly prepared external embeddings, you should see:
- **Better Tissue Classification**: Improved accuracy on tissue type prediction
- **Enhanced Representations**: More biologically meaningful features
- **Robust Performance**: Better generalization across different samples

## Next Steps

1. **Experiment**: Try different embedding types and dimensions
2. **Optimize**: Tune loss weights and fusion mechanisms  
3. **Evaluate**: Compare with baseline CellViT performance
4. **Extend**: Consider hierarchical or multi-scale embeddings

## Support

- **Documentation**: See `docs/external_embeddings_guide.md`
- **Examples**: Check `examples/train_cellvit_with_external_embeddings.py`
- **Tests**: Run `tests/test_external_embeddings.py`
- **Implementation**: Review `EXTERNAL_EMBEDDINGS_IMPLEMENTATION.md`

Happy training with multi-modal CellViT! 🚀
