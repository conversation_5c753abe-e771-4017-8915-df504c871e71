#!/usr/bin/env python3
"""
Example script for training CellViT with external embeddings from transcription information.

This script demonstrates how to:
1. Load a dataset with external embeddings
2. Create a CellViT model with external embedding support
3. Train the model with both visual and transcription information

Usage:
    python examples/train_cellvit_with_external_embeddings.py --config configs/examples/cell_segmentation/train_cellvit_with_external_embeddings.yaml
"""

import argparse
import logging
import sys
from pathlib import Path

import numpy as np
import torch
import yaml
from torch.utils.data import DataLoader

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from cell_segmentation.datasets.pannuke import PanNukeDataset
from models.segmentation.cell_segmentation.cellvit import CellViTSAM
from cell_segmentation.trainer.trainer_cellvit import CellViTTrainer
from base_ml.base_loss import retrieve_loss_fn
from cell_segmentation.utils.tools import get_transforms


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


def create_dataset_with_embeddings(config: dict, fold_type: str = "train") -> PanNukeDataset:
    """Create dataset with external embeddings support."""
    data_config = config["data"]
    
    if fold_type == "train":
        folds = data_config["train_folds"]
    elif fold_type == "val":
        folds = data_config["val_folds"]
    else:
        folds = data_config["test_folds"]
    
    # Get transforms
    transforms = get_transforms(
        transform_settings=data_config.get("transforms", {}),
        input_shape=(256, 256),
        normalize_settings=data_config["transforms"]["normalize"]
    )
    
    # External embeddings configuration
    external_embeddings_config = data_config.get("external_embeddings", {})
    external_embeddings_path = None
    embedding_dim = 512
    
    if external_embeddings_config.get("enabled", False):
        external_embeddings_path = external_embeddings_config["embeddings_path"]
        embedding_dim = external_embeddings_config["embedding_dim"]
    
    dataset = PanNukeDataset(
        dataset_path=data_config["dataset_path"],
        folds=folds,
        transforms=transforms,
        external_embeddings_path=external_embeddings_path,
        embedding_dim=embedding_dim
    )
    
    return dataset


def create_model_with_embeddings(config: dict) -> CellViTSAM:
    """Create CellViT model with external embedding support."""
    model_config = config["model"]
    data_config = config["data"]
    training_config = config["training"]
    
    # External embeddings configuration
    external_embeddings_config = model_config.get("external_embeddings", {})
    use_external_embeddings = external_embeddings_config.get("use_external_embeddings", False)
    external_embedding_dim = external_embeddings_config.get("external_embedding_dim", 512)
    
    model = CellViTSAM(
        model_path=model_config["pretrained_encoder"],
        num_nuclei_classes=data_config["num_nuclei_classes"],
        num_tissue_classes=data_config["num_tissue_classes"],
        vit_structure=model_config["backbone"],
        drop_rate=training_config["drop_rate"],
        regression_loss=training_config["regression_loss"],
        external_embedding_dim=external_embedding_dim,
        use_external_embeddings=use_external_embeddings
    )
    
    return model


def create_loss_functions(config: dict) -> dict:
    """Create loss functions including external embedding alignment loss."""
    loss_config = config["loss"]
    loss_fn_dict = {}
    
    for branch_name, branch_losses in loss_config.items():
        if branch_name == "external_embeddings":
            # Handle external embedding losses separately
            continue
            
        loss_fn_dict[branch_name] = {}
        for loss_name, loss_settings in branch_losses.items():
            parameters = loss_settings.get("args", {})
            loss_fn_dict[branch_name][loss_name] = {
                "loss_fn": retrieve_loss_fn(loss_settings["loss_fn"], **parameters),
                "weight": loss_settings["weight"]
            }
    
    return loss_fn_dict


def main():
    parser = argparse.ArgumentParser(description="Train CellViT with external embeddings")
    parser.add_argument("--config", type=str, required=True, help="Path to configuration file")
    parser.add_argument("--gpu", type=int, default=0, help="GPU device ID")
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting CellViT training with external embeddings")
    
    # Load configuration
    config = load_config(args.config)
    logger.info(f"Loaded configuration from {args.config}")
    
    # Set device
    device = torch.device(f"cuda:{args.gpu}" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Create datasets
    logger.info("Creating datasets...")
    train_dataset = create_dataset_with_embeddings(config, "train")
    val_dataset = create_dataset_with_embeddings(config, "val")
    
    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Validation dataset size: {len(val_dataset)}")
    
    # Create data loaders
    train_dataloader = DataLoader(
        train_dataset,
        batch_size=config["training"]["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=config["training"]["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    logger.info("Creating model...")
    model = create_model_with_embeddings(config)
    model = model.to(device)
    
    # Create loss functions
    logger.info("Creating loss functions...")
    loss_fn_dict = create_loss_functions(config)
    
    # Create optimizer
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=config["training"]["learning_rate"],
        weight_decay=config["training"]["weight_decay"],
        betas=config["training"]["optimizer_hyperparameter"]["betas"]
    )
    
    # Create scheduler
    scheduler = torch.optim.lr_scheduler.StepLR(
        optimizer,
        step_size=config["training"]["step_size"],
        gamma=config["training"]["gamma"]
    )
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = CellViTTrainer(
        model=model,
        loss_fn_dict=loss_fn_dict,
        optimizer=optimizer,
        scheduler=scheduler,
        device=device,
        logger=logger,
        logdir=config["logging"]["log_dir"],
        num_classes=config["data"]["num_nuclei_classes"],
        dataset_config=config["data"],
        experiment_config=config,
        mixed_precision=config["training"]["mixed_precision"]
    )
    
    # Start training
    logger.info("Starting training...")
    trainer.fit(
        train_dataloader=train_dataloader,
        val_dataloader=val_dataloader,
        epochs=config["training"]["epochs"],
        unfreeze_epoch=50
    )
    
    logger.info("Training completed!")


if __name__ == "__main__":
    main()
