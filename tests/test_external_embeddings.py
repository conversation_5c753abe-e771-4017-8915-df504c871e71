#!/usr/bin/env python3
"""
Test script for external embeddings functionality.

This script tests the key components of the external embeddings feature:
1. Dataset loading with external embeddings
2. Model forward pass with external embeddings
3. Loss function computation with embedding alignment

Usage:
    python tests/test_external_embeddings.py
"""

import sys
import tempfile
from pathlib import Path

import numpy as np
import torch
import torch.nn as nn

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent))

from cell_segmentation.datasets.pannuke import PanNukeDataset
from models.segmentation.cell_segmentation.cellvit import CellViT
from base_ml.base_loss import EmbeddingAlignmentLoss


def test_embedding_alignment_loss():
    """Test the EmbeddingAlignmentLoss function."""
    print("Testing EmbeddingAlignmentLoss...")
    
    batch_size = 4
    embed_dim = 512
    
    # Create dummy embeddings
    visual_embeddings = torch.randn(batch_size, embed_dim)
    external_embeddings = torch.randn(batch_size, embed_dim)
    
    # Test cosine loss
    cosine_loss = EmbeddingAlignmentLoss(loss_type="cosine")
    loss_value = cosine_loss(visual_embeddings, external_embeddings)
    assert loss_value.item() >= 0, "Cosine loss should be non-negative"
    print(f"  Cosine loss: {loss_value.item():.4f}")
    
    # Test MSE loss
    mse_loss = EmbeddingAlignmentLoss(loss_type="mse")
    loss_value = mse_loss(visual_embeddings, external_embeddings)
    assert loss_value.item() >= 0, "MSE loss should be non-negative"
    print(f"  MSE loss: {loss_value.item():.4f}")
    
    # Test L1 loss
    l1_loss = EmbeddingAlignmentLoss(loss_type="l1")
    loss_value = l1_loss(visual_embeddings, external_embeddings)
    assert loss_value.item() >= 0, "L1 loss should be non-negative"
    print(f"  L1 loss: {loss_value.item():.4f}")
    
    print("  ✓ EmbeddingAlignmentLoss tests passed")


def test_cellvit_with_external_embeddings():
    """Test CellViT model with external embeddings."""
    print("Testing CellViT with external embeddings...")
    
    batch_size = 2
    num_nuclei_classes = 6
    num_tissue_classes = 19
    embed_dim = 384
    external_embedding_dim = 512
    image_size = 256
    
    # Create model with external embeddings
    model = CellViT(
        num_nuclei_classes=num_nuclei_classes,
        num_tissue_classes=num_tissue_classes,
        embed_dim=embed_dim,
        input_channels=3,
        depth=12,
        num_heads=6,
        extract_layers=[3, 6, 9, 12],
        external_embedding_dim=external_embedding_dim,
        use_external_embeddings=True
    )
    
    # Create dummy input
    images = torch.randn(batch_size, 3, image_size, image_size)
    external_embeddings = torch.randn(batch_size, external_embedding_dim)
    
    # Test forward pass with external embeddings
    model.eval()
    with torch.no_grad():
        outputs = model(images, external_embeddings=external_embeddings)
    
    # Check outputs
    assert "tissue_types" in outputs, "Missing tissue_types output"
    assert "nuclei_binary_map" in outputs, "Missing nuclei_binary_map output"
    assert "hv_map" in outputs, "Missing hv_map output"
    assert "nuclei_type_map" in outputs, "Missing nuclei_type_map output"
    
    # Check output shapes
    assert outputs["tissue_types"].shape == (batch_size, num_tissue_classes), \
        f"Wrong tissue_types shape: {outputs['tissue_types'].shape}"
    assert outputs["nuclei_binary_map"].shape == (batch_size, 2, image_size, image_size), \
        f"Wrong nuclei_binary_map shape: {outputs['nuclei_binary_map'].shape}"
    
    print(f"  ✓ Forward pass successful")
    print(f"  ✓ Output shapes correct")
    
    # Test forward pass without external embeddings
    outputs_no_ext = model(images, external_embeddings=None)
    assert "tissue_types" in outputs_no_ext, "Missing tissue_types output without external embeddings"
    print(f"  ✓ Forward pass without external embeddings successful")
    
    # Test model without external embeddings support
    model_no_ext = CellViT(
        num_nuclei_classes=num_nuclei_classes,
        num_tissue_classes=num_tissue_classes,
        embed_dim=embed_dim,
        input_channels=3,
        depth=12,
        num_heads=6,
        extract_layers=[3, 6, 9, 12],
        use_external_embeddings=False
    )
    
    model_no_ext.eval()
    with torch.no_grad():
        outputs_baseline = model_no_ext(images)
    
    assert "tissue_types" in outputs_baseline, "Missing tissue_types output in baseline model"
    print(f"  ✓ Baseline model (no external embeddings) works correctly")
    
    print("  ✓ CellViT external embeddings tests passed")


def test_dataset_with_external_embeddings():
    """Test dataset loading with external embeddings."""
    print("Testing dataset with external embeddings...")
    
    # Create temporary embeddings file
    with tempfile.NamedTemporaryFile(suffix=".npz", delete=False) as f:
        embeddings_path = f.name
    
    # Create dummy embeddings
    embeddings = {
        "image1.png": np.random.randn(512),
        "image2.png": np.random.randn(512),
        "image3.png": np.random.randn(512),
    }
    np.savez(embeddings_path, **embeddings)
    
    try:
        # Test loading embeddings
        from cell_segmentation.datasets.pannuke import PanNukeDataset
        
        # Create a mock dataset instance to test embedding loading
        class MockDataset(PanNukeDataset):
            def __init__(self, external_embeddings_path, embedding_dim):
                self.external_embeddings_path = external_embeddings_path
                self.embedding_dim = embedding_dim
                self.external_embeddings = None
                self.img_names = list(embeddings.keys())
                
                if self.external_embeddings_path is not None:
                    self.load_external_embeddings()
        
        dataset = MockDataset(embeddings_path, 512)
        
        # Test embedding retrieval
        for i, img_name in enumerate(dataset.img_names):
            embedding = dataset.get_external_embedding(i)
            assert embedding.shape == (512,), f"Wrong embedding shape: {embedding.shape}"
            assert torch.is_tensor(embedding), "Embedding should be a tensor"
        
        print(f"  ✓ Embeddings loaded successfully")
        print(f"  ✓ Embedding shapes correct")
        
        # Test missing embedding handling
        dataset.img_names.append("missing_image.png")
        missing_embedding = dataset.get_external_embedding(len(dataset.img_names) - 1)
        assert torch.allclose(missing_embedding, torch.zeros(512)), \
            "Missing embedding should be zero tensor"
        print(f"  ✓ Missing embedding handling works correctly")
        
    finally:
        # Clean up temporary file
        Path(embeddings_path).unlink()
    
    print("  ✓ Dataset external embeddings tests passed")


def test_integration():
    """Test integration of all components."""
    print("Testing integration...")
    
    batch_size = 2
    embed_dim = 512
    
    # Create dummy data
    images = torch.randn(batch_size, 3, 256, 256)
    external_embeddings = torch.randn(batch_size, embed_dim)
    
    # Create model
    model = CellViT(
        num_nuclei_classes=6,
        num_tissue_classes=19,
        embed_dim=384,
        input_channels=3,
        depth=12,
        num_heads=6,
        extract_layers=[3, 6, 9, 12],
        external_embedding_dim=embed_dim,
        use_external_embeddings=True
    )
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        outputs = model(images, external_embeddings=external_embeddings)
    
    # Test loss computation
    loss_fn = EmbeddingAlignmentLoss(loss_type="cosine")
    
    # Extract visual embeddings (using class token from last layer)
    # This is a simplified version - in practice, you'd extract from the fusion layer
    visual_embeddings = torch.randn(batch_size, embed_dim)  # Placeholder
    
    alignment_loss = loss_fn(visual_embeddings, external_embeddings)
    assert alignment_loss.item() >= 0, "Alignment loss should be non-negative"
    
    print(f"  ✓ Integration test successful")
    print(f"  ✓ Alignment loss: {alignment_loss.item():.4f}")


def main():
    """Run all tests."""
    print("Running external embeddings tests...\n")
    
    try:
        test_embedding_alignment_loss()
        print()
        
        test_cellvit_with_external_embeddings()
        print()
        
        test_dataset_with_external_embeddings()
        print()
        
        test_integration()
        print()
        
        print("🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
